import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LoginComponent } from './login/login.component';
import { HomeComponent } from './home/<USER>';
import { SharedModule } from '../shared/shared.module';
import { MainRouting } from './main.routing';
import { DashboardComponent } from './dashboard/dashboard.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';


@NgModule({
  declarations: [
    LoginComponent,
    HomeComponent,
    DashboardComponent,
  ],
  exports: [
    LoginComponent,
    HomeComponent
  ],
  imports: [
    CommonModule,
    SharedModule,
    MainRouting,
    FormsModule,
    ReactiveFormsModule
  ]
})
export class MainModule {}
