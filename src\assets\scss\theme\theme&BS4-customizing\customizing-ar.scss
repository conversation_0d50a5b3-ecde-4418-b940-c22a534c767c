@use '@angular/material' as mat;
@use "../Helper/palette-helper" as palette-helper;
:root {
    --font-family-ar: 'Questv1';
}


/********** material theming customization ***************/

$custom-typography: mat.define-legacy-typography-config( $font-family: var(--font-family-ar), $headline: mat.define-typography-level(1.15rem, 32px, 600), $title: mat.define-typography-level(0.9rem, 32px, 500), $body-2: mat.define-typography-level(0.8rem, 24px, 500), $body-1: mat.define-typography-level(0.9rem, 20px, 400), $caption: mat.define-typography-level(0.95rem, 20px, 400), $button: mat.define-typography-level(1rem, 14px, 500), $input: mat.define-typography-level(0.9rem, 1.125, 400), );
// TODO(v15): As of v15 mat.legacy-core no longer includes default typography styles.
//  The following line adds:
//    1. Default typography styles for all components
//    2. Styles for typography hierarchy classes (e.g. .mat-headline-1)
//  If you specify typography styles for the components you use elsewhere, you should delete this line.
//  If you don't need the default component typographies but still want the hierarchy styles,
//  you can delete this line and instead use:
//    `@include mat.legacy-typography-hierarchy($custom-typography);`
@include mat.all-legacy-component-typographies($custom-typography);
@include mat.legacy-core();
@import 'src/assets/scss/theme/theme_colors';
@import 'colors/mat-colors-customize';
$my-app-theme:mat.define-light-theme(( color: ( primary: $my-app-primary, accent: $my-app-accent, warn:$my-app-warn, ), ));
$my-app-theme:palette-helper.modify-foreground-background($my-app-theme, $mat-light-theme-foreground, $mat-light-theme-background);
@include mat.all-legacy-component-themes($my-app-theme);
.mat-paginator,
.mat-paginator-page-size .mat-select-trigger {
    font-size: 0.6rem;
}


/**********************************************************/


/********** bootstrap customization ***************/


/* import only the necessary Bootstrap files */

@import '../../../../../node_modules/bootstrap/scss/functions';
@import '../../../../../node_modules/bootstrap/scss/variables';
@import 'colors/bs4-customizing';
$font-family-base: var(--font-family-ar);
$headings-font-family: var(--font-family-ar);
$font-size-base: 0.8rem;
$font-weight-base: 0.8rem;
$h1-font-size: 1.15rem;
$paragraph-margin-bottom: 0.3rem;
$label-margin-bottom: 0.4rem;
$btn-font-size: 1rem;
$btn-line-height: initial;
$form-group-margin-bottom: 0.2rem;
$link-hover-decoration: none;

/* finally, import Bootstrap to set the changes! */

@import '../../../../../node_modules/bootstrap/scss/bootstrap';

/**********************************************/
