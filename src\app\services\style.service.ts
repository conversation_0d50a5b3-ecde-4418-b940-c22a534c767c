import {Inject, Injectable} from '@angular/core';
import {Subject} from 'rxjs';
import {DOCUMENT} from '@angular/common';

@Injectable({
  providedIn: 'root'
})
export class StyleService {

  languagesDir: { [key: string]: string } = {
    en: 'ltr',
    ar: 'rtl'
  };

  selectedLanguage: string = 'ar';


  languageChanged: Subject<string> = new Subject<string>();

  constructor(@Inject(DOCUMENT) private doc: { [key: string]: any }) {
    this.selectedLanguage = localStorage.getItem('selectedLanguage') || 'ar';
    localStorage.setItem('selectedLanguage', this.selectedLanguage);

    this.changeStyle(this.languagesDir[this.selectedLanguage]);

    this.languageChanged.subscribe((res: string) => {
      if (localStorage.getItem('selectedLanguage') !== res) {
        localStorage.setItem('selectedLanguage', res);
        location.reload();
      }
    });
  }

  changeStyle(selectedStyle: string) {
    this.doc.dir = selectedStyle;
    this.doc.lang = this.selectedLanguage;
    let existingLink = this.doc.getElementById('langCss') as HTMLLinkElement;
    let bundleName = this.doc.lang === 'ar' ? 'arabicStyle.css' : 'englishStyle.css';
    if (existingLink) {
      existingLink.href = bundleName;
    } else {
      let linkEl = document.createElement('link');
      linkEl.setAttribute('media', 'screen,print');
      linkEl.setAttribute('type', 'text/css');
      linkEl.setAttribute('id', 'langCss');
      linkEl.setAttribute('rel', 'stylesheet');
      linkEl.setAttribute('href', bundleName);
      document.head.appendChild(linkEl);
    }
  }
}
