import {NgModule} from "@angular/core";
import {IncomingTransfersMainComponent} from './incoming-transfers-main/incoming-transfers-main.component';
import {SharedModule} from "../shared/shared.module";
import {IncomingTransfersRouting} from "./IncomingTransfers.routing";
import {IncomingTransfersModelComponent} from "./IncomingTransferComponents/incoming-transfers-model.component";
import {
  IncomingTransfersModelIndexComponent
} from "./IncomingTransferComponents/incoming-transfers-model-index/incoming-transfers-model-index.component";
import {
  IncomingTransfersModelViewComponent
} from "./IncomingTransferComponents/incoming-transfers-model-view/incoming-transfers-model-view.component";
import {IncomingTransfersModelService} from "./Services/incoming-transfers-model.service";
import {FormsModule} from "@angular/forms";
import {
  NewIncomingTransfersIndexComponent
} from "./NewIncomingTransfersComponents/new-incoming-transfers-index/new-incoming-transfers-index.component";
import {NewIncomingTransfersComponent} from "./NewIncomingTransfersComponents/new-incoming-transfers.component";
import {NewIncomingTransfersService} from "./Services/new-incoming-transfers.service";
import { NewIncomingTransfersViewComponent } from './NewIncomingTransfersComponents/new-incoming-transfers-view/new-incoming-transfers-view.component';
import { ReverseIncomingTransferComponent } from './NewIncomingTransfersComponents/reverse-incoming-transfer/reverse-incoming-transfer.component';


@NgModule({
  imports: [
    SharedModule,
    IncomingTransfersRouting,
    FormsModule,
  ],
  exports: [],
  declarations: [
    IncomingTransfersMainComponent,
    IncomingTransfersModelComponent,
    IncomingTransfersModelIndexComponent,
    IncomingTransfersModelViewComponent,
    NewIncomingTransfersIndexComponent,
    NewIncomingTransfersComponent,
    NewIncomingTransfersViewComponent,
    ReverseIncomingTransferComponent
  ],
  providers: [
    IncomingTransfersModelService,
    NewIncomingTransfersService
  ]
})

export class IncomingTransfersModule {

}
