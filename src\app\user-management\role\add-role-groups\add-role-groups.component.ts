import {Component, Inject, OnInit} from '@angular/core';
import {AlertService, FormDef, Helper} from '@ttwr-framework/ngx-main-visuals';
import {MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA, MatLegacyDialogRef as MatDialogRef} from '@angular/material/legacy-dialog';
import {AuhtItemService} from '../../services/auht-item.service';
import {AuthItem} from '../../models/auth-item.model';

@Component({
  selector: 'app-add-role-groups',
  templateUrl: './add-role-groups.component.html',
  styleUrls: ['./add-role-groups.component.scss'],
})
export class AddRoleGroupsComponent implements OnInit {
  dialogTitle='Add to groups';
  public config: FormDef = {
    titleHidden:true,
    fields: [{key: 'GroupId', parameterRequestName: 'Groups'}],
    fieldsDef: AuthItem.getModelDef(),
    obj: {},
    actionFields: [
      {
        label: 'Submit',
        delegateFunction: (obj) =>
          this.auhtItemService.addGroups(this.config.obj?.['Id'], obj).subscribe(() => {
            this.alertService.success('Success Update');
            this.dialogRef.close(true);
          }),
      },
      {
        label: 'Cancel',
        cssClass: 'btn-danger',
        causeValidate: false,
        delegateFunction: () => this.dialogRef.close(),
      },
    ],
  };

  constructor(
    public dialogRef: MatDialogRef<AddRoleGroupsComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private auhtItemService: AuhtItemService,
    private alertService: AlertService,
  ) {
  }

  ngOnInit() {
    this.config.obj = Helper.deepCopy(this.data['obj']);
  }
}
