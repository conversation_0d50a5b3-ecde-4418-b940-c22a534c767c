<div mat-dialog-title>
  <div align="end" mat-dialog-close>
    <svg-icon src="assets/ic_remove.svg"></svg-icon>
  </div>
  <h1>{{dialogTitle | i18n}}</h1>
</div>
<div mat-dialog-content>
  <ttwr-form *ngIf="doneLoading" [config]="config">
    <form [formGroup]="form">
      <div class="container form-group ng-star-inserted col-md-8">
        <label>{{'WorkingStartHour'|i18n}}</label>
        <mat-form-field class="row" appearance="outline">
          <input matInput type="time" formControlName="startWorkingHour" [(ngModel)]="startWorkingHour">
          <mat-error
            *ngIf="form.get('startWorkingHour')?.invalid && form.get('startWorkingHour')?.touched">
            {{ 'This field is required' | i18n }}
          </mat-error>
        </mat-form-field>
      </div>

      <div class="container form-group ng-star-inserted col-md-8">
        <label>{{'WorkingEndHour'|i18n}}</label>
        <mat-form-field class="row" appearance="outline">
          <input matInput type="time" formControlName="endWorkingHour" [(ngModel)]="EndWorkingHour">
          <mat-error *ngIf="form.hasError('invalidTimeRange') && (form.get('endWorkingHour')?.touched)">
            {{ 'Start time must be earlier than end time' | i18n }}
          </mat-error>
          <mat-error *ngIf="form.get('endWorkingHour')?.invalid && form.get('endWorkingHour')?.touched">
            {{ 'This field is required' | i18n }}
          </mat-error>
        </mat-form-field>
      </div>
    </form>
  </ttwr-form>
</div>
