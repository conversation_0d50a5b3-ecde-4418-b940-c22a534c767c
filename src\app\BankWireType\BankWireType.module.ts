import {NgModule} from "@angular/core";
import {BankWireTypeRouting} from "./BankWireType.routing";
import {BankWireTypeMainComponent} from './bank-wire-type-main/bank-wire-type-main.component';
import {BankWireTypeService} from "./Services/bank-wire-type.service";
import {
  BankWireTypeCreateComponent
} from "./BankWireTypeComponents/bank-wire-type-create/bank-wire-type-create.component";
import {BankWireTypeIndexComponent} from "./BankWireTypeComponents/bank-wire-type-index/bank-wire-type-index.component";
import {
  BankWireTypeUpdateComponent
} from "./BankWireTypeComponents/bank-wire-type-update/bank-wire-type-update.component";
import {BankWireTypeViewComponent} from "./BankWireTypeComponents/bank-wire-type-view/bank-wire-type-view.component";
import {BankWireTypeComponent} from "./BankWireTypeComponents/bank-wire-type.component";
import {SharedModule} from "../shared/shared.module";
import {LOVBankWireTypeListComponent} from "./LOVBankWireTypeList/LOVBankWireTypeList.component";


@NgModule({
  imports: [
    BankWireTypeRouting,
    SharedModule,
  ],
  exports: [],
  providers: [
    BankWireTypeService
  ],
  declarations: [
    BankWireTypeMainComponent,
    BankWireTypeCreateComponent,
    BankWireTypeIndexComponent,
    BankWireTypeUpdateComponent,
    BankWireTypeViewComponent,
    BankWireTypeComponent,
    LOVBankWireTypeListComponent
  ]
})
export class BankWireTypeModule {

}
