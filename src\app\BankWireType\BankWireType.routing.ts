import {NgModule} from "@angular/core";
import {RouterModule, Routes} from "@angular/router";
import {BankWireTypeMainComponent} from "./bank-wire-type-main/bank-wire-type-main.component";
import {BankWireTypeComponent} from "./BankWireTypeComponents/bank-wire-type.component";
import {BankWireTypeIndexComponent} from "./BankWireTypeComponents/bank-wire-type-index/bank-wire-type-index.component";
import {
  BankWireTypeCreateComponent
} from "./BankWireTypeComponents/bank-wire-type-create/bank-wire-type-create.component";
import {BankWireTypeViewComponent} from "./BankWireTypeComponents/bank-wire-type-view/bank-wire-type-view.component";
import {InstitutionResolver} from "../services/InstitutionResolver";
import {
  BankWireTypeUpdateComponent
} from "./BankWireTypeComponents/bank-wire-type-update/bank-wire-type-update.component";

const routes: Routes = [
  {
    path: 'BankWireType', component: BankWireTypeMainComponent, children: [
      {
        path: 'bank-wire-type',
        component: BankWireTypeComponent,
        data: {perms: {CLEAR_NAME: 'bank-wire-type'}},
        children:
          [
            {
              path: 'index', component: BankWireTypeIndexComponent,
              data: {
                perms: {CLEAR_NAME: 'عرض'},
                permsChildren: [
                  {NAME: '/api/BankWireType/delete', CLEAR_NAME: 'خدمة الحذف'},]
              }
            },
            {
              path: 'create', component: BankWireTypeCreateComponent,
              data: {
                perms: {CLEAR_NAME: 'انشاء'},
                permsChildren: [{NAME: '/api/BankWireType/create', CLEAR_NAME: 'خدمة الانشاء'}]
              }
            },
            {
              path: 'view/:id', component: BankWireTypeViewComponent,
              resolve: {
                obj: InstitutionResolver,
              },
              data: {
                service_key: 'bank-wire-type',
                perms: {CLEAR_NAME: 'استعراض التفاصيل'},
                permsChildren: [{NAME: '/api/BankWireType/view', CLEAR_NAME: 'خدمة استعراض التفاصيل'}]
              }
            },
            {
              path: 'update/:id', component: BankWireTypeUpdateComponent,
              resolve: {
                obj: InstitutionResolver,
              },
              data: {
                service_key: 'bank-wire-type',
                perms: {CLEAR_NAME: 'تعديل'},
                permsChildren: [{NAME: '/api/BankWireType/update', CLEAR_NAME: 'خدمة التعديل'}]
              }
            },
          ],
      },

    ]
  }
]

@NgModule({
  imports: [
    RouterModule.forChild(routes)
  ],
  exports: [
    RouterModule
  ]
})
export class BankWireTypeRouting {

}
