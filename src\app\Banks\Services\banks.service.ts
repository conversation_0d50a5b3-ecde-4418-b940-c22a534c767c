import {Injectable} from '@angular/core';
import {SYGSBaseService} from "../../services/SYGSBaseService";
import {Filter, Model, Sort} from "@ttwr-framework/ngx-main-visuals";
import {Observable} from "rxjs";
import {HttpParams} from "@angular/common/http";

@Injectable()
export class BanksService extends SYGSBaseService {

  protected baseName = 'Bank';
  accountNumber!: string;
  name!: string;
  address!: string;
  mobile!: string;
  bankCode!: string;


  index(
    filter: Filter[] = [],
    sort: Sort[] = [],
    pageNumber = 0,
    pageSize = 5
  ): Observable<Model[]> {
    const params = this.getGridParams(filter, sort, pageNumber, pageSize);
    return this.http.get<Model[]>(`/api/` + this.baseName + `/GetBanks`, {
      params: params,
    });
  }

  bankBranches(
    filter: Filter[] = [],
    sort: Sort[] = [],
    pageNumber = 0,
    pageSize = 5,
    bankIdentityCode: string
  ): Observable<Model[]> {
    const params = this.getGridParams(filter, sort, pageNumber, pageSize);
    return this.http.get<Model[]>(`/api/` + this.baseName + `/GetBankBranches/${bankIdentityCode}`, {
      params: params,
    });
  }

  getBankCustomer(
    pageNumber = 0,
    pageSize = 5,
    customerNumber: string
  ): Observable<Model[]> {
    let params = new HttpParams()
      .set('page', (pageNumber + 1).toString())
      .set('pageSize', pageSize.toString())
      .set('customerNumber', customerNumber)
    return this.http.get<Model[]>(`/api/` + this.baseName + `/GetBankCustomers`, {
      params: params,
    });
  }
}
