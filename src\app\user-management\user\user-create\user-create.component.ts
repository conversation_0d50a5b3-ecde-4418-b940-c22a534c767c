import {Component} from '@angular/core';
import {User} from '../../models/user.model';
import {UserService} from '../../services/user.service';
import {MatLegacyDialog as MatDialog, MatLegacyDialogRef as MatDialogRef} from '@angular/material/legacy-dialog';
import {AlertService, FormDef} from '@ttwr-framework/ngx-main-visuals';
import {AbstractControl, UntypedFormBuilder, UntypedFormGroup, ValidationErrors, Validators} from "@angular/forms";

@Component({
  selector: 'app-user-create',
  templateUrl: './user-create.component.html',
  styleUrls: ['./user-create.component.scss'],
})
export class UserCreateComponent {
  form: UntypedFormGroup;
  startWorkingHour: any;
  EndWorkingHour: any;

  dialogTitle = 'User Create';
  public config: FormDef = {
    titleHidden: true,
    fields: [
      {key: 'username'},
      {key: 'employeeId'},
      {key: 'isInactive'},
      {key: 'branchIdentificationCode'},
      // {key: 'PasswordHash'},
    ],
    fieldsDef: User.getModelDef(),
    obj: {},
    actionFields: [
      {
        label: 'Submit',
        delegateFunction: (obj) => {
          if (this.form.invalid) {
            if (this.form.hasError('invalidTimeRange')) {
              this.alertService.error('Start time must be earlier than end time')
            }
            this.form.markAllAsTouched()
            return
          }
          obj.workingStartHour = this.startWorkingHour
          obj.workingEndHour = this.EndWorkingHour
          obj.isInactive = !obj.isInactive;
          this.userService.create(obj).subscribe((res: any) => {
            if (res) {
              if (res.code == 0) {
                this.alertService.success('Success Create');
                this.dialogRef.close(true);
              } else {
                this.alertService.error(res.message);
                this.dialogRef.close(true);
              }
            }

          }, (err) => {
            this.alertService.error(err)
          })
        }
      },
      {
        label: 'Cancel',
        cssClass: 'btn-danger',
        causeValidate: false,
        delegateFunction: () => this.dialogRef.close(),
      },
    ],
  };

  constructor(
    private fb: UntypedFormBuilder,
    public dialogRef: MatDialogRef<UserCreateComponent>,
    private userService: UserService,
    private dialog: MatDialog,
    private alertService: AlertService,
  ) {
    this.form = this.fb.group({
        startWorkingHour: ['', Validators.required],
        endWorkingHour: ['', Validators.required],
      },
      {validators: startEndHourValidator}
    );
  }

}

export function startEndHourValidator(control: AbstractControl): ValidationErrors | null {
  const start = control.get('startWorkingHour')?.value;
  const end = control.get('endWorkingHour')?.value;

  if (start && end && start >= end) {
    return {'invalidTimeRange': true};
  }
  return null;
}
