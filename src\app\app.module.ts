import {APP_INITIALIZER, NgModule} from '@angular/core';
import {BrowserModule} from '@angular/platform-browser';

import {AppRoutingModule} from './app-routing.module';
import {AppComponent} from './app.component';
import {ConfigService} from "./services/config.service";
import {Observable, ObservableInput, of} from "rxjs";
import {HttpClient} from "@angular/common/http";
import {catchError, map} from "rxjs/operators";
import {environment} from "../environments/environment";
import * as _ from "lodash";
import {MainModule} from "./main/main.module";
import {httpInterceptorProviders} from "./interceptors";
import {UserManagementModule} from "./user-management/user-management.module";
import {SharedModule} from "./shared/shared.module";
import {IncomingTransfersModule} from "./IncomingTransfers/IncomingTransfers.module";
import {OutgoingTransferModule} from "./OutgoingTransfers/OutgoingTransfer.module";
import {OutgoingBankWireModule} from "./OutgoingBankWire/OutgoingBankWire.module";
import {BanksModule} from "./Banks/Banks.module";
import {BankWireTypeModule} from "./BankWireType/BankWireType.module";
import { MAT_FORM_FIELD_DEFAULT_OPTIONS } from '@angular/material/form-field';
import {SystemSettingModule} from "./SystemSettings/SystemSetting.module";


function load(http: HttpClient, config: ConfigService): (() => Promise<boolean>) {
  return (): Promise<boolean> => {
    return new Promise<boolean>((resolve: (a: boolean) => void): void => {
      http.get('./assets/config/config.json')
        .pipe(
          map((x: any) => {
            config.configuration = _.assign({}, config.configuration, x);
            let envKind = environment.production ? 'prod' : 'dev';
            http.get('./assets/config/config.' + envKind + '.json')
              .pipe(
                map((x: any) => {

                  config.configuration = _.assign({}, config.configuration, x);
                  resolve(true);
                }),
                catchError((x: { status: number }, caught: Observable<void>): ObservableInput<{}> => {
                  resolve(false);
                  console.log('Error loading config')
                  console.log(x)
                  console.log(caught)
                  return of({});
                })
              ).subscribe()
          }),
          catchError((x: { status: number }, caught: Observable<void>): ObservableInput<{}> => {
            resolve(false);
            console.log('Error loading config')
            console.log(x)
            console.log(caught)
            return of({});
          })
        ).subscribe();
    });
  };
}

@NgModule({
  declarations: [
    AppComponent,
  ],
  imports: [
    BrowserModule,
    AppRoutingModule,
    SharedModule,
    MainModule,
    UserManagementModule,
    IncomingTransfersModule,
    OutgoingTransferModule,
    OutgoingBankWireModule,
    BanksModule,
    BankWireTypeModule,
    SystemSettingModule
  ],
  providers: [
    {
      provide: APP_INITIALIZER,
      useFactory: load,
      deps: [HttpClient, ConfigService],
      multi: true
    },
    httpInterceptorProviders,
  ],
  exports: [],
  bootstrap: [AppComponent]
})
export class AppModule {
}
