import {Component, OnInit} from '@angular/core';
import {AlertService, FormDef, LanguageService} from "@ttwr-framework/ngx-main-visuals";
import {Validators} from "@angular/forms";
import {OutgoingBankWireModel} from "../../Models/OutgoingBankWire.model";
import {OutgoingBankWireService} from "../../Services/outgoing-bank-wire.service";
import {BanksService} from "../../../Banks/Services/banks.service";
import {ActivatedRoute, Router} from "@angular/router";
import {ConfigService} from "../../../services/config.service";
import {formatDate} from "@angular/common";

@Component({
  selector: 'app-outgoing-bank-wire-create',
  templateUrl: './outgoing-bank-wire-create.component.html',
  styleUrls: ['./outgoing-bank-wire-create.component.sass']
})
export class OutgoingBankWireCreateComponent implements OnInit {

  public config: FormDef = {
    title: 'OutgoingBankWireCreateComponent',
    fields: [
      {key: 'dtAccount', label: 'PayingBankAccountNumber'},
      {key: 'ctAccount', label: 'BeneficiaryBankAccountNumber', validators: []},
      {key: 'ctName', label: 'BeneficiaryBankName', isHidden: true},
      {key: 'ctAdd', isHidden: true},
      {key: 'ctPhoneNumber', isHidden: true},
      {
        key: 'ttc'
      },
      {key: 'payBankBIC'},
      {
        key: 'benefitBankBIC', onChange: (val) => {
          if (!val) {
            this.config.form?.get('benefitBranchBIC')?.setValue(null)
            let branch = this.config.fields.find(k => k.key == 'benefitBranchBIC')
            branch!.defaultValue = null;
            branch!.uiObject.searchValue = "";
          }
        }, label: 'BeneficiaryBankName'
      },
      {key: 'benefitBranchBIC'},
      {key: 'insCurrency', isHidden: true},
      {
        key: 'insAmount', validators: [
          {
            name: 'required',
            validator: Validators.required,
            message: 'this field is required'
          },
          {
            name: 'min',
            validator: Validators.min(this.configService.configuration.MinimumInsAmount),
            message: this.languageService.getLang('this field must be bigger than ') + this.configService.configuration.MinimumInsAmount
          }
        ]
      },
      {key: 'dtlChg', readonly: true, defaultValue: 'SHA'},
      {key: 'priority'},
      {key: 'valueDate'},
      {key: 'sendReference'},
      {key: 'relatedReference'},
      {key: 'rmtInformation', label: 'notes'},
      {key: 'sendToReceiveInformation'},
    ],
    fieldsDef: OutgoingBankWireModel.getModelDef(),
    actionFields: [
      {
        label: 'Submit',
        cssClass: 'btn-primary',
        delegateFunction: (obj) => {
          obj.insCurrency = "SYP"
          obj.valueDate = formatDate(new Date(obj.valueDate), 'yyyy-MM-dd', 'en')
          this.outgoingBankWireModelService.create(obj).subscribe((res: any) => {
            if (res) {
              if (res.code == 0) {
                this.alertService.success('Success Create');
                this.router.navigate(['view/' + res.data.outgoingTransferId], {relativeTo: this.activatedRoute.parent});
              } else {
                this.alertService.error(res.message)
              }
            }
          }, error => {
            this.alertService.error(error)
          })
        }
      },
      {
        label: 'Cancel',
        cssClass: 'btn-danger',
        causeValidate: false,
        delegateFunction: () => this.router.navigate(['index/'], {relativeTo: this.activatedRoute.parent}),
      }
    ]
  };

  constructor(private outgoingBankWireModelService: OutgoingBankWireService,
              private bankService: BanksService,
              private alertService: AlertService, private router: Router, private activatedRoute: ActivatedRoute,
              public configService: ConfigService,
              private languageService: LanguageService) {
  }

  ngOnInit(): void {
  }

}
