import {Component, Inject, OnInit} from "@angular/core";
import {AlertService, GridDef, Model, ModelLovGridComponent, ViewDef} from "@ttwr-framework/ngx-main-visuals";
import {BankCustomerAccountModel} from "../Models/BankCustomerAccounts.model";
import {BanksService} from "../Services/banks.service";
import {Observable, throwError} from "rxjs";
import {MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA, MatLegacyDialogRef as MatDialogRef} from "@angular/material/legacy-dialog";
import {catchError, map} from "rxjs/operators";
import {BankCustomerInformationModel} from "../Models/BankCustomerInformation.model";

@Component({
  template: `
    <ttwr-lov-grid [config]="config" [selectName]="selectName" [selectValue]="selectValue"></ttwr-lov-grid>
    <hr/>
    <ttwr-view [config]="viewConfig"></ttwr-view>
  `

})
export class LOVBankCustomerComponent extends ModelLovGridComponent implements OnInit {
  public config: GridDef = {
    title: 'BankCustomer',
    fields: [
      {
        key: 'accountNo'
      },
      {
        key: 'accountType'
      },
      {
        key: 'balance'
      },
      {
        key: 'available'
      },
    ],
    actionFields: [],
    actions: [],
    fieldsDef: BankCustomerAccountModel.getModelDef(),
    dataFunction: (filter, sort, pageIndex, pageSize): Observable<Model[]> => {
      return this.bankService.getBankCustomer(pageIndex, pageSize, this.data.search_term).pipe(map((res: any) => {
        this.viewConfig.obj = res.bankCustomerInformation
        this.viewConfig.refreshSubject?.next(true)
        return res.accounts
      }),catchError(error => {
        this.alertService.error(error.error.message);
        return throwError(() => error);
      }));
    },
    disableToggleAll: true,
    disableFiltrableColumns: true,
    export: {
      pdf: {
        enable: false
      },
      excel: {
        enable: false
      }
    }

  }
  public viewConfig: ViewDef = {
    fields: [
      {
        key: 'name', cssClass: {field: 'col-md-4'}
      },
      {
        key: 'firstName', cssClass: {field: 'col-md-4'}
      },
      {
        key: 'lastName', cssClass: {field: 'col-md-4'}
      },
      {
        key: 'middleName', cssClass: {field: 'col-md-4'}
      },
      {
        key: 'address', cssClass: {field: 'col-md-4'}
      },
      {
        key: 'mobile', cssClass: {field: 'col-md-4'}
      }
    ],
    actionFields: [],
    fieldsDef: BankCustomerInformationModel.getModelDef()
  }


  constructor(private bankService: BanksService,
              public dialogRef: MatDialogRef<LOVBankCustomerComponent>,
              private alertService: AlertService,
              @Inject(MAT_DIALOG_DATA) public data: any) {
    super()
  }

  ngOnInit() {
    this.selectName = 'customerAccountNumber';
    this.selectValue = 'customerAccountNumber';
    setTimeout(() => {
      if (this.selectValue && this.selectName) {
        this.config.rowSelectedFunction = (row) => {
          this.bankService.accountNumber = row.accountNo;
          this.bankService.name = this.viewConfig.obj.name;
          this.bankService.address = this.viewConfig.obj.address;
          this.bankService.mobile = this.viewConfig.obj.mobile
          this.dialogRef.close({status: 1, name: row[this.selectName], id: row[this.selectValue], objs: [row]});
        }
      }
    }, 5)
  }
}
