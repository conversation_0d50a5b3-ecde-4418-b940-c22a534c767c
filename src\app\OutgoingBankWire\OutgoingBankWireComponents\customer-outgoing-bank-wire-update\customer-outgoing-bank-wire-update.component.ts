import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { OutgoingBankWireModel } from '../../Models/OutgoingBankWire.model';
import { OutgoingBankWireService } from '../../Services/outgoing-bank-wire.service';
import { AlertService, Helper, FormDef, LanguageService } from '@ttwr-framework/ngx-main-visuals';
import { BanksService } from '../../../Banks/Services/banks.service';
import { Validators } from '@angular/forms';
import { ConfigService } from '../../../services/config.service';
import { formatDate } from '@angular/common';

@Component({
  selector: 'app-customer-outgoing-bank-wire-model-update',
  templateUrl: './customer-outgoing-bank-wire-update.component.html',
  styleUrls: ['./customer-outgoing-bank-wire-update.component.scss']
})
export class CustomerOutgoingBankWireUpdateComponent implements OnInit {
  ttc: string = '';
  benefitBankBIC: string = '';
  payBranchBIC: string = '';
  benefitBranchBIC: string = '';
  payPartBIC: string = '';
  benefitPartBIC: string = '';
  regex = /^[A-Z0-9]*$/;

  public config: FormDef = {
    title: 'customerOutgoingBankWireModel Update',
    fields: [
      {
        key: 'customerNumber', onChange: () => {
          this.config.form?.get('dtAccount')?.setValue(this.bankService.accountNumber);
          this.config.form?.get('dtName')?.setValue(this.bankService.name);
          this.config.form?.get('dtAdd')?.setValue(this.bankService.address);
          this.config.form?.get('dtPhoneNumber')?.setValue(this.bankService.mobile);
        }
      },
      { key: 'dtAccount', readonly: true },
      { key: 'dtName' },
      { key: 'dtMotherName' },
      { key: 'dtNationalNumber' },
      { key: 'dtPhoneNumber' },
      { key: 'dtAdd' },
      { key: 'ctAccount' },
      { key: 'ctName' },
      { key: 'ctPhoneNumber' },
      { key: 'ctAdd' },
      {
        key: 'ttc', onChange: (val) => {
          let checkNumberField = this.config.fields.find(k => k.key == 'checkNumber')
          let endrNamesField = this.config.fields.find(k => k.key == 'endrNames')
          let endrCountsField = this.config.fields.find(k => k.key == 'endrCount')
          if (val == 'CQT') {
            checkNumberField!.validators?.push({
              name: 'required',
              validator: Validators.required,
              message: 'this field is required'
            })
            endrNamesField!.validators?.push({
              name: 'required',
              validator: Validators.required,
              message: 'this field is required'
            })
            endrCountsField!.validators?.push({
              name: 'required',
              validator: Validators.required,
              message: 'this field is required'
            })
          } else {
            checkNumberField!.validators = checkNumberField!.validators?.filter(x => x.name != 'required')
            endrNamesField!.validators = endrNamesField!.validators?.filter(x => x.name != 'required')
            endrCountsField!.validators = endrCountsField!.validators?.filter(x => x.name != 'required')
          }
        }
      },
      { key: 'benefitBankBIC' },
      { key: 'benefitBranchBIC' },
      { key: 'insCurrency' },
      {
        key: 'insAmount', validators: [
          {
            name: 'required',
            validator: Validators.required,
            message: 'this field is required'
          },
          {
            name: 'min',
            validator: Validators.min(this.configService.configuration.MinimumInsAmount),
            message: this.languageService.getLang('this field must be bigger than ') + this.configService.configuration.MinimumInsAmount
          }
        ]
      },
      { key: 'exchangeRate' },
      { key: 'dtlChg' },
      { key: 'sendChangeAmount' },
      { key: 'receiveChangeAmount' },
      { key: 'priority' },
      { key: 'valueDate' },
      { key: 'checkNumber' },
      { key: 'endrCount' },
      { key: 'endrNames' },
      { key: 'rmtInformation' },
      { key: 'sendToReceiveInformation' },
    ],
    fieldsDef: OutgoingBankWireModel.getModelDef(),
    actionFields: [
      {
        label: 'Submit',
        cssClass: 'btn-primary',
        delegateFunction: (obj) => {

          if (!this.regex.test(obj.ttc)) {
            obj.ttc = this.ttc
          }

          if (!this.regex.test(obj.benefitBankBIC)) {
            obj.benefitBankBIC = this.benefitBankBIC
          }

          if (!this.regex.test(obj.payBranchBIC)) {
            obj.payBranchBIC = this.payBranchBIC
          }

          if (!this.regex.test(obj.benefitBranchBIC)) {
            obj.benefitBranchBIC = this.benefitBranchBIC;
          }
          obj.valueDate = formatDate(new Date(obj.valueDate), 'yyyy-MM-dd', 'en')
          this.outgoingBankWireModelService.updateCustomerWithStringID(this.config.obj?.['id'], obj).subscribe((res: any) => {
            if (res) {
              if (res.code == 0) {
                this.alertService.success('Success Update');
                this.router.navigate(['view/' + this.config.obj?.['id']], { relativeTo: this.activatedRoute.parent });
              } else {
                this.alertService.error(res.message)
              }
            }
          }, error => {
            this.alertService.error(error)
          })
        }
      },
      {
        label: 'Cancel',
        cssClass: 'btn-danger',
        causeValidate: false,
        delegateFunction: () => this.router.navigate(['view/' + this.route.snapshot.paramMap.get('id')], { relativeTo: this.activatedRoute.parent }),
      }
    ]
  };

  constructor(private route: ActivatedRoute, private router: Router,
              private outgoingBankWireModelService: OutgoingBankWireService,
              private alertService: AlertService,
              private activatedRoute: ActivatedRoute,
              private bankService: BanksService,
              private configService: ConfigService,
              private languageService: LanguageService) {
  }

  ngOnInit() {
    if (this.route.snapshot.data.obj) {
      this.config.obj = Helper.deepCopy(this.route.snapshot.data.obj);

      this.ttc = this.config.obj.ttc;
      this.config.obj.ttc = this.route.snapshot.data.obj.ttcName;

      this.payBranchBIC = this.config.obj.payBranchBIC;
      this.config.obj.payBranchBIC = this.route.snapshot.data.obj.payBranchBICName;

      this.benefitBankBIC = this.config.obj.benefitBankBIC;
      this.config.obj.benefitBankBIC = this.route.snapshot.data.obj.benefitBankBICName;

      this.benefitBranchBIC = this.config.obj.benefitBranchBIC;
      this.config.obj.benefitBranchBIC = this.route.snapshot.data.obj.benefitBranchBICName;
    }

  }

}
