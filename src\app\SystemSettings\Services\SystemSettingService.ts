import {Injectable} from '@angular/core';
import {Filter, Model, Sort} from "@ttwr-framework/ngx-main-visuals";
import {Observable} from "rxjs";
import {map} from "rxjs/operators";
import {HttpParams} from "@angular/common/http";
import {SYGSBaseService} from "../../services/SYGSBaseService";
import {SystemSettingGetAllDto} from "../Types/SystemSetting.types";

@Injectable()
export class SystemSettingService extends SYGSBaseService {
  protected baseName = 'SystemSetting';

  // updateWithOtherType(id: string, obj: { value: string }) {
  //   return this.http.put(`${this.base}/${id}`, obj);
  // }
  //
  // updateWithFileType(id: string, obj: FormData) {
  //   return this.http.put(`${this.base}/File/${id}`, obj);
  // }

  getAll() {
    return this.http.get<SystemSettingGetAllDto[]>(`/api/` + this.baseName + `/GetSystemSettings`);
  }

  updateWithStringID(id: string, obj: any): Observable<Model> {
    return this.http.put<Model>(`/api/` + this.baseName + `/UpdateSystemSetting/` + id, obj);
  }
}
