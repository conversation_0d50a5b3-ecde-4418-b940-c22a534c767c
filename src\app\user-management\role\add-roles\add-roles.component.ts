import {Component, Inject, OnInit} from '@angular/core';
import {AlertService, FormDef, Helper, Model} from '@ttwr-framework/ngx-main-visuals';
import {MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA, MatLegacyDialogRef as MatDialogRef} from '@angular/material/legacy-dialog';
import {AuthItem} from '../../models/auth-item.model';
import {AuhtItemService} from '../../services/auht-item.service';

@Component({
  selector: 'app-add-roles',
  templateUrl: './add-group-roles.component.html',
  styleUrls: ['./add-group-roles.component.scss'],
})
export class AddRolesComponent implements OnInit {
  dialogTitle='Add roles';
  public config: FormDef = {
    titleHidden:true,
    fields: [{key: 'Roles', parameterRequestName: 'AuthItemChildren'}],
    fieldsDef: AuthItem.getModelDef(),
    obj: {},
    actionFields: [
      {
        label: 'Submit',
        delegateFunction: (obj) =>
          this.auhtItemService.addRoleChildren(this.config.obj?.['Id'], obj).subscribe((res: Model) => {
            if (res.status === 1) {
              this.alertService.success('Success Update');
              this.dialogRef.close(true);
            }
          }),
      },
      {
        label: 'Cancel',
        cssClass: 'btn-danger',
        causeValidate: false,
        delegateFunction: () => this.dialogRef.close(),
      },
    ],
  };

  constructor(
    public dialogRef: MatDialogRef<AddRolesComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private auhtItemService: AuhtItemService,
    private alertService: AlertService,
  ) {
  }

  ngOnInit() {
    this.config.obj = Helper.deepCopy(this.data['obj']);
  }
}
