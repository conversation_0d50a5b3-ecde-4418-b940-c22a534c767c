import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MaterialModule, NgxMainVisualsModule } from '@ttwr-framework/ngx-main-visuals';
import { MenuComponent } from './menu/menu.component';
import { RouterModule } from '@angular/router';
import { i18n } from './i18n/i18n';
import { SideMenuComponent } from '../side-menu/side-menu.component';
import { MatExpansionModule } from '@angular/material/expansion';
import { BreadcrumbComponent } from '../breadcrumb/breadcrumb.component';
import { BreadcrumbModule } from 'angular-crumbs';
import { InstitutionResolver } from '../services/InstitutionResolver';
import { FilterService } from '../services/filter.service';
import { FilterComponent } from '../filter/filter.component';
import { CustomTextAreaComponent } from './custom-textarea.component';
import { ReactiveFormsModule } from '@angular/forms';

@NgModule({
  declarations: [
    MenuComponent,
    SideMenuComponent,
    BreadcrumbComponent,
    FilterComponent,
    CustomTextAreaComponent,
  ],
  imports: [
    CommonModule,
    RouterModule,
    NgxMainVisualsModule.forRoot({ i18n: i18n }),
    MatExpansionModule,
    BreadcrumbModule,
    ReactiveFormsModule,
  ],
  exports: [
    NgxMainVisualsModule,
    MaterialModule,
    MenuComponent,
    SideMenuComponent,
    BreadcrumbComponent,
    MatExpansionModule,
    FilterComponent
  ],
  providers: [
    InstitutionResolver,
    FilterService
  ]
})
export class SharedModule {
}
