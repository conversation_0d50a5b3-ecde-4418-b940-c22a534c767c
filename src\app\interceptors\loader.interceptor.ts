import {Injectable} from '@angular/core';
import {
  HttpErrorResponse,
  HttpEvent,
  HttpHandler,
  HttpInterceptor,
  HttpRequest,
  HttpStatusCode
} from '@angular/common/http';
import {Observable} from 'rxjs';
import {catchError, finalize} from 'rxjs/operators';
import {AlertService, FW_Properties, LanguageService, LoaderService} from "@ttwr-framework/ngx-main-visuals";
import {ConfigService} from "../services/config.service";

@Injectable()
export class LoaderInterceptor implements HttpInterceptor {
  private noOfRequests = 0;

  constructor(public loaderService: LoaderService, public alertService: AlertService,
              private languageService: LanguageService, private configService: ConfigService) {
  }

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    if (req.url.endsWith('.svg'))
      return next.handle(req);
    this.showLoader(req);
    return next.handle(req).pipe(
      finalize(() => {
        this.hideLoader(req);
      }),
      catchError((error: HttpErrorResponse) => {
        this.hideLoader(req);
        if (req.headers.has(FW_Properties.IGNORE_INTERCEPTING_FOR_ERRORS))
          throw error;
        if (error.status === HttpStatusCode.NotFound) {
          this.alertService.error(this.languageService.getLang('NOT_FOUND'));
          throw error;
        }
        if (error.status === HttpStatusCode.UnprocessableEntity && error.error['Value'] !== undefined)
        {
          this.alertService.error(this.languageService.getLang(error.error['Value']));
          throw error;
        }
        if (error.status === HttpStatusCode.BadRequest && error.error['error_description'] !== undefined) {
          this.alertService.error(this.languageService.getLang(error.error['error_description']));
          throw error;
        }
        if (error.status === HttpStatusCode.BadRequest && error.error['title'] !== undefined) {
          this.alertService.error(this.languageService.getLang(error.error['title']));
          throw error;
        }
        if (error.status === HttpStatusCode.InternalServerError && error.error['Value'] !== undefined) {
          this.alertService.error(this.languageService.getLang(error.error['Value']));
          throw error;
        }
        throw error;
      })
    );
  }

  private showLoader(req: HttpRequest<any>) {
    if (!req.headers.has(FW_Properties.IGNORE_LOADING_REQ_HEADER)) {
      this.loaderService.show();
      this.noOfRequests++;
    }
  }

  private hideLoader(req: HttpRequest<any>) {
    if (req.url.indexOf(this.configService.configuration.pingUrl) < 0 && !req.headers.has(FW_Properties.IGNORE_LOADING_REQ_HEADER)) {
      if (this.noOfRequests > 0)
        this.noOfRequests--;
      if (this.noOfRequests === 0)
        this.loaderService.hide();
    }
  }
}
