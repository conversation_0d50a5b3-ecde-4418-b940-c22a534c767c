import {Component} from '@angular/core';
import {AlertService, FormDef} from '@ttwr-framework/ngx-main-visuals';

import {AuthItem} from '../../models/auth-item.model';
import {AuhtItemService} from '../../services/auht-item.service';
import {MatLegacyDialogRef as MatDialogRef} from '@angular/material/legacy-dialog';

@Component({
  selector: 'app-role-create',
  templateUrl: './role-create.component.html',
  styleUrls: ['./role-create.component.scss'],
})
export class RoleCreateComponent {
  dialogTitle='Role Create';
  public config: FormDef = {
    titleHidden:true,
    fields: [{key: 'Name'}],
    fieldsDef: AuthItem.getModelDef(),
    obj: {},
    actionFields: [
      {
        label: 'Submit',
        delegateFunction: (obj) =>
          this.auhtItemService.createRole(obj).subscribe(() => {
            this.alertService.success('Success Create');
            this.dialogRef.close(true);
          }),
      },
      {
        label: 'Cancel',
        cssClass: 'btn-danger',
        causeValidate: false,
        delegateFunction: () => this.dialogRef.close(),
      },
    ],
  };

  constructor(
    private auhtItemService: AuhtItemService,
    public dialogRef: MatDialogRef<RoleCreateComponent>,
    private alertService: AlertService,
  ) {
  }
}
