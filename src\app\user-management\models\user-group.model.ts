import {Validators} from '@angular/forms';
import {RoleLovGridComponent} from './auth-item.model';
import {UserLovGridComponent} from './user.model';
import {Model, TtwrInputComponent, TtwrInputLovComponent, Types} from '@ttwr-framework/ngx-main-visuals';

export class UserGroup extends Model {
  Id!: number;
  Name!: string;
  Code!: string;
  _UserGroupRoles: any;

  protected static initializeModelDef() {
    return {
      defs: {
        Id: {
          dataType: Types.STRING,
          placeholder: '',
          label: '',
          ui: TtwrInputComponent,
        },
        Name: {
          dataType: Types.STRING,
          placeholder: 'Name',
          label: 'Name',
          ui: TtwrInputComponent,
          validators: [
            {
              name: 'required',
              message: 'This field is required',
              validator: Validators.required,
            },
          ],
        },
        Code: {
          dataType: Types.STRING,
          placeholder: 'Code',
          label: 'Code',
          ui: TtwrInputComponent,
          validators: [
            {
              name: 'required',
              message: 'This field is required',
              validator: Validators.required,
            },
          ],
        },
        _UserGroupRoles: {
          dataType: Types.STRING,
          placeholder: 'Group roles',
          label: 'Group roles',
          ui: TtwrInputLovComponent,
          config: {
            popUpHeader: 'Roles',
            gridComponent: RoleLovGridComponent,
            searchValueField: '_UserGroupRolesNames',
            multiSelect: true,
          },
        },
        _UserGroupUsers: {
          dataType: Types.STRING,
          placeholder: 'Group users',
          label: 'Group users',
          ui: TtwrInputLovComponent,
          config: {
            popUpHeader: 'Roles',
            gridComponent: UserLovGridComponent,
            searchValueField: 'FullName',
            multiSelect: true,
          },
        },
      },
    };
  }
}
