@use 'sass:map';
@use '@angular/material' as mat;

$my-app-primary: mat.define-palette($mat-blue-base) !default;
$my-app-accent: mat.define-palette($mat-navy-blue-base) !default;
$my-app-warn: mat.define-palette($mat-red-base, 900, 500, 700) !default;

$mat-light-theme-foreground: (
  base: mat.get-color-from-palette($my-app-accent, 500),
  icon: mat.get-color-from-palette($my-app-primary, 500),
  icons: mat.get-color-from-palette($my-app-primary, 500),
  text: mat.get-color-from-palette($my-app-accent, 500),
  slider-min: mat.get-color-from-palette($my-app-primary, 500),
  slider-off: mat.get-color-from-palette($my-app-primary, 500),
  slider-off-active: mat.get-color-from-palette($my-app-primary, 500)
);
$mat-light-theme-background: (
  background: mat.get-color-from-palette($mat-gray-base, 200)
);
