///**********************************  mat-form-field default style ***************************************/
///**************** mat-form-field-outline-styling ****************************/
.mat-form-field {
  line-height: normal !important;
}

.mat-form-field-appearance-outline {
  .mat-form-field-subscript-wrapper {
    padding: 0 0.2rem !important;
  }
  .mat-form-field-flex {
    display: flex;
    align-items: center;
  }
  .mat-form-field-outline-thick {
    .mat-form-field-outline-end,
    .mat-form-field-outline-gap,
    .mat-form-field-outline-start {
      border-width: 1px !important;
    }
  }
  &:not(.mat-form-field-type-richtextarea-file) {
    .mat-form-field-infix {
      display: flex !important;
      flex-direction: row-reverse;
      align-items: center;
      height: 2.8rem;
      min-height: min-content;
      svg-icon {
        margin: {
          top: 0px;
          bottom: 0px;
          #{$end-direction}: 10px;
          #{$start-direction}: 0px;
        }
      }
      label {
        margin-bottom: 0px !important;
      }
      input.mat-input-element {
        top: -0.2rem;
        position: relative;
      }
      textarea.mat-input-element {
        min-height: 6rem;
      }
      .mat-form-field-label {
        display: flex !important;
        align-items: center;
        height: 60%;
        top: 1.3rem !important;
      }
    }
    .mat-form-field-subscript-wrapper {
      margin-top: 0.2rem !important;
    }
  }
  .mat-form-field-suffix {
    height: 2.5rem;
    button {
      height: 100% !important;
    }
  }
}
//.mat-form-field-appearance-outline .mat-form-field-prefix, .mat-form-field-appearance-outline .mat-form-field-suffix {
//  top: 0.5em !important;
//}
.mat-form-field-appearance-outline .mat-form-field-outline {
  top: 0.5em !important;
}
.mat-form-field-appearance-outline .mat-select-arrow-wrapper {
  transform: none !important;
}

mat-form-field.mat-form-field.mat-form-field-appearance-outline
  > div.mat-form-field-wrapper
  > div.mat-form-field-flex
  > div.mat-form-field-infix {
  padding: 0.5em 0em;
}
mat-form-field.mat-form-field.mat-form-field-appearance-outline
  > div.mat-form-field-wrapper
  > div.mat-form-field-flex
  > div.mat-form-field-infix
  > span.mat-form-field-label-wrapper {
  top: -1.2em;
}
.mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label {
  transform: translateY(-1.3em) scale(0.75);
}
.mat-form-field-type-textarea-file {
  .mat-form-field-infix {
    height: auto !important;
  }
}
[type='number']::-webkit-inner-spin-button,
[type='number']::-webkit-outer-spin-button {
  height: 1.3rem;
}
ttwr-input-fileupload .mat-form-field .mat-icon-button .mat-button-wrapper > svg-icon {
  width: 1.2rem;
  height: unset;
}
/************ input icon is exists ************/
//icon
.mat-form-field svg-icon svg {
  width: 20px;
  height: 20px;
}
//
.mat-form-field-appearance-outline.mat-form-field-can-float
  svg-icon
  ~ .mat-input-server:focus
  + .mat-form-field-label-wrapper
  .mat-form-field-label,
.mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float
  svg-icon
  ~ .mat-form-field-label-wrapper
  .mat-form-field-label {
  transform: translateX(-30px * $transform-direction) translateY(-1.3em) scale(0.75);
}

.mat-form-field-appearance-outline svg-icon ~ .mat-form-field-label-wrapper .mat-form-field-label {
  margin-#{$start-direction}: 30px;
}

/********************** default is no float label if side label exists *********************/
.form-group label ~ .mat-form-field {
  &.mat-form-field-appearance-outline .mat-form-field-outline-start,
  &.mat-form-field-appearance-outline .mat-form-field-outline-end {
    min-width: 50%;
  }
  .mat-select-placeholder {
    color: rgba(0, 0, 0, 0.42);
    -webkit-text-fill-color: rgba(0, 0, 0, 0.42);
    transition: none;
  }
  .mat-input-element::-moz-placeholder {
    transition: none;
    color: rgba(0, 0, 0, 0.42);
    -webkit-text-fill-color: rgba(0, 0, 0, 0.42);
  }
  .mat-input-element:-ms-input-placeholder {
    transition: none;
    color: rgba(0, 0, 0, 0.42);
    -webkit-text-fill-color: rgba(0, 0, 0, 0.42);
  }
  .mat-input-element::-webkit-input-placeholder {
    transition: none;
    color: rgba(0, 0, 0, 0.42);
    -webkit-text-fill-color: rgba(0, 0, 0, 0.42);
  }

  .mat-form-field-label-wrapper .mat-form-field-label {
    display: none !important;
  }
  .mat-form-field-appearance-outline.mat-form-field-can-float
    .mat-input-server:focus
    + .mat-form-field-label-wrapper
    .mat-form-field-label,
  .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label {
    transform: none;
  }
}
///************************** form-group styles ******************************/
.form-group {
  display: flex;
  flex-direction: column;
  margin-bottom: 0px;
  min-height: 4.3rem;
  //width:50%;
  > label {
    display: flex !important;
    align-items: center;
    min-height: 4rem;
    align-self: baseline;
    text-align: start;
    width: 100%;
    padding: 0 0.3rem 0.6rem;
  }
  &[class*='col-'] {
    float: #{$start-direction};
  }
  &:not([class*='col-']) {
    width: 66.5%;
    clear: both;
    padding: 0px 15px;
  }
}

.filterable-controllers {
  .form-group {
    min-height: auto;
    width: 100%;
    padding: 0 2px;
    .mat-form-field{
      max-width: 100% !important;
      flex: 100% !important;
      .mat-form-field-wrapper{
        padding-bottom: 0!important;
        .mat-form-field-infix {
          height: 2.4rem;
        }
      }
    }
  }
}

.filter-dialog {
  .form-group {
    > label {
      flex: 0 0 80px;
      max-width: 80px;
    }
    > label ~ .mat-form-field {
      flex: 1 1 calc(100% - 80px);
      max-width: calc(100% - 80px);
    }
  }
}
///************ Radio Input************/
.mat-radio-label-content {
  padding: 0px 12px !important;
}

.dirction-field {
  > label {
    display: none !important;
    & ~ mat-radio-group {
      width: 100%;
    }
  }
}

mat-radio-group {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  .mat-radio-button {
    display: block;
    padding-bottom: 10px;
    .mat-radio-label {
      display: flex;
      align-items: center;
    }
  }
}
///************** dynamic form ******************/
ttwr-dynamic-form {
  ttwr-form form {
    > *:nth-last-child(3) {
      .form-group {
        float: #{$start-direction};
      }
    }
  }
}
.btn-add-form {
  position: relative;
  float: #{$end-direction};
  height: 0px;
}
ttwr-form ~ .btn-add-form {
  position: relative;
  bottom: 60px;
  float: #{$end-direction};
  margin-top: 0px;
}
.btn-remove-form {
  position: relative;
  #{$end-direction}: 40px;
  float: #{$end-direction};
  height: 69px;
  .btn {
    margin: 10px 0px;
  }
}
/******************* rich textarea ************/
.mat-form-field-appearance-legacy .mat-form-field-ripple {
  height: 1px !important;
}
/*************** mat-vertical-tab-group ***********/
.mat-vertical-tab-group {
  &.mat-tab-group {
    flex-direction: row;
    > .mat-tab-header {
      flex: 1 1 20%;
      border-bottom: none;
      .mat-tab-header-pagination {
        display: none !important;
      }
      .mat-tab-labels {
        flex-direction: column;
        .mat-tab-label {
          min-width: auto;
          white-space: normal;
          .mat-tab-label-content {
            white-space: normal;
          }
        }
      }
      .mat-ink-bar {
        height: 100%;
        #{$start-direction}: 98% !important;
      }
    }
  }

  > .mat-tab-body-wrapper {
    flex: 1 1 80%;
  }
}
/************ details view ************/
.details-view {
  &[class*='col-'] {
    float: #{$start-direction};
  }
  label {
    padding: 0px 15px;
  }
}
/*************** Responsive ***********/
@media all and (max-width: 576px) {
  .details-view {
    &:not([class*='col-']),
    &[class*='-12'] {
      > label {
        &:first-child {
          flex: 0 0 50% !important;
          max-width: 50% !important;
        }
        &:not(:first-child) {
          flex: 0 0 50% !important;
          max-width: 50% !important;
        }
      }
    }
  }
}
@media all and (max-width: 800px) {
  .form-group,
  .details-view {
    &:not([class*='col-']) {
      max-width: 100%;
      width: 100% !important;
    }
  }
  .form-group:not([class*='col-']).hasFieldActions {
    max-width: 80%;
    width: 80% !important;
  }
}

@media all and (max-width: 1200px) {
  .details-view {
    > label {
      &:first-child:not([class*='col-']) {
        flex: 0 0 100px !important;
        max-width: 100px !important;
      }
      &:not(:first-child):not([class*='col-']) {
        flex: 0 0 calc(100% - 100px) !important;
        max-width: calc(100% - 100px) !important;
      }
    }
  }
}

@media all and (min-width: 1200px) {
  .details-view {
    > label {
      &:first-child:not([class*='col-']) {
        flex: 0 0 125px !important;
        max-width: 125px !important;
      }
      &:not(:first-child):not([class*='col-']) {
        flex: 0 0 calc(100% - 125px) !important;
        max-width: calc(100% - 125px) !important;
      }
    }
  }
}
@media all and (max-width: 576px) {
  ttwr-form ~ .btn-add-form {
    position: relative;
    bottom: 59px !important;
  }
}

//get form group classes from bootstrap4

.form-inline .form-control {
  display: inline-block;
  width: auto;
  vertical-align: middle;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -5px;
  margin-left: -5px;
}
