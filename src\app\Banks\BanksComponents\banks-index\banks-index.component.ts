import {Component} from '@angular/core';
import {Banks} from '../../Models/Banks.model';

import {ActivatedRoute, Router} from '@angular/router';

import {BanksService} from '../../Services/banks.service';
import {Observable} from 'rxjs';
import {AlertService, GridDef, Model} from '@ttwr-framework/ngx-main-visuals';


@Component({
  selector: 'app-banks-index',
  templateUrl: './banks-index.component.html',
  styleUrls: ['./banks-index.component.scss']
})
export class BanksIndexComponent {

  public config: GridDef = {
    title: 'Banks Index',
    fields: [
      {key: 'bankENName'},
      {key: 'bankARName'},
      {key: 'bankIdentityCode'},
    ],
    actionFields: [
      {
        header: 'Actions',
        actions: [
          {
            icon: 'assets/icons/grid/ic_view.svg',
            cssClass: 'btn-view',
            tooltip: 'View',
            delegateFunction: (obj) => this.router.navigate(['view/' + obj['Id']], {relativeTo: this.activatedRoute.parent})

          },
          {
            icon: 'assets/icons/grid/ic_edit.svg',
            cssClass: 'btn-edit',
            tooltip: 'Edit',
            delegateFunction: (obj) => this.router.navigate(['update/' + obj['Id']], {relativeTo: this.activatedRoute.parent})

          },
          {
            icon: 'assets/icons/grid/ic_delete.svg',
            cssClass: 'btn-delete',
            tooltip: 'Delete',
            confirmationRequired: true,
            delegateFunction: (obj) => this.banksService.delete(obj['Id']).subscribe(() => {
              this.alertService.success('Success Delete');
              if (this.config.refreshSubject)
                this.config.refreshSubject.next(true);
            }),
          },
        ]
      }
    ],
    fieldsDef: Banks.getModelDef(),
    actions: [
      {
        icon: 'assets/icons/grid/ic_create.svg',
        cssClass: 'btn-create',
        tooltip: 'Create',
        delegateFunction: () => this.router.navigate(['create/'], {relativeTo: this.activatedRoute.parent})

      },
    ],
    dataFunction: (filter, sort, pageIndex, pageSize): Observable<Model[]> => {
      return this.banksService.index(filter, sort, pageIndex, pageSize);
    }
  };

  constructor(private banksService: BanksService, private alertService: AlertService,
              private router: Router, private activatedRoute: ActivatedRoute,) {
  }

}
