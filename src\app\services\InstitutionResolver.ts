import {SYGSBaseService} from "./SYGSBaseService";
import {IncomingTransfersModelService} from "../IncomingTransfers/Services/incoming-transfers-model.service";
import {OutgoingTransfersModelService} from "../OutgoingTransfers/Services/outgoing-transfers-model.service";
import {ActivatedRouteSnapshot, Resolve, RouterStateSnapshot} from "@angular/router";
import {Observable} from "rxjs";
import {Model} from "@ttwr-framework/ngx-main-visuals";
import {Injectable} from "@angular/core";
import {OutgoingBankWireService} from "../OutgoingBankWire/Services/outgoing-bank-wire.service";

@Injectable()
export class InstitutionResolver implements Resolve<Model> {


  map: { [key: string]: SYGSBaseService };

  constructor(
    private incomingTransfersModelService: IncomingTransfersModelService,
    private outgoingTransfersModelService: OutgoingTransfersModelService,
    private outgoingBankWireService: OutgoingBankWireService
  ) {
    this.map = {
      'incoming-transfer-model': incomingTransfersModelService,
      'outgoing-transfer-model': outgoingTransfersModelService,
      'outgoing-bank-wire-model': outgoingBankWireService,
    };
  }

  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<Model> {
    return this.map[route.data.service_key].viewAsString(route.params.id);
  }
}
