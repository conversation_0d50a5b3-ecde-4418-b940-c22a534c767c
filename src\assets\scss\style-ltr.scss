@import 'config-directions/direction-ltr';
/*****************************/
@font-face {
  font-family: 'webFont';
  src: url('fonts/webFont.woff') format('woff'), url('fonts/webFont.ttf') format('truetype');
}
/*****************************/
// customize fonts(type - size), colors,and all variables in theme and BS4
@import 'theme/theme&BS4-customizing/customizing-en';

@font-face {
  font-family: 'Kawkab';
  src: url('fonts/KawkabMono-Light.woff') format('woff'), url('fonts/KawkabMono-Light.woff2') format('woff2')
}

//shared style
@import '_main';
