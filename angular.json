{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"analytics": false, "packageManager": "npm"}, "version": 1, "newProjectRoot": "projects", "projects": {"angular12-base-project": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "sass"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/angular12-base-project", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "sass", "assets": ["src/favicon.ico", "src/assets", {"glob": "**/*", "input": "node_modules/@ttwr-framework/ngx-main-visuals/assets", "output": "assets"}], "styles": ["node_modules/bootstrap/dist/css/bootstrap.min.css", "node_modules/ngx-toastr/toastr.css", {"input": "src/assets/scss/style-rtl.scss", "bundleName": "arabicStyle", "inject": false}, {"input": "src/assets/scss/style-ltr.scss", "bundleName": "englishStyle", "inject": false}], "scripts": ["node_modules/bootstrap/dist/js/bootstrap.bundle.min.js"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "4mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "4kb", "maximumError": "8kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "angular12-base-project:build:production"}, "development": {"browserTarget": "angular12-base-project:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "angular12-base-project:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "sass", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.sass"], "scripts": []}}}}}}