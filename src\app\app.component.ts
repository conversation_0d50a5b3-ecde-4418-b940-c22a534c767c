import {Component} from '@angular/core';
import {NavigationEnd, Route, Router} from "@angular/router";
import {UserService} from "./user-management/services/user.service";
import {AlertService} from "@ttwr-framework/ngx-main-visuals";
import {AuthenticationService} from "./main/services/authentication.service";
import {ConfigService} from "./services/config.service";
import {StyleService} from "./services/style.service";

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.sass']
})
export class AppComponent {
  isLogin = false;

  constructor(
    private alertService: AlertService,
    public authService: AuthenticationService,
    private userService: UserService,
    private router: Router,
    private configService: ConfigService,
    private styleService: StyleService // this service is here just to initialize its constructor to initialize the styling
  ) {
  }

  async ngOnInit() {
    this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        document.body.className.split(' ').forEach((obj) => {
          if (obj.indexOf('ttwr-router-') > -1) {
            document.body.classList.remove(obj);
          }
        });
        document.body.classList.add('ttwr-router-' + event.url.split('/')[1]);
      }
    });
    // we need loading spinner or something
    this.isLogin = localStorage.getItem('token') !== null;
    // this.isLogin = true;
    this.authService.loginSubject.subscribe(async (res) => {
      // if (res)
      //token has been received definitely
      //await this.userService.viewCurrentUser();
      this.isLogin = res;
    });

    if (this.configService.configuration.enablePermissions) this.addPermissionsDynamically('', this.router.config);
    /*this.printPath('', this.router.config, res);
        this.downloadFile(res, '');*/
    /*this.generateAuthItemRelation('', this.router.config, null, res)
        this.downloadFile(res, '');*/
  }

  printPath(parent: string, config: Route[], res: string[]) {
    for (let i = 0; i < config.length; i++) {
      const route = config[i];
      // route.canActivate = [NgxPermissionsGuard];
      // if(route.data)
      //   route.data['permissions'] = {only: 'ADMIN'};
      // else{
      //   route.data = {
      //     permissions:{
      //       only: 'ADMIN'
      //     }
      //   }
      // }
      res.push(parent + '/' + route.path);
      // res.push(p)

      if (route.children) {
        const currentPath = route.path ? parent + '/' + route.path : parent;
        this.printPath(currentPath, route.children, res);
      }
    }
  }

  addPermissionsDynamically(parent: string, config: Route[]) {
    for (let i = 0; i < config.length; i++) {
      const route = config[i];
      const currentPath = route.path ? parent + '/' + route.path : parent;
      if (currentPath !== '/' && currentPath !== '/**' && currentPath !== '/dashboard')
      if (route.data) route.data['permissions'] = {only: currentPath};
      else
        route.data = {
          permissions: {only: currentPath},
        };
      if (route.children) {
        this.addPermissionsDynamically(currentPath, route.children);
      }
    }
  }

  generateAuthItemRelation(
    parent: string,
    config: Route[],
    parentConfig: Route,
    res: {
      parent: string;
      children: { NAME: string; INHERITED?: boolean; CLEAR_NAME?: string }[];
    }[]
  ) {
    for (let i = 0; i < config.length; i++) {
      const route = config[i];
      const data = config[i].data;
      if (route.children || (config[i].data && data && data.permsChildren)) {
        const currentPath = route.path ? parent + '/' + route.path : parent;
        this.generateAuthItemRelation(currentPath, route.children || [], config[i], res);
      }
    }
    if (parent.length > 0) {
      const children: { NAME: string; INHERITED?: boolean; CLEAR_NAME?: string }[] = [];
      for (let i = 0; i < config.length; i++) {
        const route = config[i];
        const currentPath = route.path ? parent + '/' + route.path : parent;
        children.push({
          NAME: currentPath,
          INHERITED: !(
            route.data &&
            route.data.perms &&
            route.data.perms.INHERITED != undefined &&
            route.data.perms.INHERITED == false
          ),
          CLEAR_NAME:
            route.data && route.data.perms && route.data.perms.CLEAR_NAME != undefined
              ? route.data.perms.CLEAR_NAME
              : null,
        });
      }
      if (parentConfig && parentConfig.data && parentConfig.data.permsChildren) {
        children.push(...parentConfig.data.permsChildren);
      }
      res.push({parent: parent, children: children});
    }
  }

  downloadFile(data: any) {
    const theJSON = JSON.stringify(data);
    const blob = new Blob([theJSON], {type: 'text/json;UTF-8'});
    const url = window.URL.createObjectURL(blob);
    window.open(url);
  }
}
