import {
  Model, ModelDef,
  TtwrInputComponent, TtwrInputDateComponent,
  TtwrInputDateTimeComponent, TtwrInputLovComponent, TtwrInputRichTextareaComponent,
  TtwrInputSelectComponent, TtwrInputTextareaComponent,
  Types
} from '@ttwr-framework/ngx-main-visuals';
import {UntypedFormControl, ValidationErrors, Validators} from "@angular/forms";
import {LOVBankListComponent} from "../../Banks/LOVBankList/LOVBankList.component";
import {LOVBankWireTypeListComponent} from "../../BankWireType/LOVBankWireTypeList/LOVBankWireTypeList.component";
import {LOVBankCustomerComponent} from "../../Banks/LOVBankCustomer/LOVBankCustomer.component";
import {LOVREBBranchesListComponent} from "../../Banks/LOVBankBranchesList/LOVREBBranchesList.component";
import {LOVBankBranchesListComponent} from "../../Banks/LOVBankBranchesList/LOVBankBranchesList.component";
import {CustomTextAreaComponent} from '../../shared/custom-textarea.component';

export class OutgoingBankWireModel extends Model {
  id!: string;
  addedAt!: Date;
  sygsRef!: string;
  orderType!: string;
  ttc!: string;
  payBankBIC!: string;
  payBranchBIC!: string;
  benefitBankBIC!: string;
  benefitBranchBIC!: string;
  payPartBIC!: string;
  benefitPartBIC!: string;
  optCode!: string;
  insCurrency!: string;
  insAmount!: number;
  insAmountAsWords!: string;
  exchangeRate!: number;
  dtlChg!: string;
  sendChangeAmount!: number;
  receiveChangeAmount!: number;
  settlementCurrency!: string;
  settlementAmount!: number;
  settlementAmountAsWords!: string;
  sendReference!: string;
  priority!: string;
  sendToReceiveInformation!: string;
  rmtInformation!: string;
  dtAccount!: string;
  dtName!: string;
  dtAdd!: string;
  ctAccount!: string;
  ctName!: string;
  ctAdd!: string;
  state!: string;
  relatedReference!: string;
  checkNumber!: number;
  endrCount!: string;
  endrNames!: string;
  rebMessageId!: number;
  dtNationalNumber!: string;
  dtMotherName!: string;
  dtPhoneNumber!: string;
  ctPhoneNumber!: string;
  customerNumber!: string;
  lastState!: string;
  ttcName!: string;
  payBankBICName!: string;
  payBranchBICName!: string;
  benefitBankBICName!: string;
  benefitBranchBICName!: string;
  payPartBICName!: string;
  benefitPartBICName!: string;
  isTransferred!: string;
  valueDate!: Date;


  protected static initializeModelDef(): ModelDef {
    return {
      defs: {
        id: {
          ID: 'id',
          dataType: Types.STRING,
          label: 'id',
          ui: TtwrInputComponent,
        },
        valueDate: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'valueDate',
          dataType: Types.DATE,
          label: 'valueDate',
          ui: TtwrInputDateComponent,
          validators: [
            {
              name: 'required',
              validator: Validators.required,
              message: 'This field is required'
            },
            {
              name: 'checkDate',
              validator: DateValidator.checkDate,
              message: 'The date must be between today date and two days maximum'
            }
          ]
        },
        isTransferred: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'isTransferred',
          dataType: Types.STRING,
          label: 'isTransferred',
          ui: TtwrInputComponent
        },
        payBankBICName: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'payBankBICName',
          dataType: Types.STRING,
          label: 'payBankBIC',
          ui: TtwrInputComponent
        },
        payBranchBICName: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'payBranchBICName',
          dataType: Types.STRING,
          label: 'payBranchBIC',
          ui: TtwrInputComponent
        },
        benefitBankBICName: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'benefitBankBICName',
          dataType: Types.STRING,
          label: 'benefitBankBIC',
          ui: TtwrInputComponent
        },
        benefitBranchBICName: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'benefitBranchBICName',
          dataType: Types.STRING,
          label: 'benefitBranchBIC',
          ui: TtwrInputComponent
        },
        payPartBICName: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'payPartBICName',
          dataType: Types.STRING,
          label: 'payPartBIC',
          ui: TtwrInputComponent
        },
        benefitPartBICName: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'benefitPartBICName',
          dataType: Types.STRING,
          label: 'benefitPartBIC',
          ui: TtwrInputComponent
        },
        addedAt: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'addedAt',
          dataType: Types.DATE,
          label: 'addedAt',
          ui: TtwrInputDateTimeComponent
        },
        ttcName: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'ttcName',
          dataType: Types.STRING,
          label: 'ttc',
          ui: TtwrInputComponent,
        },
        sygsRef: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'sygsRef',
          dataType: Types.STRING,
          label: 'sygsRef',
          ui: TtwrInputComponent
        },
        lastState: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'lastState',
          dataType: Types.STRING,
          label: 'lastState',
          ui: TtwrInputComponent
        },
        orderType: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'orderType',
          dataType: Types.STRING,
          label: 'orderType',
          ui: TtwrInputSelectComponent,
          options: [
            {
              label: 'customer and check transfer', value: 'MT103'
            },
            {
              label: 'Bank transfer', value: 'MT202'
            }
          ]
        },
        ttc: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'ttc',
          dataType: Types.STRING,
          label: 'ttc',
          ui: TtwrInputLovComponent,
          validators: [
            {
              name: 'required',
              validator: Validators.required,
              message: 'this field is required'
            }
          ],
          config: {
            cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
            multiSelect: false,
            gridComponent: LOVBankWireTypeListComponent,
            width: '70%',
            searchValueField: 'ttc'
          }
        },
        payBankBIC: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'payBankBIC',
          dataType: Types.STRING,
          label: 'payBankBIC',
          defaultValue: 'البنك العقاري',
          readonly: true,
          ui: TtwrInputComponent,
        },
        benefitBankBIC: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'benefitBankBIC',
          dataType: Types.STRING,
          label: 'benefitBankBIC',
          validators: [
            {
              name: 'required',
              validator: Validators.required,
              message: 'this field is required'
            }
          ],

          ui: TtwrInputLovComponent,
          config: {
            cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
            multiSelect: false,
            gridComponent: LOVBankListComponent,
            width: '70%',
            searchValueField: 'benefitBankBIC',
          },
        },
        benefitBranchBIC: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'benefitBranchBIC',
          dataType: Types.STRING,
          label: 'benefitBranchBIC',
          validators: [
            //   {
            //   name: 'required',
            //   validator: Validators.required,
            //   message: 'this field is required'
            // }
          ],
          ui: TtwrInputLovComponent,
          config: {
            cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
            multiSelect: false,
            gridComponent: LOVBankBranchesListComponent,
            width: '70%',
            searchValueField: 'benefitBranchBIC',
          },
        },
        payPartBIC: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'payPartBIC',
          dataType: Types.STRING,
          label: 'payPartBIC',
          ui: TtwrInputLovComponent,
          validators: [
            {
              name: 'required',
              validator: Validators.required,
              message: 'this field is required'
            }
          ],
          config: {
            cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
            multiSelect: false,
            gridComponent: LOVBankListComponent,
            width: '70%',
            searchValueField: 'payPartBIC',
          },
        },
        benefitPartBIC: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'benefitPartBIC',
          dataType: Types.STRING,
          label: 'benefitPartBIC',
          ui: TtwrInputLovComponent,
          validators: [{
            name: 'required',
            validator: Validators.required,
            message: 'this field is required'
          }],
          config: {
            cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
            multiSelect: false,
            gridComponent: LOVBankListComponent,
            width: '70%',
            searchValueField: 'benefitPartBIC',
          },
        },
        optCode: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'optCode',
          dataType: Types.STRING,
          label: 'optCode',
          ui: TtwrInputComponent
        },
        insCurrency: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'insCurrency',
          dataType: Types.STRING,
          label: 'insCurrency',
          defaultValue: 'SYP',
          validators: [{
            name: 'required',
            validator: Validators.required,
            message: 'this field is required'
          }],
          ui: TtwrInputSelectComponent,
          options: [
            {
              label: 'SYP', value: 'SYP'
            }
          ]
        },
        insAmount: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'insAmount',
          dataType: Types.NUMBER,
          label: 'insAmount',
          ui: TtwrInputComponent,
          validators: []
        },
        insAmountAsWords: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'insAmountAsWords',
          dataType: Types.STRING,
          label: 'insAmountAsWords',
          ui: TtwrInputComponent
        },
        exchangeRate: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'exchangeRate',
          dataType: Types.NUMBER,
          label: 'exchangeRate',
          ui: TtwrInputComponent,
          validators: [
            {
              name: 'required',
              validator: Validators.required,
              message: 'this field is required'
            },
            {
              name: 'min',
              validator: Validators.min(1),
              message: 'this field must be bigger than or equal 1'
            }
          ]
        },
        dtlChg: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'dtlChg',
          dataType: Types.STRING,
          label: 'dtlChg',
          ui: TtwrInputComponent
        },
        sendChangeAmount: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'sendChangeAmount',
          dataType: Types.NUMBER,
          label: 'sendChangeAmount',
          ui: TtwrInputComponent
        },
        receiveChangeAmount: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'receiveChangeAmount',
          dataType: Types.NUMBER,
          label: 'receiveChangeAmount',
          ui: TtwrInputComponent
        },
        settlementCurrency: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'settlementCurrency',
          dataType: Types.STRING,
          label: 'settlementCurrency',
          defaultValue: 'SYP',
          validators: [{
            name: 'required',
            validator: Validators.required,
            message: 'this field is required'
          }],
          ui: TtwrInputSelectComponent,
          options: [
            {
              label: 'SYP', value: 'SYP'
            }
          ]
        },
        settlementAmount: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'settlementAmount',
          dataType: Types.NUMBER,
          label: 'settlementAmount',
          validators: [{
            name: 'required',
            validator: Validators.required,
            message: 'this field is required'
          }],
          ui: TtwrInputComponent
        },
        settlementAmountAsWords: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'settlementAmountAsWords',
          dataType: Types.STRING,
          label: 'settlementAmountAsWords',
          ui: TtwrInputComponent
        },
        sendReference: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'sendReference',
          dataType: Types.STRING,
          label: 'sendReference',
          ui: TtwrInputComponent,
          validators: [
            {
              name: 'required',
              validator: Validators.required,
              message: 'This field is required'
            }
          ]
        },
        priority: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'priority',
          dataType: Types.STRING,
          label: 'priority',
          defaultValue: 'Sixth',
          validators: [{
            name: 'required',
            validator: Validators.required,
            message: 'this field is required'
          }],
          ui: TtwrInputSelectComponent,
          options: [
            {
              label: 'Second', value: 'Second'
            },
            {
              label: 'Third', value: 'Third'
            },
            {
              label: 'Fourth', value: 'Fourth'
            },
            {
              label: 'Fifth', value: 'Fifth'
            },
            {
              label: 'Sixth', value: 'Sixth'
            }
          ]
        },
        sendToReceiveInformation: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'sendToReceiveInformation',
          dataType: Types.STRING,
          label: 'sendToReceiveInformation',
          ui: CustomTextAreaComponent,
          validators: [
            {
              name: 'pattern',
              validator: Validators.pattern("^[^/\\\\]*$"),
              message: 'Character / and \\ ignored'
            },
            {
              name: 'required',
              validator: Validators.required,
              message: 'this field is required'
            }
          ],
          config: {
            rows: 5,
            maxLength: 175
          }
        },
        rmtInformation: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'rmtInformation',
          dataType: Types.STRING,
          label: 'rmtInformation',
          ui: CustomTextAreaComponent,
          validators: [
            {
              name: 'required',
              validator: Validators.required,
              message: 'this field is required'
            },
            {
              name: 'maxlength',
              validator: Validators.maxLength(140),
              message: 'Characters must be less than 140'
            },
            {
              name: 'minlength',
              validator: Validators.minLength(2),
              message: 'Characters must be bigger than 2'
            },
            {
              name: 'pattern',
              validator: Validators.pattern("^[^/\\\\]*$"),
              message: 'Character / and \\ ignored'
            }
          ],
          config: {
            rows: 4,
            maxLength: 140
          }
        },
        dtAccount: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'dtAccount',
          dataType: Types.STRING,
          label: 'dtAccount',
          ui: TtwrInputComponent,
          validators: [{
            name: 'required',
            validator: Validators.required,
            message: 'this field is required'
          },
            {
              name: 'maxlength',
              validator: Validators.maxLength(35),
              message: 'Characters must be less than 35'
            }
          ],
        },
        dtName: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'dtName',
          dataType: Types.STRING,
          label: 'dtName',
          validators: [{
            name: 'required',
            validator: Validators.required,
            message: 'this field is required'
          },
            {
              name: 'maxlength',
              validator: Validators.maxLength(35),
              message: 'Characters must be less than 35'
            },
            {
              name: 'pattern',
              validator: Validators.pattern("^[^/\\\\]*$"),
              message: 'Character / and \\ ignored'
            }
          ],
          ui: TtwrInputComponent
        },
        dtAdd: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'dtAdd',
          dataType: Types.STRING,
          label: 'dtAdd',
          ui: TtwrInputComponent,
          validators: [
            {
              name: 'required',
              validator: Validators.required,
              message: 'this field is required'
            },
            {
              name: 'maxlength',
              validator: Validators.maxLength(35),
              message: 'Characters must be less than 35'
            },
            {
              name: 'pattern',
              validator: Validators.pattern("^[^/\\\\]*$"),
              message: 'Character / and \\ ignored'
            }
          ]
        },
        ctAccount: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'ctAccount',
          dataType: Types.STRING,
          label: 'ctAccount',
          validators: [{
            name: 'required',
            validator: Validators.required,
            message: 'this field is required'
          },
            {
              name: 'maxlength',
              validator: Validators.maxLength(35),
              message: 'Characters must be less than 35'
            }
          ],
          ui: TtwrInputComponent
        },
        ctName: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'ctName',
          dataType: Types.STRING,
          label: 'ctName',
          validators: [
            {
              name: 'required',
              validator: Validators.required,
              message: 'this field is required'
            },
            {
              name: 'maxlength',
              validator: Validators.maxLength(35),
              message: 'Characters must be less than 35'
            },
            {
              name: 'pattern',
              validator: Validators.pattern("^[^/\\\\]*$"),
              message: 'Character / and \\ ignored'
            }
          ],
          ui: TtwrInputComponent
        },
        ctAdd: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'ctAdd',
          dataType: Types.STRING,
          label: 'ctAdd',
          ui: TtwrInputComponent,
          validators: [
            {
              name: 'required',
              validator: Validators.required,
              message: 'this field is required'
            },
            {
              name: 'maxlength',
              validator: Validators.maxLength(35),
              message: 'Characters must be less than 35'
            },
            {
              name: 'pattern',
              validator: Validators.pattern("^[^/\\\\]*$"),
              message: 'Character / and \\ ignored'
            }
          ],
        },
        state: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'state',
          dataType: Types.STRING,
          label: 'state',
          ui: TtwrInputComponent
        },
        relatedReference: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'relatedReference',
          dataType: Types.STRING,
          label: 'relatedReference',
          ui: TtwrInputComponent,
          validators: [
            {
              name: 'required',
              validator: Validators.required,
              message: 'This field is required'
            }
          ]
        },
        checkNumber: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'checkNumber',
          dataType: Types.NUMBER,
          label: 'checkNumber',
          ui: TtwrInputComponent,
          validators: [
            {
              name: 'maxlength',
              validator: Validators.maxLength(16),
              message: 'Characters must be less than 16'
            }
          ]
        },
        endrCount: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'endrCount',
          dataType: Types.STRING,
          label: 'endrCount',
          ui: TtwrInputComponent,
          validators: [
            {
              name: 'maxlength',
              validator: Validators.maxLength(16),
              message: 'Characters must be less than 16'
            },
            {
              name: 'pattern',
              validator: Validators.pattern("^[^/\\\\]*$"),
              message: 'Character / and \\ ignored'
            }
          ]
        },
        endrNames: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'endrNames',
          dataType: Types.STRING,
          label: 'endrNames',
          ui: TtwrInputTextareaComponent,
          validators: [
            {
              name: 'maxlength',
              validator: Validators.maxLength(170),
              message: 'Characters must be less than 170'
            },
            {
              name: 'pattern',
              validator: Validators.pattern("^[^/\\\\]*$"),
              message: 'Character / and \\ ignored'
            }
          ]
        },
        rebMessageId: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'rebMessageId',
          dataType: Types.NUMBER,
          label: 'rebMessageId',
          ui: TtwrInputComponent
        },
        dtNationalNumber: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'DtNationalNumber',
          dataType: Types.STRING,
          label: 'DtNationalNumber',
          validators: [{
            name: 'required',
            validator: Validators.required,
            message: 'this field is required'
          }],
          ui: TtwrInputComponent
        },
        dtMotherName: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'DtMotherName',
          dataType: Types.STRING,
          label: 'DtMotherName',
          validators: [{
            name: 'required',
            validator: Validators.required,
            message: 'this field is required'
          }],
          ui: TtwrInputComponent
        },
        dtPhoneNumber: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'DtPhoneNumber',
          dataType: Types.STRING,
          label: 'DtPhoneNumber',
          ui: TtwrInputComponent
        },
        ctPhoneNumber: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'CtPhoneNumber',
          dataType: Types.STRING,
          label: 'CtPhoneNumber',
          ui: TtwrInputComponent
        },
        customerNumber: {
          cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
          ID: 'customerNumber',
          dataType: Types.STRING,
          label: 'customerNumber',
          ui: TtwrInputLovComponent,
          config: {
            cssClass: {field: 'col-lg-6 col-12', label:'me-2'},
            multiSelect: false,
            gridComponent: LOVBankCustomerComponent,
            width: '70%'
          },
        }
      }
    }
  }

}

export class DateValidator {
  static checkDate(control: UntypedFormControl): ValidationErrors | null {
    const today = new Date();
    const maxDate = new Date();
    maxDate.setDate(maxDate.getDate() + 2);

    today.setHours(0, 0, 0, 0);
    maxDate.setHours(0, 0, 0, 0);
    if (new Date(control.value) >= today && new Date(control.value) <= maxDate)
      return null
    return {"checkDate": true};
  }
}
