import {Component, Input, OnInit, QueryList, ViewChildren} from '@angular/core';
import {MenuItem} from "../shared/models/MenuItem";
import {MatExpansionPanel} from "@angular/material/expansion";
import {Router} from "@angular/router";

@Component({
  selector: 'app-side-menu',
  templateUrl: './side-menu.component.html',
  styleUrls: ['./side-menu.component.scss']
})
export class SideMenuComponent implements OnInit {

  @Input() isExpanded = true;
  @Input() items!: MenuItem[];
  @Input() ref: any;
  @Input() sidenavPosition!: string;

  @ViewChildren(MatExpansionPanel) viewPanels!: QueryList<MatExpansionPanel>;

  constructor(public router: Router) {
  }

  ngOnInit() {
    this.items && this.items.forEach(item => {
      if (item.items) {
        let menuVisible = false;
        item.items.forEach((subItem: any) => {
          menuVisible = menuVisible || subItem.visible;
        });
      }
    });
    if (!this.isExpanded) {
      this.viewPanels.forEach(p => p.close());
    }
  }

  closeListItemExpanded(panel: any) {
    this.viewPanels.forEach(p => {
        if (p._getExpandedState() !== 'expanded')
          p.close();
      }
    );
    return true;
  }
}
