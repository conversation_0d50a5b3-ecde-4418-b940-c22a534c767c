.mat-accordion-position {
  display: flex;
  align-items: start;
  justify-content: center;
  flex-direction: column;
  height: 100%;

  .item-content {
    margin-bottom: 0px;
    align-items: center;
  }
}

.item-content {
  height: 40px;
  margin-bottom: 20px;
  font: normal normal normal 14px/29px Questv1;
}

.parent-without-children:hover {
  background-color: var(--SteelTeal);
  width: 100%;
  border-radius: 6px;
}

.parent-with-child {
  width: 100%;

  ::ng-deep mat-expansion-panel-header {
    height: 40px;
  }

}

::ng-deep .mat-list-item-content:hover {
  color: var(--white) !important;
  border-radius: 6px;
  cursor: pointer;
}

::ng-deep .mat-expansion-indicator:after {
  color: var(--black);
}


.mat-expansion-color {
  border-radius: 6px !important;
  //background-color: #47566e00;
  mat-panel-title {
    color: var(--black);

  }
}

.mat-expansion-color.mat-expanded, .mat-expansion-color.mat-expanded:hover {
  //background-color: #568890 !important;

  ::ng-deep .mat-expansion-panel-header[aria-disabled=false] {
    /* change what you want */
    border-radius: 6px !important;
    background-color: var(--SteelTeal) !important;
    color: var(--white) !important;
  }


  mat-panel-title {
    color: var(--white);
  }

  ::ng-deep .mat-expansion-panel-body {
    background-color: var(--white);
  }

  ::ng-deep .mat-expansion-indicator:after {
    color: var(--white);
  }
}

::ng-deep.mat-expansion-panel-body {
  //background-color: rgba(228, 228, 228, 0.9) !important;
}

.mat-expansion-color:hover {
  ::ng-deep .mat-expansion-panel-header[aria-disabled=false] {
    /* change what you want */
    border-radius: 6px !important;
    background-color: var(--SteelTeal) !important;
    color: var(--white) !important;
  }

  mat-panel-title {
    color: var(--white);
  }

  ::ng-deep .mat-expansion-indicator:after {
    color: var(--white);
  }
}


::ng-deep .mat-expansion-panel-content {
  background-color: transparent !important;
  border-radius: 6px !important;

  span {
    display: flex;
    align-items: start;
  }
}

.sub-item:hover {
  background-color: var(--SteelTeal);
  color: var(--white);
  border-radius: 6px;
}

.sub-item {
  margin-top: 5px;
  padding-top: 15px;
  padding-left: 5px;
  width: 100%;
  display: flex;
  padding-right: 3px;
  color: var(--SteelTeal);
  font: normal normal normal 14px/29px Questv1;
}


.parent-without-children {
  width: 100%;
  margin-bottom: 5px;

  .active {
    color: var(--white) !important;
    border-radius: 6px;

    .item-content {
      background-color: var(--SteelTeal) !important;
      border-radius: 6px;
    }
  }
}

.active-sub-menu {
  .active {
    .sub-item {
      background-color: var(--SteelTeal) !important;
      color: var(--white);
      border-radius: 6px;
    }
  }
}

::ng-deep .mat-list-item {
  width: 100%;
}

