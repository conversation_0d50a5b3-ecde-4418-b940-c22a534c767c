/************ Variables ************/
$important: null !default;
$host-ngDeep: null !default; // should be :host ::ng-deep
/************ Mixins in this File you can use ***************/
/* icon-size($width,$height,$cssClass_field)*/ // the icon which is included in mat-from-field
/* icon_position($position,$cssClass_field)*/ // the icon which is included in mat-from-field
/* notfloatLabel($cssClass_field) */
/* horizontal-formGroup($cssClass_field) */ // label and input are in one line
/* vertical-formGroup($cssClass_field) */ // label is above the input
/* border($top,$left,$bottom,$right,$cssClass_field)  */ // customizing each border of input
/* border-radius-mat-form-field($radius,$cssClass_field) */
/* border-outline($cssClass_field) */ // the input has just bottom-border
/* border-underline($cssClass_field,$border-bottom) */ // the input has (top,left,bottom,right)-border
/**********************************/
/************ Label ************/
@mixin icon-size($width, $height: $width, $cssClass_field: '') {
  #{$host-ngDeep} .form-group#{$cssClass_field} {
    .mat-form-field svg-icon svg {
      width: $width;
      height: $height;
    }
  }
}

@mixin icon_position($position: start, $cssClass_field: '') //position :'start','end'
 {
  @if $position == start {
    #{$host-ngDeep} .form-group#{$cssClass_field} {
      .mat-form-field-infix {
        flex-direction: row-reverse $important;
      }

      .mat-form-field-appearance-outline.mat-form-field-can-float
        .mat-input-server:focus
        + .mat-form-field-label-wrapper
        .mat-form-field-label,
      .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label {
        transform: translateX(30px) translateY(-1.3em) scale(0.75) $important;
      }

      .mat-form-field-label {
        margin-left: 30px $important;
      }
    }
    [dir='rtl'] #{$host-ngDeep} {
      #{$cssClass_field} {
        .mat-form-field-label {
          margin-right: 30px $important;
          margin-left: 0px $important;
        }
      }
    }
  } @else if $position == end {
    #{$host-ngDeep} .form-group#{$cssClass_field} {
      .mat-form-field-infix {
        flex-direction: row $important;
      }

      .mat-form-field-appearance-outline.mat-form-field-can-float
        .mat-input-server:focus
        + .mat-form-field-label-wrapper
        .mat-form-field-label,
      .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label {
        transform: translateY(-1.3em) scale(0.75) $important;
      }

      .mat-form-field-label {
        margin-left: 0px $important;
      }
    }
  }
}

//// clear floatLabel
@mixin notfloatLabel($cssClass_field: '') {
  //.mat-form-field-infix {
  //  border-top: 0px $important;
  //}
  #{$host-ngDeep} .form-group#{$cssClass_field} {
    .mat-form-field {
      &.mat-form-field-appearance-outline .mat-form-field-outline-start,
      &.mat-form-field-appearance-outline .mat-form-field-outline-end {
        min-width: 50% $important;
      }

      .mat-select-placeholder {
        color: rgba(0, 0, 0, 0.42) $important;
        -webkit-text-fill-color: rgba(0, 0, 0, 0.42) $important;
        transition: none $important;
      }

      .mat-input-element::-moz-placeholder {
        transition: none $important;
        color: rgba(0, 0, 0, 0.42) $important;
        -webkit-text-fill-color: rgba(0, 0, 0, 0.42) $important;
      }

      .mat-input-element:-ms-input-placeholder {
        transition: none $important;
        color: rgba(0, 0, 0, 0.42) $important;
        -webkit-text-fill-color: rgba(0, 0, 0, 0.42) $important;
      }

      .mat-input-element::-webkit-input-placeholder {
        transition: none $important;
        color: rgba(0, 0, 0, 0.42) $important;
        -webkit-text-fill-color: rgba(0, 0, 0, 0.42) $important;
      }

      .mat-form-field-label-wrapper .mat-form-field-label {
        display: none $important;
      }

      .mat-form-field-appearance-outline.mat-form-field-can-float
        .mat-input-server:focus
        + .mat-form-field-label-wrapper
        .mat-form-field-label,
      .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label {
        transform: none $important;
      }
    }
  }
}

/************************  Input ************************/
// form group style
@mixin horizontal-formGroup($cssClass_field: '') {
  #{$host-ngDeep} .form-group#{$cssClass_field} {
    flex-direction: row $important;

    .mat-form-field {
      width: 100% $important;
    }
  }
  @media all and (max-width: 1200px) {
    #{$host-ngDeep} .form-group#{$cssClass_field} {
      > label:not([class*='col-']) {
        flex: 0 0 100px $important;
        max-width: 100px $important;
      }

      > label ~ .mat-form-field:not([class*='col-']) {
        flex: 1 1 calc(100% - 100px) $important;
        max-width: calc(100% - 100px) $important;
      }
    }
  }
  @media all and (min-width: 1200px) {
    #{$host-ngDeep} .form-group#{$cssClass_field} {
      > label:not([class*='col-']) {
        flex: 0 0 125px $important;
        max-width: 125px $important;
      }

      > label ~ .mat-form-field:not([class*='col-']) {
        flex: 1 1 calc(100% - 125px) $important;
        max-width: calc(100% - 125px) $important;
      }
    }
  }
}

///
@mixin vertical-formGroup($cssClass_field: '') {
  #{$host-ngDeep} .form-group#{$cssClass_field} {
    flex-direction: column $important;

    > label {
      text-align: start $important;
      padding: 0 !important;
      margin-bottom: 0 !important;
      width: 100% $important;
      flex: 0 0 auto !important;
      max-width: none !important;
      min-height: auto !important;
    }

    .mat-form-field {
      width: 100% $important;
      flex: 0 0 auto !important;
      max-width: none !important;
    }
  }
}

// Border Style
@mixin border(
  $top: 1px solid currentColor,
  $left: 1px solid currentColor,
  $bottom: 1px solid currentColor,
  $right: 1px solid currentColor,
  $cssClass_field: null
) {
  [dir='ltr'] #{$host-ngDeep} .form-group#{$cssClass_field} {
    .mat-form-field-appearance-outline .mat-form-field-outline-end {
      border: {
        right: $right !important;
        left: none !important;
      }
    }

    .mat-form-field-appearance-outline .mat-form-field-outline-start {
      border: {
        left: $left !important;
        right: none !important;
      }
    }
  }
  [dir='rtl'] #{$host-ngDeep} .form-group#{$cssClass_field} {
    .mat-form-field-appearance-outline .mat-form-field-outline-end {
      border: {
        left: $right !important;
        right: none !important;
      }
    }

    .mat-form-field-appearance-outline .mat-form-field-outline-start {
      border: {
        right: $left !important;
        left: none !important;
      }
    }
  }
  #{$host-ngDeep} .form-group#{$cssClass_field} {
    .mat-form-field-appearance-outline .mat-form-field-outline-start,
    label ~ .mat-form-field-appearance-outline .mat-form-field-outline-gap,
    .mat-form-field-appearance-outline .mat-form-field-outline-end {
      border: {
        top: $top $important;
        bottom: $bottom $important;
      }
    }

    .mat-form-field-appearance-outline .mat-form-field-outline-gap {
      border: {
        bottom: $bottom $important;
      }
    }
    @if ($top == 'none') {
      .mat-form-field-appearance-outline .mat-form-field-outline-gap {
        border: {
          top: none $important;
        }
      }
    } @else {
      .mat-form-field-appearance-outline:not(.mat-form-field-should-float) .mat-form-field-outline-gap {
        border: {
          top: $top $important;
        }
      }
    }
  }
}

@mixin border-radius-mat-form-field($radius: 5px, $cssClass_field: '') {
  [dir='rtl'] #{$host-ngDeep} .form-group#{$cssClass_field} {
    .mat-form-field-appearance-outline .mat-form-field-outline-start {
      width: 10px !important;
      border-radius: 0 $radius $radius 0 !important;
      min-width: 10px !important;
    }

    .mat-form-field-appearance-outline .mat-form-field-outline-end {
      border-radius: $radius 0px 0px $radius !important;
    }
  }
  [dir='ltr'] #{$host-ngDeep} .form-group#{$cssClass_field} {
    .mat-form-field-appearance-outline .mat-form-field-outline-start {
      width: 10px !important;
      border-radius: $radius 0px 0px $radius !important;
      min-width: 10px !important;
    }

    .mat-form-field-appearance-outline .mat-form-field-outline-end {
      border-radius: 0 $radius $radius 0 !important;
    }
  }
}

@mixin border-outline($cssClass_field: '') {
  #{$host-ngDeep} .form-group#{$cssClass_field} {
    .mat-form-field-infix {
      place-items: center $important;
    }
  }

  @include border($cssClass_field: $cssClass_field);
}

@mixin border-underline($cssClass_field: '', $border-bottom: 1px solid currentColor) {
  #{$host-ngDeep} .form-group#{$cssClass_field} {
    .mat-form-field-infix {
      height: 3rem !important;
      place-items: flex-end $important;
      padding: 0.5em 0em 0.2em !important;

      svg-icon {
        place-self: center $important;
      }
    }
    .mat-form-field-type-richtextarea-file {
      .mat-form-field-infix {
        height: 15rem !important;
        overflow: auto;
      }
    }
    .mat-form-field-appearance-outline:not(.mat-form-field-type-richtextarea-file)
      .mat-form-field-infix
      .mat-form-field-label {
      mat-label,
      .mat-form-field-required-marker {
        align-self: flex-end;
      }
    }
  }
  @include border(none, none, $border-bottom, none, $cssClass_field);
  @include border-radius-mat-form-field(0px, $cssClass_field);
}

//// border style (color,hover-color,focus-color)
//@mixin border-color($border-color:rgba(0,0,0,.12),$border-color-hover:rgba(0,0,0,.87),$border-color-focus:#4a5199){
//
//  .mat-form-field-appearance-outline .mat-form-field-outline {
//    color: $border-color $important;
//  }
//  .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick{
//    color: $border-color-focus $important;
//  }
//  .mat-form-field-appearance-outline .mat-form-field-outline-thick{
//    color: $border-color-hover $important;
//  }
//}
//// border style
//@mixin border-color-disabled($border-color-disabled:rgba(0,0,0,.06)) {
//  .mat-form-field-appearance-outline.mat-form-field-invalid.mat-form-field-invalid .mat-form-field-outline-thick {
//    color: $border-color-disabled $important;
//  }
//}
//@mixin border-color-invalid($border-color-inv1alid:#f44336) {
//  .mat-form-field-appearance-outline.mat-form-field-disabled .mat-form-field-outline{
//    color: $border-color-invalid $important;
//  }
//}
