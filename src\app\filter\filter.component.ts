import {Component, EventEmitter, OnInit, Output} from '@angular/core';
import {
  FormDef, TtwrInputCheckboxComponent,
  TtwrInputComponent,
  TtwrInputDateComponent,
  TtwrInputSelectComponent,
  Types
} from "@ttwr-framework/ngx-main-visuals";
import {formatDate} from "@angular/common";
import {FilterService} from "../services/filter.service";
import {ActivatedRoute} from "@angular/router";
import {IncomingTransfersModel} from "../IncomingTransfers/Models/IncomingTransfers.model";
import {FilterModel} from "./Models/Filter.model";

@Component({
  selector: 'app-filter',
  templateUrl: './filter.component.html',
  styleUrls: ['./filter.component.sass']
})
export class FilterComponent implements OnInit {

  @Output() isSubmitted = new EventEmitter<boolean>();
  public config: FormDef = {
    fields: [
      {
        key: 'FromDate', cssClass: {field: 'col-md-4 field-design'}, sideLabel: false,
        isHidden: this.route.snapshot.data.breadcrumb == 'users-index',
        onChange: (val) => {
          if (val) {
            this.filterService.fromDate = formatDate(new Date(val), 'yyyy-MM-dd hh:mm:ss', 'en');
          } else {
            this.filterService.fromDate = null;
          }
          this.isSubmitted.emit(true);
        }
      },
      {
        key: 'ToDate', cssClass: {field: 'col-md-4 field-design'}, sideLabel: false,
        isHidden: this.route.snapshot.data.breadcrumb == 'users-index',
        onChange: (val) => {
          if (val) {
            this.filterService.toDate = formatDate(new Date(val), 'yyyy-MM-dd hh:mm:ss', 'en');
          } else {
            this.filterService.toDate = null;
          }
          this.isSubmitted.emit(true);
        }
      },
      {
        key: 'sygsRef', cssClass: {field: 'col-md-4 field-design'}, sideLabel: false,
        isHidden: this.route.snapshot.data.breadcrumb == 'OutgoingBankWires' || this.route.snapshot.data.breadcrumb == 'users-index',
      },
      {
        key: 'lastState', cssClass: {field: 'col-md-4 field-design'}, sideLabel: false,
        isHidden: this.route.snapshot.data.breadcrumb == 'IncomingTransfersReport' || this.route.snapshot.data.breadcrumb == 'users-index'
      },
      // {
      //   key: 'addedAt', cssClass: {field: 'col-md-4 field-design'}, sideLabel: false,
      //   isHidden: this.route.snapshot.data.breadcrumb == 'IncomingTransfersReport'
      // },
      {
        key: 'insAmount', cssClass: {field: 'col-md-4 field-design'}, sideLabel: false,
        isHidden: this.route.snapshot.data.breadcrumb == 'users-index'
      },
      {
        key: 'dtAccount', cssClass: {field: 'col-md-4 field-design'}, sideLabel: false,
        isHidden: this.route.snapshot.data.breadcrumb == 'IncomingTransfersReport' || this.route.snapshot.data.breadcrumb == 'users-index'
      },
      {
        key: 'dtName', cssClass: {field: 'col-md-4 field-design'}, sideLabel: false,
        isHidden: this.route.snapshot.data.breadcrumb == 'users-index'
      },
      {
        key: 'ctAccount', cssClass: {field: 'col-md-4 field-design'}, sideLabel: false,
        isHidden: this.route.snapshot.data.breadcrumb == 'users-index'
      },
      {
        key: 'ctName', cssClass: {field: 'col-md-4 field-design'}, sideLabel: false,
        isHidden: this.route.snapshot.data.breadcrumb == 'users-index'
      },
      {
        key: 'benefitBankBIC',
        cssClass: {field: 'col-md-4 field-design'},
        sideLabel: false,
        isHidden: this.route.snapshot.data.breadcrumb == 'IncomingTransfersReport' || this.route.snapshot.data.breadcrumb == 'users-index'
      },
      {
        key: 'payBranchBIC',
        cssClass: {field: 'col-md-4 field-design'},
        sideLabel: false,
        isHidden: ((this.route.snapshot.data.breadcrumb == 'IncomingTransfersReport'
          || this.route.snapshot.data.breadcrumb == 'users-index') && localStorage.getItem('roles')?.includes('SuperAdmin'))
      },
      {
        key: 'payBankBIC',
        cssClass: {field: 'col-md-4 field-design'},
        sideLabel: false,
        isHidden: this.route.snapshot.data.breadcrumb == 'OutgoingBankWires' || this.route.snapshot.data.breadcrumb == 'users-index'
      },
      {
        key: 'transferType', cssClass: {field: 'col-md-4 field-design'}, sideLabel: false,
        isHidden: this.route.snapshot.data.breadcrumb == 'OutgoingBankWires' || this.route.snapshot.data.breadcrumb == 'users-index'
      },
      {
        key: 'isSuccessful', cssClass: {field: 'col-md-4 isSuccessfulDesign field-design'}, sideLabel: false,
        isHidden: this.route.snapshot.data.breadcrumb == 'OutgoingBankWires' || this.route.snapshot.data.breadcrumb == 'users-index'
      },
      {
        key: 'username', cssClass: {field: 'col-md-4 field-design'}, sideLabel: false,
        isHidden: this.route.snapshot.data.breadcrumb != 'users-index'
      },
      {
        key: 'branchIdentificationCode', cssClass: {field: 'col-md-4 field-design'}, sideLabel: false,
        isHidden: this.route.snapshot.data.breadcrumb != 'users-index'
      },
    ],
    fieldsDef: FilterModel.getModelDef(),
    actionFields: [
      {
        label: 'Submit',
        cssClass: 'submit-style',
        delegateFunction: (obj) => {
          this.filterService.fromDate = obj.FromDate == null ? null : formatDate(new Date(obj.FromDate), 'yyyy-MM-dd hh:mm:ss', 'en');
          this.filterService.toDate = obj.ToDate == null ? null : formatDate(new Date(obj.ToDate), 'yyyy-MM-dd hh:mm:ss', 'en');
          this.filterService.sygsRef = obj.sygsRef;
          this.filterService.benefitBankBIC = obj.benefitBankBIC;
          this.filterService.payBankBIC = obj.payBankBIC;
          this.filterService.isSuccess = obj.isSuccessful;
          this.filterService.transferType = obj.transferType;
          this.filterService.lastState = obj.lastState;
          this.filterService.addedAt = obj.addedAt == null ? null : formatDate(new Date(obj.addedAt), 'yyyy-MM-dd', 'en')
          this.filterService.insAmount = obj.insAmount;
          this.filterService.dtAccount = obj.dtAccount;
          this.filterService.dtName = obj.dtName;
          this.filterService.ctAccount = obj.ctAccount;
          this.filterService.username = obj.username;
          this.filterService.ctName = obj.ctName;
          this.filterService.branchIdentificationCode = obj.branchIdentificationCode;
          this.filterService.payBranchBIC = obj.payBranchBIC;
          this.isSubmitted.emit(true);
        }
      },
    ],
    obj: {}
  }


  constructor(private filterService: FilterService, private route: ActivatedRoute) {
    console.log(this.route.snapshot.data.breadcrumb)
  }

  ngOnInit(): void {
  }


}
