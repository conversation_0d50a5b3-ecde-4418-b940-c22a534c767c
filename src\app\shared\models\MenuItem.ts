export interface MenuItem {
  label?: string;
  icon?: string;
  command?: (event?: any) => void;
  url?: string;
  routerLink?: any;
  queryParams?: {
    [k: string]: any;
  };
  visible?: boolean;

  /*    expanded?: boolean;
      disabled?: boolean;
      target?: string;
      routerLinkActiveOptions?: any;
      separator?: boolean;
      badge?: string;
      badgeStyleClass?: string;
      style?: any;
      styleClass?: string;*/
  title?: string;
  id?: string;
  items?: MenuItem[];
  /*    automationId?: any;*/
}
