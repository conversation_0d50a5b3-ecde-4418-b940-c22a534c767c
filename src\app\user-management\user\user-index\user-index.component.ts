import {Component} from '@angular/core';
import {User} from '../../models/user.model';
import {ActivatedRoute, Router} from '@angular/router';
import {UserService} from '../../services/user.service';
import {Observable, throwError} from 'rxjs';
import {MatLegacyDialog as MatDialog} from '@angular/material/legacy-dialog';
import {UserCreateComponent} from '../user-create/user-create.component';
import {UserGroupService} from '../../services/user-group.service';
import {AlertService, Filter, GridDef, LanguageService, Model} from "@ttwr-framework/ngx-main-visuals";
import {UserUpdateComponent} from "../user-update/user-update.component";
import {catchError} from "rxjs/operators";
import {FilterService} from "../../../services/filter.service";

@Component({
  selector: 'app-user-index',
  templateUrl: './user-index.component.html',
  styleUrls: ['./user-index.component.scss'],
})
export class UserIndexComponent {
  filters: Filter[] = [];

  public activeConfig: GridDef = {
    title: 'Users',
    fields: [
      {key: 'username'},
      {key: 'employeeId'},
      {key: 'roles'},
      {
        key: 'isInactive', displayCellValueFunc: (val) => {
          if (val == false) {
            return this.languageService.getLang('true')
          } else {
            return this.languageService.getLang('false')
          }
        }
      },
      {key: 'workingStartHour'},
      {key: 'workingEndHour'},
      {key: 'branchIdentificationName'},
    ],
    disableToggleAll: true,
    export: {
      excel: {
        enable: false,
      },
      pdf: {
        enable: false
      }
    },
    disableFiltrableColumns: true,

    actionFields: [
      {
        header: 'Actions',
        actions: [
          // {
          //   icon: 'assets/icons/grid/ic_view.svg',
          //   tooltip: 'View',
          //   cssClass: 'btn-view',
          //   delegateFunction: (obj) =>
          //     this.router.navigate(['view/' + obj['Id']], {relativeTo: this.activatedRoute.parent}),
          // },
          {
            icon: 'assets/icons/grid/ic_edit.svg',
            tooltip: 'Edit',
            cssClass: 'btn-edit',
            delegateFunction: async (obj) => {
              console.log(obj)
              const dialogRef = this.dialog.open(UserUpdateComponent, {
                width: '50%',
                panelClass: 'ttwr-dialog',
                data: {obj: obj},
              });
              dialogRef.afterClosed().subscribe((res: any) => {
                if (res && this.activeConfig.refreshSubject) {
                  this.activeConfig.refreshSubject.next(true);
                }
                if (res && this.inactiveConfig.refreshSubject) {
                  this.inactiveConfig.refreshSubject.next(true);
                }
              });
            },
          },
          // {
          //   icon: 'assets/icons/grid/ic_delete.svg',
          //   tooltip: 'Delete',
          //   cssClass: 'btn-delete',
          //   confirmationRequired: true,
          //   delegateFunction: (obj) =>
          //     this.userService.delete(obj['Id']).subscribe(() => {
          //       this.alertService.success('Success Delete');
          //       if (this.config.refreshSubject) this.config.refreshSubject.next(true);
          //     }),
          // },
        ],
      },
    ],
    fieldsDef: User.getModelDef(),
    actions: [
      {
        icon: 'assets/icons/grid/ic_create.svg',
        tooltip: 'Create',
        cssClass: 'btn-create',
        delegateFunction: () => {
          const dialogRef = this.dialog.open(UserCreateComponent, {
            width: '50%',
            panelClass: 'ttwr-dialog',
          });
          dialogRef.afterClosed().subscribe((res: any) => {
            if (res && this.activeConfig.refreshSubject) {
              this.activeConfig.refreshSubject.next(true);
            }
          });
        },
      },
    ],
    dataFunction: (filter, sort, pageIndex, pageSize): Observable<Model[]> => {
      return this.userService.getUsers(this.filters, sort, pageIndex, pageSize, true).pipe(catchError(err => {
        this.alertService.error(err.message);
        return throwError(err.message);
      }));
    },
  };
  public inactiveConfig: GridDef = {
    title: 'Users',
    fields: [
      {key: 'username'},
      {key: 'employeeId'},
      {key: 'roles'},
      {
        key: 'isInactive', displayCellValueFunc: (val) => {
          if (val == false) {
            return this.languageService.getLang('true')
          } else {
            return this.languageService.getLang('false')
          }
        }
      },
      {key: 'branchIdentificationName'},
    ],
    disableToggleAll: true,
    export: {
      excel: {
        enable: false,
      },
      pdf: {
        enable: false
      }
    },
    disableFiltrableColumns: true,

    actionFields: [
      {
        header: 'Actions',
        actions: [
          {
            icon: 'assets/icons/Lock.svg',
            tooltip: 'Activate',
            cssClass: 'btn-delete',
            confirmationRequired: true,
            delegateFunction: (obj) => {
              this.userService.ActivatedUser(obj['id']).subscribe(() => {
                this.alertService.success('Success Activated');
                if (this.inactiveConfig.refreshSubject)
                  this.inactiveConfig.refreshSubject.next(true);
                if (this.activeConfig.refreshSubject)
                  this.activeConfig.refreshSubject.next(true);
              })
            }

          },
        ],
      },
    ],
    fieldsDef: User.getModelDef(),
    actions: [],
    dataFunction: (filter, sort, pageIndex, pageSize): Observable<Model[]> => {
      return this.userService.getUsers(this.filters, sort, pageIndex, pageSize, false).pipe(catchError(err => {
        this.alertService.error(err.message);
        return throwError(err.message);
      }));
    },
  };

  UserFilter(event: any) {
    if (event == true) {
      this.filters = this.filterService.createFilter();
      this.activeConfig.refreshSubject?.next(true);
      this.inactiveConfig.refreshSubject?.next(true);
    }
  }

  constructor(
    private router: Router,
    private userService: UserService,
    private userGroupService: UserGroupService,
    private dialog: MatDialog,
    private alertService: AlertService,
    private activatedRoute: ActivatedRoute,
    private languageService: LanguageService,
    private filterService: FilterService,
  ) {
  }
}
