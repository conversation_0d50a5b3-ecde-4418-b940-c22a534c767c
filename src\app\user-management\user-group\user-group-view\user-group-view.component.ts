import {Component, OnInit} from '@angular/core';
import {UserGroup} from '../../models/user-group.model';

import {MatLegacyDialog as MatDialog} from '@angular/material/legacy-dialog';
import {User} from '../../models/user.model';
import {catchError, map} from 'rxjs/operators';
import {Observable, throwError} from 'rxjs';

import {ActivatedRoute, Router} from '@angular/router';
import {UserService} from '../../services/user.service';
import {UserGroupUpdateComponent} from '../user-group-update/user-group-update.component';
import {AuhtItemService} from '../../services/auht-item.service';
import {AuthItem} from '../../models/auth-item.model';
import {AddGroupUsersComponent} from '../add-group-users/add-group-users.component';
import {AddGroupRolesComponent} from '../add-group-roles/add-group-roles.component';
import {UserGroupService} from '../../services/user-group.service';
import {AlertService, Helper, MasterDetailDef, Model} from '@ttwr-framework/ngx-main-visuals';

@Component({
  selector: 'app-user-group-view',
  templateUrl: './user-group-view.component.html',
  styleUrls: ['./user-group-view.component.scss'],
})
export class UserGroupViewComponent implements OnInit {
  public config: MasterDetailDef = {
    masterViewConfig: {
      title: 'User Group View',
      fields: [{key: 'Name'}],
      obj: {},

      fieldsDef: UserGroup.getModelDef(),
      actionFields: [
        {
          label: 'Edit',
          cssClass: 'btn-info',
          delegateFunction: (obj) => {
            const dialogRef = this.dialog.open(UserGroupUpdateComponent, {
              width: '50%',
              panelClass: 'ttwr-dialog',
              data: {obj: obj},
            });
            dialogRef.afterClosed().subscribe(async (res: any) => {
              if (res && this.config.masterViewConfig.obj) {
                this.config.masterViewConfig.obj = await this.userGroupService
                  .view(this.config.masterViewConfig.obj['Id'])
                  .pipe(
                    map((res) => {
                      return res;
                    })
                  )
                  .toPromise();
                if (this.config.masterViewConfig.refreshSubject) this.config.masterViewConfig.refreshSubject.next(true);
              }
            });
          },
        },
        {
          label: 'Delete',
          confirmationRequired: true,
          cssClass: 'btn-warning',
          delegateFunction: (obj) =>
            this.userService.delete(obj['Id']).subscribe(() => {
              this.alertService.success('Success Delete');
              this.router.navigate(['index/'], {relativeTo: this.activatedRoute.parent});
            }),
        },
        {
          label: 'Done',
          cssClass: 'btn-danger',
          delegateFunction: () => this.router.navigate(['index/'], {relativeTo: this.activatedRoute.parent}),
        },
      ],
    },
    detailGridsConfig: [
      {
        title: 'Users',
        fields: [{key: 'Username'}, {key: 'FullName'}],
        fieldsDef: User.getModelDef(),
        dataFunction: (filter, sort, pageIndex, pageSize): Observable<Model[]> => {
          if (this.config.masterViewConfig.obj)
            return this.userService.indexOnGroup(
              this.config.masterViewConfig.obj['Id'],
              filter,
              sort,
              pageIndex,
              pageSize
            ).pipe(catchError(err => {
              this.alertService.error(err.message);
              return throwError(err.message);
            }));
          else return new Observable<Model[]>();
        },
        actionFields: [
          {
            header: 'Actions',
            actions: [
              {
                icon: 'assets/icons/grid/ic_delete.svg',
                tooltip: 'Delete',
                cssClass: 'btn-delete',
                confirmationRequired: true,
                delegateFunction: (obj) => {
                  if (this.config.masterViewConfig.obj)
                    this.userGroupService
                      .deleteUser(obj['Id'], this.config.masterViewConfig.obj['Id'])
                      .subscribe(() => {
                        this.alertService.success('Success Delete');
                        this.config.detailGridsConfig.forEach((grid) =>
                          grid.refreshSubject ? grid.refreshSubject.next(true) : undefined
                        );
                      });
                },
              },
            ],
          },
        ],
        actions: [
          {
            icon: 'assets/icons/grid/ic_create.svg',
            tooltip: 'Create',
            cssClass: 'btn-create',
            delegateFunction: () => {

              const dialogRef = this.dialog.open(AddGroupUsersComponent, {
                width: '50%',
                panelClass: 'ttwr-dialog',
                data: {obj: this.config.masterViewConfig.obj},
              });
              dialogRef.afterClosed().subscribe((res: any) => {
                if (res) {
                  this.config.detailGridsConfig.forEach((grid) =>
                    grid.refreshSubject ? grid.refreshSubject.next(true) : undefined
                  );
                }
              });
            },
          },
        ],
      },
      {
        title: 'Roles',
        fields: [{key: 'Name'}],
        fieldsDef: AuthItem.getModelDef(),
        dataFunction: (filter, sort, pageIndex, pageSize): Observable<Model[]> => {
          if (this.config.masterViewConfig.obj)
            return this.auhtItemService.indexOnGroup(
              this.config.masterViewConfig.obj['Id'],
              filter,
              sort,
              pageIndex,
              pageSize
            ).pipe(catchError(err => {
              this.alertService.error(err.message);
              return throwError(err.message);
            }));
          else return new Observable<Model[]>();
        },
        actionFields: [
          {
            header: 'Actions',
            actions: [
              {
                icon: 'assets/icons/grid/ic_delete.svg',
                tooltip: 'Delete',
                cssClass: 'btn-delete',
                confirmationRequired: true,
                delegateFunction: (obj) => {
                  if (this.config.masterViewConfig.obj)
                    this.userGroupService
                      .deleteRole(obj['Id'], this.config.masterViewConfig.obj['Id'])
                      .subscribe(() => {
                        this.alertService.success('Success Delete');
                        this.config.detailGridsConfig.forEach((grid) =>
                          grid.refreshSubject ? grid.refreshSubject.next(true) : undefined
                        );
                      });
                },
              },
            ],
          },
        ],
        actions: [
          {
            icon: 'assets/icons/grid/ic_create.svg',
            tooltip: 'Create',
            cssClass: 'btn-create',
            delegateFunction: () => {
              const dialogRef = this.dialog.open(AddGroupRolesComponent, {
                width: '50%',
                panelClass: 'ttwr-dialog',
                data: {obj: this.config.masterViewConfig.obj},
              });
              dialogRef.afterClosed().subscribe((res: any) => {
                if (res) {
                  this.config.detailGridsConfig.forEach((grid) =>
                    grid.refreshSubject ? grid.refreshSubject.next(true) : undefined
                  );
                }
              });
            },
          },
        ],
      },
    ],
  };

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private userService: UserService,
    private alertService: AlertService,
    private activatedRoute: ActivatedRoute,
    private auhtItemService: AuhtItemService,
    private dialog: MatDialog,
    private userGroupService: UserGroupService,
  ) {
  }

  ngOnInit() {
    if (this.route.snapshot.data.obj)
      this.config.masterViewConfig.obj = Helper.deepCopy(this.route.snapshot.data.obj)
  }
}
