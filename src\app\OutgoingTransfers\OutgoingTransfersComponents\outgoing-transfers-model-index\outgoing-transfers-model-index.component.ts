import {Component, Inject, LOCALE_ID} from '@angular/core';
import {OutgoingTransfersModel} from '../../Models/OutgoingTransfers.model';

import {ActivatedRoute, Router} from '@angular/router';

import {OutgoingTransfersModelService} from '../../Services/outgoing-transfers-model.service';
import {Observable, throwError} from 'rxjs';
import {
  AlertService,
  Filter,
  GridDef,
  LanguageService,
  Model
} from '@ttwr-framework/ngx-main-visuals';
import {FilterService} from "../../../services/filter.service";
import {formatDate, formatNumber} from "@angular/common";
import {catchError} from "rxjs/operators";


@Component({
  selector: 'app-outgoing-transfers-model-index',
  templateUrl: './outgoing-transfers-model-index.component.html',
  styleUrls: ['./outgoing-transfers-model-index.component.scss']
})
export class OutgoingTransfersModelIndexComponent {
  filters: Filter[] = [];

  public config: GridDef = {
    title: 'OutgoingTransfersReport',
    fields: [
      {key: 'sygsRef'},
      {key: 'benefitBankBIC'},
      {
        key: 'valueDate', displayCellValueFunc: (val) => {
          if (val) {
            return formatDate(new Date(val), 'yyyy-MM-dd', 'en')
          } else return '-'
        }
      },

      {
        key: 'addedAt', displayCellValueFunc: (val) => {
          if (val) {
            return formatDate(new Date(val), 'yyyy-MM-dd hh:mm:ss', this.locale);
          } else return '-'
        }
      },
      {
        key: 'insAmount', displayCellValueFunc: (val) => {
          if (val) {
            return formatNumber(val, this.locale, '3.0-2');
          } else return '-'
        }
      },
      {
        key: 'settlementAmount', displayCellValueFunc: (val) => {
          if (val) {
            return formatNumber(val, this.locale, '3.0-2');
          } else return '-'
        }
      },
      {key: 'dtAccount'},
      {key: 'dtName'},
      {key: 'ctAccount'},
      {key: 'ctName'},
    ],
    actionFields: [
      {
        header: 'Actions',
        actions: [
          {
            icon: 'assets/icons/grid/ic_view.svg',
            cssClass: 'btn-view',
            tooltip: 'View',
            delegateFunction: (obj) => {
              this.router.navigate(['view/' + obj['transferId']], {relativeTo: this.activatedRoute.parent})
            }

          },
        ]
      }
    ],

    disableToggleAll: true,
    disableFiltrableColumns: true,
    export: {
      excel: {
        enable: false,
      },
      pdf: {
        enable: false
      }
    },

    fieldsDef: OutgoingTransfersModel.getModelDef(),
    actions: [],
    dataFunction: (filter, sort, pageIndex, pageSize): Observable<Model[]> => {
      return this.outgoingTransfersModelService.index(this.filters, sort, pageIndex, pageSize).pipe(catchError(err => {
        this.alertService.error(err.messag);
        return throwError(err.messag);
      }));
    }
  };

  constructor(private outgoingTransfersModelService: OutgoingTransfersModelService, private alertService: AlertService,
              private router: Router, private activatedRoute: ActivatedRoute,
              private languageService: LanguageService, private filterService: FilterService,
              @Inject(LOCALE_ID) public locale: string) {
  }

  submitForm(event: any) {
    if (event == true) {
      this.filters = this.filterService.createFilter();
      this.config.refreshSubject?.next(true);
    }

  }

}
