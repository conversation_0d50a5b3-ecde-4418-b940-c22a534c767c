@import '../../../assets/scss/theme/ttwr-theme-mixin/inputMixin';

$important: !important;
$host-ngDeep: ':host ::ng-deep';

@include border-underline;
//@include vertical-formGroup;

:host ::ng-deep svg-icon {
  svg,
  svg path,
  svg ellipse {
    fill: var(--primary);
  }
}

/*************************************************/
.login-holder-design {
  ::ng-deep .buttons-section {
    align-items: center;
    justify-content: center !important;
  }

  ::ng-deep .btn-primary {
    background-color: #0d606e;
  }

}

:host .login-page {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #ededed;

  main {
    margin: 0 0 1rem;
    flex: 1 1 auto;
    background: {
      image: url('../../../assets/images/background/login_bg.jpg');
      color: #ededed;
      repeat: no-repeat;
      size: 100% 100%;
      position: center center;
      attachment: fixed;
    }
    display: flex;
    align-items: center;

    .card {
      float: none;
      overflow: hidden;
      padding: 0px;
      margin: 0 auto;
      //background: transparent;

      .card-header {
        padding: 5px 0px;
        margin: 0px;
        height: auto;
        //background-color: var(--primary);
        img {
          margin: 1rem;
        }

        p {
          color: #ffffff;
          margin-bottom: 0.2rem;
        }
      }

      .card-body {
        ::ng-deep ttwr-form {
          .header-content {
            height: 0px !important;
          }

          .body-content {
            background-color: transparent !important;
            border: none !important;
            box-shadow: none !important;
            padding: 0px !important;
            padding-bottom: 10px !important;

            .form-group {
              margin-bottom: 0.4rem;
            }


            .buttons-section > div {
              width: 100%;

              .btn-primary {
                width: 60%;
                height: 35px;
                -o-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
                -moz-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
                -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
                box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
                border-radius: 5px;
              }
            }
          }
        }
      }
    }
  }

  footer {
    margin-top: 0;
    background-color: transparent;
  }
}

.centerText {
  text-align: center;
}

.title1-design {
  color: #ec9f25;
  font-size: x-large;
  font-weight: bold;
  font-family: "Arabic Typesetting";
}

.title2-design {
  color: #0d606e;
  font-family: "Nirmala UI";
  font-size: large;
}

.title3-design {
  color: #000000;
  padding-top: 30px;
}

.center-login {
  display: flex;
  align-items: center;
  justify-content: center;
}

.section-one {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.image-display {
  height: 100%;
  object-fit: cover;
  width: 100%;
}

.image-cover {
  height: 100vh;
  padding: 0;
}

::ng-deep.mat-form-field-outline-end {
  background-color: transparent;
}

::ng-deep.mat-form-field-outline-start {
  background-color: transparent;
}

::ng-deep.mat-form-field-outline-gap {
  background-color: transparent;
}

.disable-scroll {
  overflow: hidden;
}

.login-holder-design {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 100%;
  background-image: url("../../../assets/LoginBackground.png") !important;
  background-repeat: no-repeat;
  background-size: cover;
}

.child-holder-design {
  box-shadow: 0px 2px 13px 3px #88888882;
  border-radius: 11px;
  background-color: #FFFFFF;
}


.login-height {
  height: 100%;
}

.ttwr-form-holder {
  width: 60%;

  img {
    width: 20pc;
  }

  //width: 70%;  //todo If return login form uncomment it

  ::ng-deep.btn-primary {
    width: 140px !important;
    border-radius: 6px !important;
    color: #FFFFFF;
  }
}

