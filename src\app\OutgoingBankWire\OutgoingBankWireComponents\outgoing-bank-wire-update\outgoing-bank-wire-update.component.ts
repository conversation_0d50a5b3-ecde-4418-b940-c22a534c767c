import {Component, OnInit} from '@angular/core';
import {AlertService, FormDef, Helper, LanguageService} from "@ttwr-framework/ngx-main-visuals";
import {Validators} from "@angular/forms";
import {OutgoingBankWireModel} from "../../Models/OutgoingBankWire.model";
import {ActivatedRoute, Router} from "@angular/router";
import {OutgoingBankWireService} from "../../Services/outgoing-bank-wire.service";
import {BanksService} from "../../../Banks/Services/banks.service";
import {ConfigService} from "../../../services/config.service";
import {formatDate} from "@angular/common";

@Component({
  selector: 'app-outgoing-bank-wire-update',
  templateUrl: './outgoing-bank-wire-update.component.html',
  styleUrls: ['./outgoing-bank-wire-update.component.sass']
})
export class OutgoingBankWireUpdateComponent implements OnInit {
  ttc: string = '';
  benefitBankBIC: string = '';
  payBranchBIC: string = '';
  benefitBranchBIC: string = '';
  payPartBIC: string = '';
  benefitPartBIC: string = '';
  regex = /^[A-Z0-9]*$/;

  public config: FormDef = {
    title: 'OutgoingBankWireModel Update',
    fields: [
      {key: 'dtAccount', label: 'PayingBankAccountNumber'},
      {key: 'ctAccount', label: 'BeneficiaryBankAccountNumber', validators: []},
      // {key: 'ctName', isHidden: true},
      // {key: 'ctPhoneNumber', isHidden: true},
      // {key: 'ctAdd', isHidden: true},
      {
        key: 'ttc'
      },
      {key: 'payBankBIC'},
      {
        key: 'benefitBankBIC', onChange: (val) => {
          if (!val) {
            this.config.form?.get('benefitBranchBIC')?.setValue(null)
            let branch = this.config.fields.find(k => k.key == 'benefitBranchBIC')
            branch!.defaultValue = null;
            branch!.uiObject.searchValue = "";
          }
        }, label: 'BeneficiaryBankName'
      },
      {key: 'benefitBranchBIC'},
      // {key: 'insCurrency', isHidden: true},
      {
        key: 'insAmount', validators: [
          {
            name: 'required',
            validator: Validators.required,
            message: 'this field is required'
          },
          {
            name: 'min',
            validator: Validators.min(this.configService.configuration.MinimumInsAmount),
            message: this.languageService.getLang('this field must be bigger than ') + this.configService.configuration.MinimumInsAmount
          }
        ]
      },
      {key: 'dtlChg', readonly: true, defaultValue: 'SHA'},
      {key: 'priority'},
      {key: 'valueDate'},
      {key: 'sendReference'},
      {key: 'relatedReference'},
      {key: 'rmtInformation', label: 'notes'},
      {
        key: 'sendToReceiveInformation', validators: [
          {
            name: 'pattern',
            validator: Validators.pattern("^[^/\\\\]*$"),
            message: 'Character / and \\ ignored'
          },
          {
            name: 'required',
            validator: Validators.required,
            message: 'this field is required'
          }
        ]
      },
    ],
    fieldsDef: OutgoingBankWireModel.getModelDef(),
    actionFields: [
      {
        label: 'Submit',
        cssClass: 'btn-primary',
        delegateFunction: (obj) => {
          if (!this.regex.test(obj.ttc)) {
            obj.ttc = this.ttc
          }

          if (!this.regex.test(obj.benefitBankBIC)) {
            obj.benefitBankBIC = this.benefitBankBIC
          }

          if (!this.regex.test(obj.payBranchBIC)) {
            obj.payBranchBIC = this.payBranchBIC
          }

          if (!this.regex.test(obj.benefitBranchBIC)) {
            obj.benefitBranchBIC = this.benefitBranchBIC;
          }
          obj.valueDate = formatDate(new Date(obj.valueDate), 'yyyy-MM-dd', 'en')
          this.outgoingBankWireModelService.updateWithStringID(this.config.obj?.['id'], obj).subscribe((res: any) => {
            if (res) {
              if (res.code == 0) {
                this.alertService.success('Success Update');
                this.router.navigate(['view/' + this.config.obj?.['id']], {relativeTo: this.activatedRoute.parent});
              } else {
                this.alertService.error(res.message)
              }
            }
          }, error => {
            this.alertService.error(error)
          })
        }
      },
      {
        label: 'Cancel',
        cssClass: 'btn-danger',
        causeValidate: false,
        delegateFunction: () => this.router.navigate(['view/' + this.route.snapshot.paramMap.get('id')], {relativeTo: this.activatedRoute.parent}),
      }
    ]
  };

  constructor(private route: ActivatedRoute, private router: Router,
              private outgoingBankWireModelService: OutgoingBankWireService,
              private alertService: AlertService,
              private activatedRoute: ActivatedRoute,
              private bankService: BanksService,
              private configService: ConfigService,
              private languageService: LanguageService) {
  }

  ngOnInit() {
    if (this.route.snapshot.data.obj) {
      this.config.obj = Helper.deepCopy(this.route.snapshot.data.obj);

      this.ttc = this.config.obj.ttc;
      this.config.obj.ttc = this.route.snapshot.data.obj.ttcName;

      this.payBranchBIC = this.config.obj.payBranchBIC;
      this.config.obj.payBranchBIC = this.route.snapshot.data.obj.payBranchBICName;

      this.benefitBankBIC = this.config.obj.benefitBankBIC;
      this.config.obj.benefitBankBIC = this.route.snapshot.data.obj.benefitBankBICName;

      this.benefitBranchBIC = this.config.obj.benefitBranchBIC;
      this.config.obj.benefitBranchBIC = this.route.snapshot.data.obj.benefitBranchBICName;
    }

  }

}
