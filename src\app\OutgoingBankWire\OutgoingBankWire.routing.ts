import {NgModule} from "@angular/core";
import {RouterModule, Routes} from "@angular/router";
import {OutgoingBankWireMainComponent} from "./outgoing-bank-wire-main/outgoing-bank-wire-main.component";
import {OutgoingBankWireComponent} from "./OutgoingBankWireComponents/outgoing-bank-wire.component";
import {
  OutgoingBankWireIndexComponent
} from "./OutgoingBankWireComponents/outgoing-bank-wire-index/outgoing-bank-wire-index.component";
import {
  CustomerOutgoingBankWireCreateComponent
} from "./OutgoingBankWireComponents/CustomerOutgoing-bank-wire-create/CustomerOutgoing-bank-wire-create.component";
import {
  OutgoingBankWireModelViewComponent
} from "./OutgoingBankWireComponents/outgoing-bank-wire-model-view/outgoing-bank-wire-model-view.component";
import {InstitutionResolver} from "../services/InstitutionResolver";
import {
  OutgoingBankWireCreateComponent
} from "./OutgoingBankWireComponents/outgoing-bank-wire-create/outgoing-bank-wire-create.component";
import {
  CustomerOutgoingBankWireUpdateComponent
} from "./OutgoingBankWireComponents/customer-outgoing-bank-wire-update/customer-outgoing-bank-wire-update.component";
import {
  OutgoingBankWireUpdateComponent
} from "./OutgoingBankWireComponents/outgoing-bank-wire-update/outgoing-bank-wire-update.component";

const routes: Routes = [
  {
    path: 'OutgoingBankWire', component: OutgoingBankWireMainComponent, children: [
      {
        path: 'outgoing-bank-wire-model',
        component: OutgoingBankWireComponent,
        data: {perms: {CLEAR_NAME: 'outgoing-bank-wire-model'}},
        children:
          [
            {
              path: 'index', component: OutgoingBankWireIndexComponent,
              data: {
                breadcrumb: 'OutgoingBankWires',
                perms: {CLEAR_NAME: 'عرض'},
                permsChildren: [
                  {NAME: '/api/OutgoingBankWireModel/delete', CLEAR_NAME: 'خدمة الحذف'},]
              }
            },
            {
              path: 'CustomerOutgoingBankWireModelCreate', component: CustomerOutgoingBankWireCreateComponent,
              data: {
                breadcrumb: 'CustomerOutgoingBankWireModelCreate',
                perms: {CLEAR_NAME: 'انشاء'},
                permsChildren: [{NAME: '/api/OutgoingBankWireModel/create', CLEAR_NAME: 'خدمة الانشاء'}]
              }
            },
            {
              path: 'outgoingBankWireCreate', component: OutgoingBankWireCreateComponent,
              data: {
                breadcrumb: 'OutgoingBankWireCreateComponent',
                perms: {CLEAR_NAME: 'انشاء'},
                permsChildren: [{NAME: '/api/OutgoingBankWireModel/create', CLEAR_NAME: 'خدمة الانشاء'}]
              }
            },
            {
              path: 'view/:id', component: OutgoingBankWireModelViewComponent,
              resolve: {
                obj: InstitutionResolver,
              },
              data: {
                breadcrumb: 'OutgoingBankWireModel',
                service_key: 'outgoing-bank-wire-model',
                perms: {CLEAR_NAME: 'استعراض التفاصيل'},
                permsChildren: [{NAME: '/api/OutgoingBankWireModel/view', CLEAR_NAME: 'خدمة استعراض التفاصيل'}]
              }
            },
            {
              path: 'customerOutgoingBankWireUpdate/:id', component: CustomerOutgoingBankWireUpdateComponent,
              resolve: {
                obj: InstitutionResolver,
              },
              data: {
                breadcrumb: 'customerOutgoingBankWireModel Update',
                service_key: 'outgoing-bank-wire-model',
                perms: {CLEAR_NAME: 'تعديل'},
                permsChildren: [{NAME: '/api/OutgoingBankWireModel/update', CLEAR_NAME: 'خدمة التعديل'}]
              }
            },
            {
              path: 'OutgoingBankWireUpdate/:id', component: OutgoingBankWireUpdateComponent,
              resolve: {
                obj: InstitutionResolver,
              },
              data: {
                breadcrumb: 'OutgoingBankWireModel Update',
                service_key: 'outgoing-bank-wire-model',
                perms: {CLEAR_NAME: 'تعديل'},
                permsChildren: [{NAME: '/api/OutgoingBankWireModel/update', CLEAR_NAME: 'خدمة التعديل'}]
              }
            },
          ],
      },

    ]
  }
]


@NgModule({
  imports: [
    RouterModule.forChild(routes)
  ],
  exports: [
    RouterModule
  ]
})
export class OutgoingBankWireRouting {

}
