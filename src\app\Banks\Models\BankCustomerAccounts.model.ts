import {Model, TtwrInputComponent, Types} from "@ttwr-framework/ngx-main-visuals";


export class BankCustomerAccountModel extends Model {
  accountNo!: string;
  accountType!: string;
  balance!: string;
  available!: string;
  branch!: string;
  currency!: string;


  protected static initializeModelDef() {
    return {
      defs: {
        accountNo: {
          ID: 'accountNo',
          dataType: Types.STRING,
          label: 'accountNo',
          ui: TtwrInputComponent,
        },
        accountType: {
          ID: 'accountType',
          dataType: Types.STRING,
          label: 'accountType',
          ui: TtwrInputComponent
        },
        balance: {
          ID: 'balance',
          dataType: Types.STRING,
          label: 'balance',
          ui: TtwrInputComponent
        },
        available: {
          ID: 'available',
          dataType: Types.STRING,
          label: 'available',
          ui: TtwrInputComponent
        },
        branch: {
          ID: 'branch',
          dataType: Types.STRING,
          label: 'branch',
          ui: TtwrInputComponent
        },
        currency: {
          ID: 'currency',
          dataType: Types.STRING,
          label: 'currency',
          ui: TtwrInputComponent
        }
      }
    }
  }
}
