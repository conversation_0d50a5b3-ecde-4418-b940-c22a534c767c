import {Model, TtwrInputComponent, Types} from "@ttwr-framework/ngx-main-visuals";
import {Validators} from "@angular/forms";

export class Banks extends Model {
  bankENName!: string;
  bankARName!: string;
  bankIdentityCode!: string;


  protected static initializeModelDef() {
    return {
      defs: {
        bankENName: {
          ID: 'bankENName',
          dataType: Types.STRING,
          label: 'bankENName',
          ui: TtwrInputComponent,
          validators: [
            {
              name: 'required',
              validator: Validators.required,
              message: 'this field is required'
            }
          ]
        },
        bankARName: {
          ID: 'bankARName',
          dataType: Types.STRING,
          label: 'bankARName',
          ui: TtwrInputComponent,
          validators: [
            {
              name: 'required',
              validator: Validators.required,
              message: 'this field is required',
            }
          ]
        },
        bankIdentityCode: {
          ID: 'bankIdentityCode',
          dataType: Types.STRING,
          label: 'bankIdentityCode',
          ui: TtwrInputComponent,
          validators: [
            {
              name: 'required',
              validator: Validators.required,
              message: 'this field is required'
            }
          ]
        }
      }
    }
  }
}
