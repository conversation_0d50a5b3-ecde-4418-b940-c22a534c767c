import {Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {AlertService, LanguagePipe, TreeDef, TreeNode,} from '@ttwr-framework/ngx-main-visuals';
import {MatLegacyDialog as MatDialog} from '@angular/material/legacy-dialog';
import {AuthItem} from '../../models/auth-item.model';
import {AuhtItemService} from '../../services/auht-item.service';

@Component({
  selector: 'app-permission-tree',
  templateUrl: './permission-tree.component.html',
  styleUrls: ['./permission-tree.component.scss'],
})
export class PermissionTreeComponent implements OnInit {
  @ViewChild('searchInput', {static: false}) input!: ElementRef;

  public config: TreeDef = {
    data: [],
    actions: [],
  };

  public doneLoading!: boolean;

  constructor(
    private auhtItemService: AuhtItemService,
    private dialog: MatDialog,
    private alertService: AlertService,
    private languagePipe: LanguagePipe
  ) {
  }

  ngOnInit() {
    this.refreshTree();
  }

  public refreshTree() {
    this.auhtItemService
      .getTree(this.input ? (this.input.nativeElement.value !== '' ? this.input.nativeElement.value : null) : null)
      .subscribe((res) => {
        this.fillTree(res);
        if (this.config.refreshSubject) {
          this.config.refreshSubject.next(true);
        }
        this.doneLoading = true;
      });
  }

  private fillTree(root: any) {
    const treeNodeResult: TreeNode<AuthItem>[] = [];
    const rootNode: TreeNode<AuthItem> = {
      name: this.languagePipe.transform('Permission Tree'),
      children: [],
      parent: undefined,
      level: 1,
    };

    Array.prototype.forEach.call(root, (node) => {
      if (rootNode.children) rootNode.children.push(this.getTreeNodeFromDbNode(node, rootNode));
    });
    treeNodeResult.push(rootNode);

    this.config.data = treeNodeResult;
  }

  private getTreeNodeFromDbNode(dbNode: any, parent: any) {
    const node: TreeNode<AuthItem> = {
      name: dbNode.ClearName,
      data: dbNode,
      parent: parent,
      children: [],
      level: parent.level + 1,
    };
    dbNode.AuthItemChildren.forEach((n: any) => {
      if (node.children) node.children.push(this.getTreeNodeFromDbNode(n, node));
    });
    return node;
  }
}

