import {Component, Inject, OnInit} from '@angular/core';
import {
  AlertService,
  DynamicFormDef,
  TtwrInputComponent,
  TtwrInputSelectComponent,
  Types
} from "@ttwr-framework/ngx-main-visuals";
import {MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA, MatLegacyDialog as MatDialog} from "@angular/material/legacy-dialog";
import {SystemSettingService} from "../../Services/SystemSettingService";
import {Validators} from "@angular/forms";

@Component({
  selector: 'app-system-setting-update',
  templateUrl: './system-setting-update.component.html',
  styleUrls: ['./system-setting-update.component.sass']
})
export class SystemSettingUpdateComponent implements OnInit {
  public dialogTitle: string = 'UpdateSystemSettings';
  public formDef: DynamicFormDef = new DynamicFormDef();

  ngOnInit(): void {
    this.initDynamicForm()
    this.formDef.formsArray?.push(this.formDef.formDef!);
  }

  constructor(private systemSettingService: SystemSettingService,
              @Inject(MAT_DIALOG_DATA) public data: any,
              private alertService: AlertService,
              private dialog: MatDialog) {
  }

  initDynamicForm() {
    this.formDef = new DynamicFormDef();
    this.formDef.formsArray = [];
    this.formDef.title = '';
    this.formDef.hideAddRemoveButtons = true;
    this.formDef.formDef = {
      title: '',
      fields: [],
      actionFields: [
        {
          label: 'Edit',
          cssClass: 'btn-primary',
          delegateFunction: (dynamicObj) => {
            let body = {
              value: dynamicObj[this.data.key]
            };
            this.systemSettingService.updateWithStringID(this.data.id, body).subscribe((res: any) => {
              this.alertService.success('Success Update');
              this.dialog.closeAll()
            }, error => {
              this.alertService.error(error)
            })
          },
        },
      ],
      fieldsDef: {
        defs: {},
      },
    };

    if (this.data.systemSettingType == 'String') {
      if (this.data.key == "WorkMode") {
        this.formDef.formDef!.fieldsDef.defs[this.data.key] = {
          ID: this.data.key,
          ui: TtwrInputSelectComponent,
          dataType: Types.STRING,
          label: this.data.key,
          options: [
            {
              label: 'Work', value: 'Work'
            },
            {
              label: 'Maintenance', value: 'Maintenance'
            }
          ],
          validators: [
            {
              name: 'required',
              validator: Validators.required,
              message: 'This field is required',
            }],
        };
      } else {
        this.formDef.formDef!.fieldsDef.defs[this.data.key] = {
          ID: this.data.key,
          ui: TtwrInputComponent,
          dataType: Types.STRING,
          label: this.data.key,
          validators: [
            {
              name: 'required',
              validator: Validators.required,
              message: 'This field is required',
            }],
        };
      }
    } else if (this.data.systemSettingType == 'Int') {
      this.formDef.formDef!.fieldsDef.defs[this.data.key] = {
        ID: this.data.key,
        ui: TtwrInputComponent,
        dataType: Types.NUMBER,
        label: this.data.key,
        validators: [
          {
            name: 'required',
            validator: Validators.required,
            message: 'This field is required',
          }],
      };
    }

    this.formDef.formDef?.fields.push({
      key: this.data.key,
      defaultValue: this.data.value,
    });
  }

}
