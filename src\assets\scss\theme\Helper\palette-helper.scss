@function modify-foreground-background($theme,$foreground:null,$background:null) {
  $theme-color: map-get($theme, color);
  $background-palette:();
  $color-background-palette:();
  $foreground-palette:();
  $color-foreground-palette:();
  //////////////// background
  @if($background != null){
    // Replace the background in the color object's background palette.
    $color-background-palette: map-get($theme-color, background);
    $color-background-palette: map-merge($color-background-palette, $background);

    // Replace the background in the background palette.
    $background-palette: map-get($theme, background);
    $background-palette: map-merge($background-palette,$background);
  }

 //////////////// foreground
  @if($foreground != null){
     // Replace the foreground in the color object's foreground palette.
       $color-foreground-palette: map-get($theme-color, foreground);
       $color-foreground-palette: map-merge($color-foreground-palette, $foreground);

       // Replace the foreground in the foreground palette.
       $foreground-palette: map-get($theme, foreground);
       $foreground-palette: map-merge($foreground-palette,$foreground);
  }
  $modified-theme:$theme;

   @if($foreground != null and $background != null){
     $modified-theme: map-merge($theme, (
           foreground: $foreground-palette,
            color: (map-merge(map-get($theme, color),
                   (foreground: $color-foreground-palette)))
       )
     );
     $modified-theme: map-merge($modified-theme, (
                 background: $background-palette,
                 foreground:  (map-merge(map-get($modified-theme, foreground),
                                                     (background: $color-background-palette))),
                 color: (map-merge(map-get($modified-theme, color),
                        (background: $color-background-palette)))
      )
    );
   }@else if($foreground == null ){
      $modified-theme: map-merge($theme, (
        background: $background-palette,
         color: (map-merge(map-get($theme, color),
                (background: $color-background-palette)))
      )
    );
    }@else if($background == null ){
       $modified-theme: map-merge($theme, (
           foreground: $foreground-palette,
            color: (map-merge(map-get($theme, color),
                   (foreground: $color-foreground-palette)))
         )
       );
     }

  // Merge the changes into a new theme.
  @return $modified-theme;
}
