import {Component, Inject, LOCALE_ID} from '@angular/core';
import {IncomingTransfersModel} from '../../Models/IncomingTransfers.model';

import {ActivatedRoute, Router} from '@angular/router';

import {IncomingTransfersModelService} from '../../Services/incoming-transfers-model.service';
import {Observable, throwError} from 'rxjs';
import {
  AlertService,
  Filter,
  GridDef,
  LanguageService,
  Model
} from '@ttwr-framework/ngx-main-visuals';
import {FilterService} from "../../../services/filter.service";
import {formatDate, formatNumber} from "@angular/common";
import {catchError} from "rxjs/operators";


@Component({
  selector: 'app-incoming-transfers-model-index',
  templateUrl: './incoming-transfers-model-index.component.html',
  styleUrls: ['./incoming-transfers-model-index.component.scss']
})
export class IncomingTransfersModelIndexComponent {
  filters: Filter[] = [];

  public config: GridDef = {
    title: 'IncomingTransfersReport',
    fields: [
      {key: 'sygsRef'},
      {key: 'payBankName', isSortable: false},
      {key: 'valueDate'},
      {
        key: 'addedAt', displayCellValueFunc: (val) => {
          if (val) {
            return formatDate(new Date(val), 'yyyy-MM-dd hh:mm:ss', 'en');
          } else return '-'
        }
      },
      // {
      //   key: 'additionType', displayCellValueFunc: (val) => {
      //     if (val) {
      //       return this.languageService.getLang(val)
      //     } else return '-'
      //   }
      // },
      {
        key: 'insAmount', displayCellValueFunc: (val) => {
          if (val) {
            return formatNumber(val, this.locale, '3.0-2');
          } else return '-'
        }
      },
      {
        key: 'settlementAmount', displayCellValueFunc: (val) => {
          if (val) {
            return formatNumber(val, this.locale, '3.0-2');
          } else return '-'
        }
      },
      {key: 'dtAccount'},
      {key: 'dtName'},
      {key: 'ctAccount'},
      {key: 'ctName'},
      {key: 'employeeName'},
    ],
    actionFields: [
      {
        header: 'Actions',
        actions: [
          {
            icon: 'assets/icons/grid/ic_view.svg',
            cssClass: 'btn-view',
            tooltip: 'View',
            delegateFunction: (obj) => {
              this.router.navigate([])
                .then(result => {
                  window.open('incoming-transfers/incoming-transfers-model/view/' + obj['transferId'], '_blank')
                })
            }
          },
        ]
      },
    ],
    actions: [
      {
        label: 'Export To EXCEL',
        cssClass: 'btn-create btn-export',
        visible: (obj: any) => {
          if (localStorage.getItem('roles')?.includes('IncomingBankWireManagement') ||
            localStorage.getItem('roles')?.includes('SuperAdmin') ||
            localStorage.getItem('roles')?.includes('IncomingBankWireReporting')) {
            return true
          } else {
            return false
          }
        },
        delegateFunction: () => {
          let fromFilter = this.filters?.find(a => a.attribute == 'FromDate');
          let toFilter = this.filters?.find(a => a.attribute == 'ToDate');

          if (this.filters.length == 0 || fromFilter == null || toFilter == null ||
            (((new Date(toFilter.value).getTime() - new Date(fromFilter.value).getTime()) / (1000 * 60 * 60 * 24)) > 31)) {
            this.alertService.error('The start and end date must be specified with a maximum range of 31 days');
          } else {
            this.incomingTransfersModelService.exportToExcel(this.filters).subscribe((res: any) => {
              this.downloadFile(res);
            }, (error) => {
              this.alertService.error(error.message)
            })
          }
        }

      },
    ],
    disableToggleAll: true,
    disableFiltrableColumns: true,
    export: {
      excel: {
        enable: false,
      },
      pdf: {
        enable: false
      }
    },
    fieldsDef: IncomingTransfersModel.getModelDef(),
    dataFunction: (filter, sort, pageIndex, pageSize): Observable<Model[]> => {
      return this.incomingTransfersModelService.index(this.filters, sort, pageIndex, pageSize).pipe(catchError(err => {
        this.alertService.error(err.message);
        return throwError(err.message);
      }));
    }
  };

  constructor(private incomingTransfersModelService: IncomingTransfersModelService, private alertService: AlertService,
              private router: Router, private activatedRoute: ActivatedRoute, private languageService: LanguageService,
              private filterService: FilterService,
              @Inject(LOCALE_ID) public locale: string) {
  }

  IncomingFilter(event: any) {
    if (event == true) {
      this.filters = this.filterService.createFilter();
      this.config.refreshSubject?.next(true);
    }
  }

  downloadFile(data: any) {
    const blob = new Blob([data], {type: 'application/vnd.ms-excel',});
    const url = window.URL.createObjectURL(blob);
    window.open(url);
  }

}
