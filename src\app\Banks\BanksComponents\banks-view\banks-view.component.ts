import {Component, OnInit} from '@angular/core';
import {Banks} from '../../Models/Banks.model';
import {ActivatedRoute, Router} from '@angular/router';
import {BanksService} from '../../Services/banks.service';
import {AlertService, Helper,ViewDef} from '@ttwr-framework/ngx-main-visuals';


@Component({
  selector: 'app-banks-view',
  templateUrl: './banks-view.component.html',
  styleUrls: ['./banks-view.component.scss']
})
export class BanksViewComponent implements OnInit {

  public config: ViewDef = {
    title: 'Banks',
    fields: [
      {key: 'bankENName'},
      {key: 'bankARName'},
      {key: 'bankIdentityCode'},
    ],
    fieldsDef: Banks.getModelDef(),
    actionFields: [
      {
        label: 'Update',
        cssClass: 'btn-info',
        delegateFunction: (obj) => this.router.navigate(['update/' + obj['Id']], {relativeTo: this.activatedRoute.parent})

      },
      {
        label: 'Delete',
        cssClass: 'btn-warning',
        confirmationRequired: true,
        delegateFunction: (obj) => this.banksService.delete(this.config.obj['Id']).subscribe(() => {
          this.alertService.success('Success Delete');
          this.router.navigate(['index/'], {relativeTo: this.activatedRoute.parent});
        }),
      },
      {
        label: 'Done',
        cssClass: 'btn-danger',
        delegateFunction: (obj) => this.router.navigate(['index/'], {relativeTo: this.activatedRoute.parent})
      },
    ]
  };

  constructor(private route: ActivatedRoute, private router: Router,
              private banksService: BanksService, private alertService: AlertService, private activatedRoute: ActivatedRoute) {
  }

  ngOnInit() {
    if(this.route.snapshot.data.obj)
       this.config.obj =Helper.deepCopy(this.route.snapshot.data.obj)
  }

}
