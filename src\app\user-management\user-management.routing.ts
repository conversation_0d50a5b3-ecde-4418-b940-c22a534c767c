import {RouterModule, Routes} from '@angular/router';
import {NgModule} from '@angular/core';
import {UserComponent} from './user/user.component';
import {UserIndexComponent} from './user/user-index/user-index.component';
import {UserViewComponent} from './user/user-view/user-view.component';
import {RoleComponent} from './role/role.component';
import {RoleIndexComponent} from './role/role-index/role-index.component';
import {PermissionComponent} from './permission/permission.component';
import {PermissionTreeComponent} from './permission/permission-index/permission-tree.component';
import {MainComponent} from './main/main.component';
import {UserGroupComponent} from './user-group/user-group.component';
import {UserGroupIndexComponent} from './user-group/user-group-index/user-group-index.component';
import {UserGroupViewComponent} from './user-group/user-group-view/user-group-view.component';
import {RoleViewComponent} from './role/role-view/role-view.component';
import {RoleUpdateComponent} from './role/role-update/role-update.component';
import {RoleViewTreeComponent} from './role/role-view-tree/role-view-tree.component';
import {UserManagementResolver} from "./resolvers/user-management.resolver";


const routes: Routes = [
  {
    path: 'users-managements',
    component: MainComponent,
    children: [
      {
        path: 'users',
        component: UserComponent,
        children: [
          {path: 'index', component: UserIndexComponent,
          data: {
            breadcrumb: 'users-index'
          }},
          {
            path: 'view/:id',
            component: UserViewComponent,
            resolve: {
              obj: UserManagementResolver,
            },
            data: {
              service_key: 'user'
            }
          },
        ],
      },
      {
        path: 'role',
        component: RoleComponent,
        children: [
          {path: 'index', component: RoleIndexComponent},
          {
            path: 'view/:id',
            component: RoleViewComponent,
            resolve: {
              obj: UserManagementResolver,
            },
            data: {
              service_key: 'role'
            }
          },
          {
            path: 'view-tree/:id',
            component: RoleViewTreeComponent,
            resolve: {
              obj: UserManagementResolver,
            },
            data: {
              service_key: 'role'
            }
          },
          {
            path: 'update/:id',
            component: RoleUpdateComponent,
            resolve: {
              obj: UserManagementResolver,
            },
            data: {
              service_key: 'role'
            }
          },
        ],
      },
      {
        path: 'permission',
        component: PermissionComponent,
        children: [{path: 'index', component: PermissionTreeComponent}],
      },
      {
        path: 'user-group',
        component: UserGroupComponent,
        children: [
          {path: 'index', component: UserGroupIndexComponent},
          {
            path: 'view/:id',
            component: UserGroupViewComponent,
            resolve: {
              obj: UserManagementResolver,
            },
            data: {
              service_key: 'user-group'
            }
          },
        ],
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class UserManagementRouting {
}
