﻿import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Subject } from 'rxjs';
import { ConfigService } from '../../services/config.service';

@Injectable({ providedIn: 'root' })
export class AuthenticationService {
  loginSubject: Subject<boolean> = new Subject<boolean>();

  constructor(
    private http: HttpClient,
    private configService: ConfigService
  ) {
  }

  async login(username: any, password: any) {
    let body = {
      'username': username,
      'password': password
    }

    let options = {
      headers: new HttpHeaders().set('Content-Type', 'application/json')
    };
    let res = await this.http.post<any>(this.configService.configuration.authenticationUrl, body, options).toPromise();

    localStorage.setItem('token', res.sessionId);
    localStorage.setItem('username', username);
    localStorage.setItem('roles', res.userRoles)
    this.loginSubject.next(true);
  }

  logout() {
    localStorage.removeItem('token');
    localStorage.removeItem('username');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('roles');
    localStorage.removeItem('groups');
    this.loginSubject.next(false);
  }

  logoutWithCallApi() {
    if (localStorage.getItem('token')) {
      let options = {
        headers: new HttpHeaders().set('Content-Type', 'application/json')
      };
      let res = this.http.post<any>(this.configService.configuration.apiUrl + '/Auth/Logout', {}, options)
        .subscribe();
    }
    this.logout();
  }

  refreshAccessToken() {
    let body = new URLSearchParams();
    body.set('refresh_token', localStorage.getItem('refreshToken') ?? '');
    body.set('grant_type', 'refresh_token');
    body.set('scope', 'FrontEndApp');
    body.set('client_id', this.configService.configuration.authenticationClientId);

    let options = {
      headers: new HttpHeaders().set('Content-Type', 'application/x-www-form-urlencoded')
    };
    return this.http.post<any>(this.configService.configuration.authenticationUrl, body.toString(), options);
  }

  getAccessToken(): string | string[] {
    const item = localStorage.getItem('token');
    if (item === null) return '';
    else return item;
  }
}
