import {NgModule} from "@angular/core";
import {SharedModule} from "../shared/shared.module";
import {FormsModule} from "@angular/forms";
import {SystemSettingService} from "./Services/SystemSettingService";
import {SystemSettingComponent} from "./SystemSetting.component";
import {SystemSettingIndexComponent} from "./Components/system-setting-index/system-setting-index.component";
import {SystemSettingUpdateComponent} from "./Components/system-setting-update/system-setting-update.component";
import {SystemSettingRouting} from "./SystemSetting.routing";

@NgModule({
  imports: [
    SharedModule,
    FormsModule,
    SystemSettingRouting
  ],
  exports: [],
  declarations: [
    SystemSettingComponent,
    SystemSettingIndexComponent,
    SystemSettingUpdateComponent
  ],
  providers: [
    SystemSettingService
  ]
})

export class SystemSettingModule {

}
