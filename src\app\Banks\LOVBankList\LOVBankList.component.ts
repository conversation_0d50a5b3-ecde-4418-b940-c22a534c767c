import {Component, OnInit} from "@angular/core";
import {GridDef, LanguageService, Model, ModelLovGridComponent} from "@ttwr-framework/ngx-main-visuals";
import {BanksService} from "../Services/banks.service";
import {Banks} from "../Models/Banks.model";
import {Observable} from "rxjs";
import {MatLegacyDialogRef as MatDialogRef} from "@angular/material/legacy-dialog";


@Component({
  template: `
    <ttwr-lov-grid [config]="config" [selectName]="selectName" [selectValue]="selectValue"></ttwr-lov-grid>
  `,
})
export class LOVBankListComponent extends ModelLovGridComponent implements OnInit {

  public config: GridDef = {
    title: 'Banks',
    fields: [
      {
        key: 'bankIdentityCode'
      },
      {
        key: 'bankARName', isHidden: this.languageService.getSelectedLanguage() == 'en'
      },
      {
        key: 'bankENName', isHidden: this.languageService.getSelectedLanguage() == 'ar'
      }
    ],
    fieldsDef: Banks.getModelDef(),
    actionFields: [],
    actions: [],
    disableToggleAll: true,
    export: {
      pdf: {
        enable: false
      },
      excel: {
        enable: false
      }
    },

    dataFunction: (filter, sort, pageIndex, pageSize): Observable<Model[]> => {
      return this.bankService.index(filter, sort, pageIndex, pageSize);
    }

  }

  constructor(private bankService: BanksService,
              public dialogRef: MatDialogRef<LOVBankListComponent>,
              private languageService: LanguageService) {
    super();
  }

  ngOnInit() {
    if (this.languageService.selectedLanguage == 'ar') {
      this.selectName = 'bankARName';
    } else {
      this.selectName = 'bankENName ';
    }

    this.selectValue = 'bankIdentityCode'

    setTimeout(() => {
      if (this.selectValue && this.selectName) {
        this.config.rowSelectedFunction = (row) => {
          this.bankService.bankCode = row[this.selectValue];
          this.dialogRef.close({status: 1, name: row[this.selectName], id: row[this.selectValue], objs: [row]});
        }
      }
    }, 5)
  }

}
