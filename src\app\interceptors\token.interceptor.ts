import {Injectable} from '@angular/core';
import {
  HttpErrorResponse,
  HttpEvent,
  HttpHandler,
  HttpInterceptor,
  HttpRequest,
  HttpStatusCode
} from '@angular/common/http';
import {BehaviorSubject, Observable, throwError} from 'rxjs';

import {catchError, filter, switchMap, take} from 'rxjs/operators';
import {AuthenticationService} from "../main/services/authentication.service";
import {ConfigService} from "../services/config.service";
import {Token} from "../main/models/token";

@Injectable()
export class RefreshTokenInterceptor implements HttpInterceptor {
  private refreshTokenInProgress = false;
  // Refresh Token Subject tracks the current token, or is null if no token is currently
  // available (e.g. refresh pending).
  private refreshTokenSubject: BehaviorSubject<any> = new BehaviorSubject<any>(null);

  constructor(public auth: AuthenticationService, private configService: ConfigService) {
  }

  intercept(request: HttpRequest<any>, next: <PERSON>tt<PERSON><PERSON><PERSON><PERSON>): Observable<HttpEvent<any>> {
    return next.handle(request).pipe(
      catchError((error: HttpErrorResponse) => {
        // We don't want to refresh token for some requests like login or refresh token itself
        // So we verify url and we throw an error if it's the case

        if (
          request.url.includes(this.configService.configuration.authenticationUrl)
        ) {
          // We do another check to see if refresh token failed
          // In this case we want to logout user and to redirect it to login page
          this.auth.logout();
          return throwError(error);
        }

        // If error status is different than 401 we want to skip refresh token
        // So we check that and throw the error if it's the case
        if (error.status !== HttpStatusCode.Unauthorized) {
          return throwError(error);
        }

        if (this.refreshTokenInProgress) {
          // If refreshTokenInProgress is true, we will wait until refreshTokenSubject has a non-null value
          // – which means the new token is ready and we can retry the request again
          return this.refreshTokenSubject.pipe(filter(result => result !== null), take(1), switchMap(() => next.handle(this.addAuthenticationToken(request))));
        } else {
          this.refreshTokenInProgress = false;

          // Set the refreshTokenSubject to null so that subsequent API calls will wait until the new token has been retrieved
          this.refreshTokenSubject.next(null);
          this.auth.logout();
          return throwError(error);

          // Call auth.refreshAccessToken(this is an Observable that will be returned)
          // return this.auth
          //   .refreshAccessToken().pipe(switchMap((res: Token) => {
          //
          //     //When the call to refreshToken completes we reset the refreshTokenInProgress to false
          //     // for the next time the token needs to be refreshed
          //     this.refreshTokenInProgress = false;
          //     localStorage.setItem('token', res['access_token']);
          //     this.refreshTokenSubject.next(res['access_token']);
          //
          //     return next.handle(this.addAuthenticationToken(request));
          //   }), catchError((err: any) => {
          //     this.refreshTokenInProgress = false;
          //
          //     this.auth.logout();
          //     return throwError(error);
          //   }));
        }
      })
    );
  }

  addAuthenticationToken(request: HttpRequest<any>) {
    // Get access token from Local Storage
    const accessToken = this.auth.getAccessToken();

    // If access token is null this means that user is not logged in
    // And we return the original request
    if (!accessToken) {
      return request;
    }

    // We clone the request, because the original request is immutable
    return request.clone({
      setHeaders: {
        Authorization: this.auth.getAccessToken()
      }
    });
  }
}
