import {Component, Inject, OnInit} from '@angular/core';
import {AlertService, LanguageService, ViewDef} from "@ttwr-framework/ngx-main-visuals";
import {IncomingTransfersModel} from "../../Models/IncomingTransfers.model";
import {MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA, MatLegacyDialog as MatDialog} from "@angular/material/legacy-dialog";
import {formatNumber} from "@angular/common";
import {NewIncomingTransfersService} from "../../Services/new-incoming-transfers.service";
import {ReverseIncomingTransferComponent} from "../reverse-incoming-transfer/reverse-incoming-transfer.component";

@Component({
  selector: 'app-new-incoming-transfers-view',
  templateUrl: './new-incoming-transfers-view.component.html',
  styleUrls: ['./new-incoming-transfers-view.component.scss']
})
export class NewIncomingTransfersViewComponent implements OnInit {
  public dialogTitle: string = 'newIncomingTransferDetails'
  public viewConfig: ViewDef = {
    title: '',
    fields: [
      {
        key: 'sygsRef', cssClass: {field: 'col-md-6 col-12'}
      },
      {
        key: 'orderType', cssClass: {field: 'col-md-6 col-12'}
      },
      {
        key: 'ttc', cssClass: {field: 'col-md-6 col-12'}
      },
      {
        key: 'payBankBIC', cssClass: {field: 'col-md-6 col-12'}
      },
      {
        key: 'payBranchBIC', cssClass: {field: 'col-md-6 col-12'}
      },
      {
        key: 'benefitBankBIC', cssClass: {field: 'col-md-6 col-12'}
      },
      {
        key: 'benefitBranchBIC', cssClass: {field: 'col-md-6 col-12'}
      },
      {
        key: 'payPartBIC', cssClass: {field: 'col-md-6 col-12'}
      },
      {
        key: 'benefitPartBIC', cssClass: {field: 'col-md-6 col-12'}
      },
      {
        key: 'valueDate', cssClass: {field: 'col-md-6 col-12'}
      },
      {
        key: 'optCode', cssClass: {field: 'col-md-6 col-12'}
      },
      {
        key: 'insCurrency', cssClass: {field: 'col-md-6 col-12'}
      },
      {
        key: 'insAmount', cssClass: {field: 'col-md-6 col-12'}, displayCellValueFunc: (val) => {
          if (val) {
            return formatNumber(val, 'en', '3.0-0')
          } else
            return '-'
        }
      },
      {
        key: 'exchangeRate', cssClass: {field: 'col-md-6 col-12'}
      },
      {
        key: 'dtlChg', cssClass: {field: 'col-md-6 col-12'}
      },
      {
        key: 'sendChangeAmount', cssClass: {field: 'col-md-6 col-12'}
      },
      {
        key: 'receiveChangeAmount', cssClass: {field: 'col-md-6 col-12'}
      },
      {
        key: 'settlementCurrency', cssClass: {field: 'col-md-6 col-12'}
      },
      {
        key: 'settlementAmount', cssClass: {field: 'col-md-6 col-12'}, displayCellValueFunc: (val) => {
          if (val) {
            return formatNumber(val, 'en', '3.0-0')
          } else
            return '-'
        }
      },
      {
        key: 'sendReference', cssClass: {field: 'col-md-6 col-12'}
      },
      {
        key: 'priority', cssClass: {field: 'col-md-6 col-12'}
      },
      {
        key: 'sendToReceiveInformation', cssClass: {field: 'col-md-6 col-12'}
      },
      {
        key: 'rmtInformation', cssClass: {field: 'col-md-6 col-12'}
      },
      {
        key: 'dtAccount', cssClass: {field: 'col-md-6 col-12'}
      },
      {
        key: 'dtName', cssClass: {field: 'col-md-6 col-12'}
      },
      {
        key: 'dtAdd', cssClass: {field: 'col-md-6 col-12'}
      },
      {
        key: 'ctAccount', cssClass: {field: 'col-md-6 col-12', value: 'ct-account-value'},
      },
      {
        key: 'ctName', cssClass: {field: 'col-md-6 col-12'}
      },
      {
        key: 'ctNameInREB', cssClass: {field: 'col-md-6 col-12'}
      },
      {
        key: 'ctAdd', cssClass: {field: 'col-md-6 col-12'}
      },
      {
        key: 'state', cssClass: {field: 'col-md-6 col-12'}
      },
      {
        key: 'relatedReference', cssClass: {field: 'col-md-6 col-12'}
      },
      {
        key: 'checkNumber', cssClass: {field: 'col-md-6 col-12'}
      },
      {
        key: 'endrCount', cssClass: {field: 'col-md-6 col-12'}
      },
      {
        key: 'endrNames', cssClass: {field: 'col-md-6 col-12'}
      },
      {
        key: 'transferredCurrency',
        isHidden: true,
        cssClass: {field: 'col-md-6 col-12', value: 'danger-color', label: 'danger-color'},
        displayCellValueFunc: (val) => {
          if (val) {
            return val
          } else
            return '-'
        }
      },
    ],
    actionFields: [
      {
        label: 'Transfer',
        cssClass: 'btn-info',
        confirmationRequired: true,
        delegateFunction: (obj) => this.newIncomingTransfersService.transfer(obj.sygsRef).subscribe((res: any) => {
          if (res) {
            if (res.code == 0) {
              this.alertService.success(this.languageService.getLang('Transfer completed successfully'));
            } else {
              this.alertService.error(res.error);
            }
            this.dialog.closeAll()
          }
        }, (err) => {
          this.alertService.error(err.error.message)
        }),

      },
      {
        label: 'Reverse',
        cssClass: 'btn-danger',
        delegateFunction: (obj: any) => {
          this.dialog.open(ReverseIncomingTransferComponent, {
            width: '50%',
            panelClass: 'ttwr-dialog',
            data: obj.sygsRef
          })
        }
      },
      {
        label: 'Archive',
        cssClass: 'btn-info',
        confirmationRequired: true,
        delegateFunction: (obj) => this.newIncomingTransfersService.archive(obj.sygsRef).subscribe((res: any) => {
          if (res) {
            if (res.code == 0) {
              this.alertService.success(this.languageService.getLang('Transfer archived successfully'));
            } else {
              this.alertService.error(res.message);
            }
            this.dialog.closeAll()
          }
        }, (err) => {
          this.alertService.error(err.error.message)
        }),

      },
      {
        label: 'Cancel',
        cssClass: 'btn-danger',
        delegateFunction: () => {
          this.dialog.closeAll();
          ``
        }
      },

    ],
    fieldsDef: IncomingTransfersModel.getModelDef(),
  }

  constructor(@Inject(MAT_DIALOG_DATA) public data: any,
              private dialog: MatDialog, private newIncomingTransfersService: NewIncomingTransfersService,
              private alertService: AlertService,
              private languageService: LanguageService,) {
  }

  ngOnInit(): void {
    this.viewConfig.obj = this.data.obj;
  }

}
