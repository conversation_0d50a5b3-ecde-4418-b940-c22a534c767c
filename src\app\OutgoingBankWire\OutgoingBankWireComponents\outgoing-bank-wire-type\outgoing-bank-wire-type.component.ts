import {Component, OnInit} from '@angular/core';
import {ActivatedRoute, Router} from "@angular/router";
import {MatLegacyDialogRef as MatDialogRef} from "@angular/material/legacy-dialog";

@Component({
  selector: 'app-outgoing-bank-wire-type',
  templateUrl: './outgoing-bank-wire-type.component.html',
  styleUrls: ['./outgoing-bank-wire-type.component.sass']
})
export class OutgoingBankWireTypeComponent implements OnInit {

  dialogTitle: string = 'CreateNewOutgoingBankWire'


  openCustomerOutgoingBankWire() {
    this.dialogRef.close();
    this.router.navigate(['/OutgoingBankWire/outgoing-bank-wire-model/CustomerOutgoingBankWireModelCreate'])
  }

  openOutgoingBankWire() {
    this.dialogRef.close();
    this.router.navigate(['/OutgoingBankWire/outgoing-bank-wire-model/outgoingBankWireCreate'])
  }

  constructor(private router: Router, private activatedRoute: ActivatedRoute,
              public dialogRef: MatDialogRef<OutgoingBankWireTypeComponent>,
  ) {
  }

  ngOnInit(): void {
  }

}
