import {RouterModule, Routes} from "@angular/router";
import {NgModule} from "@angular/core";
import {IncomingTransfersMainComponent} from "./incoming-transfers-main/incoming-transfers-main.component";
import {IncomingTransfersModelComponent} from "./IncomingTransferComponents/incoming-transfers-model.component";
import {
  IncomingTransfersModelIndexComponent
} from "./IncomingTransferComponents/incoming-transfers-model-index/incoming-transfers-model-index.component";
import {
  IncomingTransfersModelViewComponent
} from "./IncomingTransferComponents/incoming-transfers-model-view/incoming-transfers-model-view.component";
import {InstitutionResolver} from "../services/InstitutionResolver";
import {
  NewIncomingTransfersIndexComponent
} from "./NewIncomingTransfersComponents/new-incoming-transfers-index/new-incoming-transfers-index.component";
import {NewIncomingTransfersComponent} from "./NewIncomingTransfersComponents/new-incoming-transfers.component";

const routes: Routes = [
  {
    path: 'incoming-transfers', component: IncomingTransfersMainComponent, children: [
      {
        path: 'incoming-transfers-model',
        component: IncomingTransfersModelComponent,
        data: {perms: {CLEAR_NAME: 'incoming-transfers-model'}},
        children:
          [
            {
              path: 'index', component: IncomingTransfersModelIndexComponent,
              data: {
                breadcrumb: 'IncomingTransfersReport',
                perms: {CLEAR_NAME: 'عرض'},
                permsChildren: [
                  {NAME: '/api/IncomingTransfersModel/delete', CLEAR_NAME: 'خدمة الحذف'},]
              },
            },
            {
              path: 'view/:id', component: IncomingTransfersModelViewComponent,
              resolve: {
                obj: InstitutionResolver,
              },
              data: {
                breadcrumb: 'IncomingTransfersModel',
                service_key: 'incoming-transfer-model',
                perms: {CLEAR_NAME: 'استعراض التفاصيل'},
                permsChildren: [{NAME: '/api/IncomingTransfersModel/view', CLEAR_NAME: 'خدمة استعراض التفاصيل'}]
              }
            },

          ],
      },
      {
        path: 'new-incoming-transfer',
        component: NewIncomingTransfersComponent,
        data: {perms: {CLEAR_NAME: 'incoming-transfers-model'}},
        children:
          [
            {
              path: 'index', component: NewIncomingTransfersIndexComponent,
              data: {
                breadcrumb: 'NewIncomingTransfersModel',
                perms: {CLEAR_NAME: 'عرض'},
                permsChildren: [
                  {NAME: '/api/IncomingTransfersModel/delete', CLEAR_NAME: 'خدمة الحذف'},]
              }
            },
            // {
            //   path: 'view/:id', component: NewIncomingTransfersViewComponent,
            //   // resolve: {
            //   //   obj: ObjectResolver,
            //   // },
            //   data: {
            //     service_key: 'incoming-transfers-model',
            //     perms: {CLEAR_NAME: 'استعراض التفاصيل'},
            //     permsChildren: [{NAME: '/api/IncomingTransfersModel/view', CLEAR_NAME: 'خدمة استعراض التفاصيل'}]
            //   }
            // },
          ],
      },

    ]
  }
]

@NgModule({
  imports: [
    RouterModule.forChild(routes)
  ],
  exports: [
    RouterModule
  ]
})

export class IncomingTransfersRouting {

}
