import {Component, OnInit} from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import {OutgoingBankWireModel} from '../../Models/OutgoingBankWire.model';
import {OutgoingBankWireService} from '../../Services/outgoing-bank-wire.service';
import {AlertService, FormDef, LanguageService} from '@ttwr-framework/ngx-main-visuals';
import {BanksService} from "../../../Banks/Services/banks.service";
import {Validators} from "@angular/forms";
import {ConfigService} from "../../../services/config.service";
import {formatDate} from "@angular/common";


@Component({
  selector: 'app-customer-outgoing-bank-wire-model-create',
  templateUrl: './CustomerOutgoing-bank-wire-create.component.html',
  styleUrls: ['./CustomerOutgoing-bank-wire-create.component.scss']
})
export class CustomerOutgoingBankWireCreateComponent implements OnInit {

  public config: FormDef = {
    title: 'CustomerOutgoingBankWireModelCreate',
    fields: [
      {
        key: 'customerNumber', onChange: () => {
          this.config.form?.get('dtAccount')?.setValue(this.bankService.accountNumber);
          this.config.form?.get('dtName')?.setValue(this.bankService.name);
          this.config.form?.get('dtAdd')?.setValue(this.bankService.address);
          this.config.form?.get('dtPhoneNumber')?.setValue(this.bankService.mobile);
        }
      },
      {key: 'dtAccount', readonly: true},
      {key: 'dtName'},
      {key: 'dtMotherName'},
      {key: 'dtNationalNumber'},
      {key: 'dtPhoneNumber'},
      {key: 'dtAdd'},
      {key: 'ctAccount'},
      {key: 'ctName'},
      {key: 'ctAdd'},
      {key: 'ctPhoneNumber'},
      {
        key: 'ttc', onChange: (val) => {
          let checkNumberField = this.config.fields.find(k => k.key == 'checkNumber')
          let endrNamesField = this.config.fields.find(k => k.key == 'endrNames')
          let endrCountsField = this.config.fields.find(k => k.key == 'endrCount')
          if (val == 'CQT') {
            checkNumberField!.validators?.push({
              name: 'required',
              validator: Validators.required,
              message: 'this field is required'
            })
            endrNamesField!.validators?.push({
              name: 'required',
              validator: Validators.required,
              message: 'this field is required'
            })
            endrCountsField!.validators?.push({
              name: 'required',
              validator: Validators.required,
              message: 'this field is required'
            })
          } else {
            checkNumberField!.validators = checkNumberField!.validators?.filter(x => x.name != 'required')
            endrNamesField!.validators = endrNamesField!.validators?.filter(x => x.name != 'required')
            endrCountsField!.validators = endrCountsField!.validators?.filter(x => x.name != 'required')
          }
        },
      },
      {key: 'payBankBIC'},
      {key: 'benefitBankBIC'},
      {key: 'benefitBranchBIC'},
      {key: 'payPartBIC', isHidden: true},
      {key: 'benefitPartBIC', isHidden: true},
      {key: 'insCurrency', isHidden: true},
      {
        key: 'insAmount', validators: [
          {
            name: 'required',
            validator: Validators.required,
            message: 'this field is required'
          },
          {
            name: 'min',
            validator: Validators.min(this.configService.configuration.MinimumInsAmount),
            message: this.languageService.getLang('this field must be bigger than ') + this.configService.configuration.MinimumInsAmount
          }
        ]
      },
      {key: 'exchangeRate'},
      {key: 'dtlChg', readonly: true, defaultValue: 'SHA'},
      {key: 'sendChangeAmount'},
      {key: 'receiveChangeAmount'},
      {key: 'priority'},
      {key: 'valueDate'},
      {key: 'sendReference'},
      {key: 'relatedReference'},
      {key: 'checkNumber'},
      {key: 'endrCount'},
      {key: 'endrNames'},
      {key: 'rmtInformation'},
      {key: 'sendToReceiveInformation'},
    ],
    fieldsDef: OutgoingBankWireModel.getModelDef(),
    actionFields: [
      {
        label: 'Submit',
        cssClass: 'btn-primary',
        delegateFunction: (obj) => {
          obj.insCurrency = "SYP"
          obj.valueDate = formatDate(new Date(obj.valueDate), 'yyyy-MM-dd', 'en')
          this.outgoingBankWireModelService.createCustomer(obj).subscribe((res: any) => {
            if (res) {
              if (res.code == 0) {
                this.alertService.success('Success Create');
                this.router.navigate(['view/' + res.data.outgoingTransferId], {relativeTo: this.activatedRoute.parent});
              } else {
                this.alertService.error(res.message)
              }
            }
          }, error => {
            this.alertService.error(error)
          })
        }
      },
      {
        label: 'Cancel',
        cssClass: 'btn-danger',
        causeValidate: false,
        delegateFunction: () => this.router.navigate(['index/'], {relativeTo: this.activatedRoute.parent}),
      }
    ]
  };

  constructor(private outgoingBankWireModelService: OutgoingBankWireService,
              private bankService: BanksService,
              private alertService: AlertService, private router: Router, private activatedRoute: ActivatedRoute,
              public configService: ConfigService,
              private languageService: LanguageService) {
  }

  ngOnInit() {

  }

}
