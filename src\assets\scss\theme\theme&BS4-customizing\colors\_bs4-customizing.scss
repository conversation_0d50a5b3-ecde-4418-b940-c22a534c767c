@use 'sass:map';
@use '@angular/material' as mat;

$theme-colors: (
  'info': mat.get-color-from-palette(map.get($my-app-theme, accent), 500),
  'primary': mat.get-color-from-palette(map.get($my-app-theme, primary), 500),
  //"secondary": #018ABC,
  'success': mat.get-color-from-palette($mat-green-base, 500),
  'warning': mat.get-color-from-palette($mat-yellow-base, 500),
  'danger': mat.get-color-from-palette(map.get($my-app-theme, warn), 500),
);
$theme-colors-rgb: map-loop($theme-colors, to-rgb, "$value");
//$utilities-colors: map-merge($utilities-colors, $theme-colors-rgb);
//$utilities-text-colors: map-loop($utilities-colors, rgba-css-var, "$key", "text");
//$utilities-bg-colors: map-loop($utilities-colors, rgba-css-var, "$key", "bg");

$body-color: mat.get-color-from-palette(map.get($my-app-theme, accent), 500);
$link-color: $info;
$link-hover-color: $primary;
$color-contrast-dark: $info;
$btn-focus-box-shadow: null;
$btn-focus-width: 0;
$btn-box-shadow: null;
