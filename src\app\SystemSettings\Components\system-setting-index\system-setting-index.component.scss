.table {
  --table-color-type: initial;
  --table-bg-type: initial;
  --table-color-state: initial;
  --table-bg-state: initial;
  --table-accent-bg: transparent;
  width: 100%;
  margin-bottom: 1rem;
  vertical-align: top;
  text-align: center;

  thead {
    vertical-align: bottom;

    th {
      font-size: 0.8rem;
    }
  }

  tbody {
    vertical-align: center;
  }

  > :not(caption) > * > * {
    padding: 0.5rem 0.5rem;
    color: var(--table-color-state, var(--table-color-type, var(--table-color)));
    background-color: var(--table-bg);
    box-shadow: inset 0 0 0 9999px var(--table-bg-state, var(--table-bg-type, var(--table-accent-bg)));
  }
}

mat-expansion-panel-header {
  margin-right: 10px;
}

.btn-edit-icon {
  background-color: transparent;
  border: none;

  img {
    width: 1.4rem;
  }
}

.important-msg{
  color: #ff00008a;
  margin-bottom: 2rem;
}
