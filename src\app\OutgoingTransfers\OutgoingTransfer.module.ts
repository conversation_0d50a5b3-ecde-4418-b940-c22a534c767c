import {NgModule} from "@angular/core";
import {OutgoingTransferMainComponent} from './outgoing-transfer-main/outgoing-transfer-main.component';
import {OutgoingTransferRouting} from "./OutgoingTransfer.routing";
import {SharedModule} from "../shared/shared.module";
import {
  OutgoingTransfersModelIndexComponent
} from "./OutgoingTransfersComponents/outgoing-transfers-model-index/outgoing-transfers-model-index.component";
import {OutgoingTransfersModelComponent} from "./OutgoingTransfersComponents/outgoing-transfers-model.component";
import {
  OutgoingTransfersModelViewComponent
} from "./OutgoingTransfersComponents/outgoing-transfers-model-view/outgoing-transfers-model-view.component";
import {OutgoingTransfersModelService} from "./Services/outgoing-transfers-model.service";
import {FormsModule} from "@angular/forms";


@NgModule({
  imports: [
    SharedModule,
    OutgoingTransferRouting,
    FormsModule
  ],
  exports: [],
  declarations: [
    OutgoingTransferMainComponent,
    OutgoingTransfersModelIndexComponent,
    OutgoingTransfersModelComponent,
    OutgoingTransfersModelViewComponent,
  ],
  providers: [
    OutgoingTransfersModelService
  ]
})

export class OutgoingTransferModule {

}
