import {Component, Inject, OnInit} from '@angular/core';
import {AlertService, FormDef, Helper} from '@ttwr-framework/ngx-main-visuals';
import {MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA, MatLegacyDialogRef as MatDialogRef} from '@angular/material/legacy-dialog';
import {UserGroup} from '../../models/user-group.model';
import {UserGroupService} from '../../services/user-group.service';

@Component({
  selector: 'app-add-group-roles',
  templateUrl: './add-group-roles.component.html',
  styleUrls: ['./add-group-roles.component.scss'],
})
export class AddGroupRolesComponent implements OnInit {
  dialogTitle='Add roles';
  public config: FormDef = {
    titleHidden:true,
    fields: [{key: '_UserGroupRoles'}],
    fieldsDef: UserGroup.getModelDef(),
    obj: {},
    actionFields: [
      {
        label: 'Submit',
        delegateFunction: (obj) =>
          this.userGroupService.addRoles(this.data.obj.Id, obj).subscribe(() => {
            this.alertService.success('Success Update');
            this.dialogRef.close(true);
          }),
      },
      {
        label: 'Cancel',
        cssClass: 'btn-danger',
        causeValidate: false,
        delegateFunction: () => this.dialogRef.close(),
      },
    ],
  };

  constructor(
    public dialogRef: MatDialogRef<AddGroupRolesComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private userGroupService: UserGroupService,
    private alertService: AlertService,
  ) {
  }

  ngOnInit() {

  }
}
