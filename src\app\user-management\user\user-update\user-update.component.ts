import {Component, Inject, OnInit} from '@angular/core';
import {UserService} from '../../services/user.service';
import {MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA, MatLegacyDialog as MatDialog, MatLegacyDialogRef as MatDialogRef} from '@angular/material/legacy-dialog';
import { AlertService, FormDef, LanguageService } from '@ttwr-framework/ngx-main-visuals';
import {UserRoleModel} from "../../models/user-role.model";
import {AbstractControl, UntypedFormBuilder, UntypedFormGroup, ValidationErrors, Validators} from "@angular/forms";

@Component({
  selector: 'app-user-update',
  templateUrl: './user-update.component.html',
  styleUrls: ['./user-update.component.scss'],
})
export class UserUpdateComponent implements OnInit {
  form: UntypedFormGroup;
  startWorkingHour: any;
  EndWorkingHour: any;
  doneLoading = false;

  selectedRole: string[] = [];
  dialogTitle = 'User Update';
  public config: FormDef = {
    titleHidden: true,
    fields: [
      {key: 'username'},
      {key: 'employeeId'},
      // {key: 'newPassword'},
      {key: 'role'},
      {key: 'isInactive'},
      {key: 'branchIdentificationCode'},
    ],
    fieldsDef: UserRoleModel.getModelDef(),
    obj: {},
    actionFields: [
      {
        label: 'Submit',
        delegateFunction: (obj) => {
          if (this.form.invalid) {
            if (this.form.hasError('invalidTimeRange')) {
              this.alertService.error('Start time must be earlier than end time')
            }
            this.form.markAllAsTouched()
            return
          }
          obj.workingStartHour = this.startWorkingHour
          obj.workingEndHour = this.EndWorkingHour
          obj.isInactive = !obj.isInactive;
          this.userService.updateWithStringId(this.data.obj?.['id'], obj).subscribe((res: any) => {
            if (res.code == 0) {
              this.alertService.success('Success Update');
              this.dialogRef.close(true);
            } else {
              this.alertService.error(res.message);
              this.dialogRef.close(true);
            }
          }, (err) => {
            this.alertService.error(err)
          })
        }
      },
      {
        label: 'Cancel',
        cssClass: 'btn-danger',
        causeValidate: false,
        delegateFunction: () => this.dialogRef.close(),
      },
    ],
  };

  constructor(
    private fb: UntypedFormBuilder,
    public dialogRef: MatDialogRef<UserUpdateComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private dialog: MatDialog,
    private userService: UserService,
    private alertService: AlertService,
    private languageService: LanguageService,
  ) {
    this.form = this.fb.group({
        startWorkingHour: ['', Validators.required],
        endWorkingHour: ['', Validators.required],
      },
      {validators: startEndHourValidator}
    );
  }


  ngOnInit() {
    this.userService.getUserById(this.data.obj['id']).subscribe((res) => {
      this.config.obj.username = res.username;
      this.config.obj.employeeId = res.employeeId;
      this.config.obj.isInactive = !res.isInactive;
      this.config.obj.branchIdentificationCode = res.branchIdentificationCode;
      this.config.obj.branchIdentificationName = this.languageService.selectedLanguage === 'ar'
        ? res.branchIdentificationNameAr
        : res.branchIdentificationNameEn;

      this.startWorkingHour = res.workingStartHour;
      this.EndWorkingHour = res.workingEndHour;
      if (res.userRoles != null && res.userRoles.length > 0) {
        res.userRoles.forEach((item: any) => {
          this.selectedRole.push(item.role.id);
        })
        this.config.obj.role = this.selectedRole;
      }
      this.doneLoading = true;
    })
  }
}

export function startEndHourValidator(control: AbstractControl): ValidationErrors | null {
  const start = control.get('startWorkingHour')?.value;
  const end = control.get('endWorkingHour')?.value;

  if (start && end && start >= end) {
    return {'invalidTimeRange': true};
  }
  return null;
}
