<mat-toolbar color="accent">
  <ul class="navigation-items justify-content-start">
    <li>
      <button
        type="button"
        aria-label="Toggle sidenav"
        mat-icon-button
        (click)="sidenav.toggle()">
        <mat-icon aria-label="Side nav toggle icon">menu</mat-icon>
      </button>
    </li>
    <li>
      <a routerLink="/dashboard" class="link-design">
        <img class="logo logo-size" src="{{logo.url}}"/>
        <label>{{title | i18n }}</label>
      </a>
    </li>

  </ul>
  <ul class="navigation-items justify-content-end icons-position">
    <li>
      <a routerLink="/dashboard">
        <svg-icon src="assets/icons/menu/ic_dashboard.svg"></svg-icon>
      </a>
    </li>
    <li class="li-border">
      <mat-menu #appMenu="matMenu" xPosition="before">
        <button (click)="onChangeLanguage(lang.value)" *ngFor="let lang of languages" mat-menu-item>
          {{lang.name}}
        </button>
      </mat-menu>

      <a [matMenuTriggerFor]="appMenu">
        <svg-icon src="assets/icons/menu/ic_language.svg"></svg-icon>
      </a>
    </li>
    <li>
      <a [matMenuTriggerFor]="appMenuUser" class="user-link">
        <!--        <label class="px-2" fxHide.lt-sm> {{userService.username}}</label>-->
        <svg-icon src="assets/icons/person.svg"></svg-icon>
        <p class="user-name-design">{{user}}</p>
      </a>
      <mat-menu #appMenuUser="matMenu" xPosition="before">
        <button (click)="logout()" mat-menu-item>
          <span class="label">{{'logout' | i18n }}</span>
        </button>
      </mat-menu>
    </li>
  </ul>
</mat-toolbar>
<mat-sidenav-container [class.isPrinting]="printService.isPrinting">
  <!--  <button-->
  <!--    type="button"-->
  <!--    aria-label="Toggle sidenav"-->
  <!--    mat-icon-button-->
  <!--    (click)="sidenav.toggle()">-->
  <!--    <mat-icon aria-label="Side nav toggle icon">menu</mat-icon>-->
  <!--  </button>-->

  <mat-sidenav #sidenav role="navigation"
               [mode]="$any(sidenavPosition)" [opened]="isSidenavOpen" autosize
               [ngClass]="(sideNavIsExpanded? 'expanded-sidenav' : 'mini-sidenav')"
               class="mat-elevation-z8 sidenav-style">
    <div class="sidenav-header">
      <a routerLink="/dashboard">
      </a>
    </div>
    <app-side-menu [items]="menuItems"
                   [isExpanded]="sideNavIsExpanded"
                   [sidenavPosition]="sidenavPosition"
                   [ref]="sidenav">
    </app-side-menu>
  </mat-sidenav>
  <!--Sidenav content-->
  <mat-sidenav-content>
    <main>
      <router-outlet></router-outlet>
    </main>
  </mat-sidenav-content>
</mat-sidenav-container>
<footer class="footer">
  <div class="footer-info">
    <span class="">
        {{description|i18n}}
      <p class="c_right">{{('REB'|i18n) + (' - ') + ('All rights reserved' | i18n) + ' ' + currentYear}} </p>
      </span>
  </div>
  <div class="footer-copyright">
    <div><a class="ttwr"
            href="https://tatweer.sy/"
            target="_blank">{{'Powered by Tatweer' | i18n}}</a>
    </div>
    <span>
      <img class="logo-footer" src="{{footerLogo.url}}"/>
    </span>
  </div>
</footer>
<router-outlet name="print"></router-outlet>


