import {Model, TtwrInputComponent, Types} from "@ttwr-framework/ngx-main-visuals";


export class BankCustomerInformationModel extends Model {
  name!: string;
  firstName!: string;
  lastName!: string;
  middleName!: string;
  address!: string;
  mobile!: string;


  protected static initializeModelDef() {
    return {
      defs: {
        name: {
          ID: 'name',
          dataType: Types.STRING,
          label: 'name',
          ui: TtwrInputComponent
        },
        firstName: {
          ID: 'firstName',
          dataType: Types.STRING,
          label: 'firstName',
          ui: TtwrInputComponent
        },
        lastName: {
          ID: 'lastName',
          dataType: Types.STRING,
          label: 'lastName',
          ui: TtwrInputComponent
        },
        middleName: {
          ID: 'middleName',
          dataType: Types.STRING,
          label: 'middleName',
          ui: TtwrInputComponent
        },
        address: {
          ID: 'address',
          dataType: Types.STRING,
          label: 'address',
          ui: TtwrInputComponent
        },
        mobile: {
          ID: 'mobile',
          dataType: Types.STRING,
          label: 'mobile',
          ui: TtwrInputComponent
        }
      }
    }
  }
}
