import {Component, OnInit} from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import {Banks} from '../../Models/Banks.model';
import {BanksService} from '../../Services/banks.service';
import {AlertService,Helper, FormDef} from '@ttwr-framework/ngx-main-visuals';

@Component({
  selector: 'app-banks-update',
  templateUrl: './banks-update.component.html',
  styleUrls: ['./banks-update.component.scss']
})
export class BanksUpdateComponent implements OnInit {

  public config: FormDef = {
    title: 'Banks Update',
    fields: [
      {key: 'bankENName'},
      {key: 'bankARName'},
      {key: 'bankIdentityCode'},
    ],
    fieldsDef: Banks.getModelDef(),
    actionFields: [
      {
        label: 'Submit',
        cssClass: 'btn-primary',
        delegateFunction: (obj) => this.banksService.update(this.config.obj?.['Id'], obj).subscribe(() => {
          this.alertService.success('Success Update');
          this.router.navigate(['view/' + this.config.obj?.['Id']], {relativeTo: this.activatedRoute.parent});
        }),
      },
      {
        label: 'Cancel',
        cssClass: 'btn-danger',
        causeValidate: false,
        delegateFunction: () => this.router.navigate(['view/' + this.config.obj?.['Id']], {relativeTo: this.activatedRoute.parent}),
      }
    ]
  };

  constructor(private route: ActivatedRoute, private router: Router,
              private banksService: BanksService, private alertService: AlertService, private activatedRoute: ActivatedRoute) {
  }

  ngOnInit() {
     if(this.route.snapshot.data.obj)
       this.config.obj =Helper.deepCopy(this.route.snapshot.data.obj)
  }

}
