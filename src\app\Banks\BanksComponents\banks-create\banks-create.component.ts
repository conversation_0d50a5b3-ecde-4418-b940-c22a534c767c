import {Component} from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import {Banks} from '../../Models/Banks.model';
import {BanksService} from '../../Services/banks.service';
import {AlertService, FormDef} from '@ttwr-framework/ngx-main-visuals';

@Component({
  selector: 'app-banks-create',
  templateUrl: './banks-create.component.html',
  styleUrls: ['./banks-create.component.scss']
})
export class BanksCreateComponent {

  public config: FormDef = {
    title: 'Banks Create',
    fields: [
      {key: 'bankENName'},
      {key: 'bankARName'},
      {key: 'bankIdentityCode'},
    ],
    fieldsDef: Banks.getModelDef(),
    actionFields: [
      {
        label: 'Submit',
        cssClass: 'btn-primary',
        delegateFunction: (obj) => this.banksService.create(obj).subscribe((res:any) => {
          this.alertService.success('Success Create');
          this.router.navigate(['view/' + res.data.objList[0].Id], {relativeTo: this.activatedRoute.parent});
        }),
      },
      {
        label: 'Cancel',
        cssClass: 'btn-danger',
        causeValidate: false,
        delegateFunction: () => this.router.navigate(['index/'], {relativeTo: this.activatedRoute.parent}),
      }
    ]
  };

  constructor(private banksService: BanksService,
              private alertService: AlertService, private router: Router, private activatedRoute: ActivatedRoute) {
  }

}
