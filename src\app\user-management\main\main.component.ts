import {Component} from '@angular/core';

@Component({
  selector: 'app-main',
  templateUrl: './main.component.html',
  styleUrls: ['./main.component.scss'],
})
export class MainComponent {
  menuItems = [
    // {
    //   id: 'Users_Roles',
    //   label: 'Users & Roles',
    //   visible: true,
    //   items: [
    //     {
    //       label: 'Users',
    //       routerLink: '/users-managements/users/index',
    //       visible: true,
    //     },
    //     {
    //       label: 'User groups',
    //       routerLink: '/users-managements/user-group/index',
    //       visible: true,
    //     },
    //     {
    //       label: 'Roles',
    //       routerLink: '/users-managements/role/index',
    //       visible: true,
    //     },
    //   ],
    // },
    // {
    //   id: 'Permissions',
    //   label: 'Permissions',
    //   visible: true,
    //   items: [
    //     {
    //       label: 'Permission Tree',
    //       routerLink: '/users-managements/permission/index',
    //       visible: true,
    //     },
    //   ],
    // },
  ];
}
