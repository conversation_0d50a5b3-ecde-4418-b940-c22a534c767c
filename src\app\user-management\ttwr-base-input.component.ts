/*
 * Copyright (c) 2019. This template belongs to Tatweer LLC.
 * Any use of this template must be authorized by the owner company.
 * The distribution of this product is prohibited under any circumstances.
 */

import {Directive, Input, OnInit} from '@angular/core';
import {UntypedFormGroup, Validators} from '@angular/forms';
import {DataCol, Helper} from "@ttwr-framework/ngx-main-visuals";



@Directive({
  selector: '[ttwrBaseInput]'
})
export class TtwrBaseInputComponent implements OnInit {
  @Input() field!: DataCol;
  @Input() group!: UntypedFormGroup;
  @Input() formFields!: DataCol[];
  @Input() config!: any;

  ngOnInit() {
    if (this.field && !this.field.uiObject) {
      this.field.uiObject = this;
    }
  }

  public isRequired(validators: Validators[]) {
    return Helper.getRequiredValidator(validators);
  }
}
