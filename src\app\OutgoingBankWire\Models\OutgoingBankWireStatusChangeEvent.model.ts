import {Model, TtwrInputComponent, TtwrInputDateTimeComponent, Types} from "@ttwr-framework/ngx-main-visuals";


export class OutgoingBankWireStatusChangeEventModel extends Model {
  eventId!: string;
  eventName!: string;
  executeDate!: Date;
  correctionReason!: string;
  exception!: string;
  employeeId!: string;
  isSuccess!: number;
  employeeName!:string;
  xapiReferenceNumber!: string;


  protected static initializeModelDef() {
    return {
      defs: {
        eventId: {
          ID: 'eventId',
          dataType: Types.STRING,
          label: 'eventId',
          ui: TtwrInputComponent
        },
        employeeName: {
          ID: 'employeeName',
          dataType: Types.STRING,
          label: 'employeeName',
          ui: TtwrInputComponent
        },
        eventName: {
          ID: 'eventName',
          dataType: Types.STRING,
          label: 'eventName',
          ui: TtwrInputComponent
        },
        executeDate: {
          ID: 'executeDate',
          dataType: Types.DATETIME,
          label: 'executeDate',
          ui: TtwrInputDateTimeComponent
        },
        correctionReason: {
          ID: 'correctionReason',
          dataType: Types.STRING,
          label: 'correctionReason',
          ui: TtwrInputComponent
        },
        exception: {
          ID: 'exception',
          dataType: Types.STRING,
          label: 'exception',
          ui: TtwrInputComponent
        },
        employeeId: {
          ID: 'employeeId',
          dataType: Types.STRING,
          label: 'employeeId',
          ui: TtwrInputComponent
        },
        isSuccess: {
          ID: 'isSuccess',
          dataType: Types.NUMBER,
          label: 'isSuccess',
          ui: TtwrInputComponent
        },
        xapiReferenceNumber: {
          ID: 'xapiReferenceNumber',
          dataType: Types.STRING,
          label: 'xapiReferenceNumber',
          ui: TtwrInputComponent
        },
      }
    }
  }
}
