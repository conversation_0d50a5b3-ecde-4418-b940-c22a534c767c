import {Injectable} from '@angular/core';
import {Filter, Model, Sort} from "@ttwr-framework/ngx-main-visuals";
import {Observable} from "rxjs";
import {SYGSBaseService} from "../../services/SYGSBaseService";
import {map} from "rxjs/operators";

@Injectable()
export class NewIncomingTransfersService extends SYGSBaseService {

  protected baseName = 'IncomingBankWire';


  transfer(sygsRef: string): Observable<Model> {
    return this.http.post<Model>(`/api/` + this.baseName + `/AddNewIncomingBankWire`, {
      sygsReference: sygsRef
    });
  }

  reverse(sygsRef: string, reason: string): Observable<Model> {
    return this.http.post<Model>(`/api/` + this.baseName + `/ReverseIncomingBankWire`, {
      sygsReference: sygsRef,
      reason: reason
    });
  }

  archive(sygsRef: string): Observable<Model> {
    return this.http.post<Model>(`/api/` + this.baseName + `/ArchiveIncomingBankWire`, {
      sygsReference: sygsRef
    });
  }


  index(
    filter: Filter[] = [],
    sort: Sort[] = [],
    pageNumber = 0,
    pageSize = 5
  ): Observable<Model[]> {
    const params = this.getGridParams(filter, sort, pageNumber, pageSize);
    return this.http.get<Model[]>(`/api/` + this.baseName + `/GetNewIncomingBankWires`, {
      params: params,
    }).pipe(map((res: any) => {
      res.items.forEach((obj: any) => {
        obj["sygsRef"] = obj['sygs']?.toString();
      })
      var newObj: any = {
        Items: res.items,
        PageSize: res.pageSize,
        TotalCount: res.totalCount
      }
      return newObj;
    }))
  };
}
