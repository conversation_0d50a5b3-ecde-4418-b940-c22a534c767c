import {Model, TtwrInputComponent, Types} from "@ttwr-framework/ngx-main-visuals";
import {Validators} from "@angular/forms";

export class BankWireType extends Model {
  type!: string;
  enName!: string;
  arName!: string;


  protected static initializeModelDef() {
    return {
      defs: {
        type: {
          ID: 'type',
          dataType: Types.STRING,
          label: 'type',
          ui: TtwrInputComponent,
          validators: [
            {
              name: 'required',
              validator: Validators.required,
              message: 'this field is required'
            }
          ]
        },
        enName: {
          ID: 'enName',
          dataType: Types.STRING,
          label: 'enName',
          ui: TtwrInputComponent,
          Validators: [
            {
              name: 'required',
              validator: Validators.required,
              message: 'this field id required'
            }
          ]
        },
        arName: {
          ID: 'arName',
          dataType: Types.STRING,
          label: 'arName',
          ui: TtwrInputComponent,
          validators: [
            {
              name: 'required',
              validator: Validators.required,
              message: 'this field id required'
            }
          ]
        }
      }
    }
  }
}
