stages:          
  - build_UAT
  - build_Live

UAT-Build-Job:
  stage: build_UAT
  tags:
    - Dev03-shell
  only: 
    - UAT
  script:
    - rm -rf ./UAT_Builds
    - npm install --legacy-peer-deps
    - node node_modules/@angular/cli/bin/ng build --output-path UAT_Builds
    - rm -f ./UAT_Builds/assets/config/config.json /var/www/html/sygs/uat_builds/sygs_front_uat.tar.gz
    - tar czvf /var/www/html/sygs/uat_builds/sygs_front_uat.tar.gz UAT_Builds

Live-Build-Job:
  stage: build_Live
  tags:
    - Dev03-shell
  only: 
    - Live
  script:
    - rm -rf ./Live_Builds
    - npm install --legacy-peer-deps
    - node node_modules/@angular/cli/bin/ng build --output-path Live_Builds
    - rm -f ./Live_Builds/assets/config/config.json /var/www/html/sygs/live_builds/sygs_front_live.tar.gz
    - tar czvf /var/www/html/sygs/live_builds/sygs_front_live.tar.gz Live_Builds

