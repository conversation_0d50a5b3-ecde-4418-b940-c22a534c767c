import {Component} from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import {BankWireType} from '../../Models/BankWireType.model';
import {BankWireTypeService} from '../../Services/bank-wire-type.service';
import {AlertService, FormDef} from '@ttwr-framework/ngx-main-visuals';

@Component({
  selector: 'app-bank-wire-type-create',
  templateUrl: './bank-wire-type-create.component.html',
  styleUrls: ['./bank-wire-type-create.component.scss']
})
export class BankWireTypeCreateComponent {

  public config: FormDef = {
    title: 'BankWireType Create',
    fields: [
      {key: 'type'},
      {key: 'enName'},
      {key: 'arName'},
    ],
    fieldsDef: BankWireType.getModelDef(),
    actionFields: [
      {
        label: 'Submit',
        cssClass: 'btn-primary',
        delegateFunction: (obj) => this.bankWireTypeService.create(obj).subscribe((res:any) => {
          this.alertService.success('Success Create');
          this.router.navigate(['view/' + res.data.objList[0].Id], {relativeTo: this.activatedRoute.parent});
        }),
      },
      {
        label: 'Cancel',
        cssClass: 'btn-danger',
        causeValidate: false,
        delegateFunction: () => this.router.navigate(['index/'], {relativeTo: this.activatedRoute.parent}),
      }
    ]
  };

  constructor(private bankWireTypeService: BankWireTypeService,
              private alertService: AlertService, private router: Router, private activatedRoute: ActivatedRoute) {
  }

}
