import {RouterModule, Routes} from "@angular/router";
import {NgModule} from "@angular/core";
import {OutgoingTransferMainComponent} from "./outgoing-transfer-main/outgoing-transfer-main.component";
import {OutgoingTransfersModelComponent} from "./OutgoingTransfersComponents/outgoing-transfers-model.component";
import {
  OutgoingTransfersModelIndexComponent
} from "./OutgoingTransfersComponents/outgoing-transfers-model-index/outgoing-transfers-model-index.component";
import {
  OutgoingTransfersModelViewComponent
} from "./OutgoingTransfersComponents/outgoing-transfers-model-view/outgoing-transfers-model-view.component";
import {InstitutionResolver} from "../services/InstitutionResolver";

const routes: Routes = [
  {
    path: 'outgoing-transfers', component: OutgoingTransferMainComponent, children: [
      {
        path: 'outgoing-transfers-model',
        component: OutgoingTransfersModelComponent,
        data: {perms: {CLEAR_NAME: 'outgoing-transfers-model'}},
        children:
          [
            {
              path: 'index', component: OutgoingTransfersModelIndexComponent,
              data: {
                breadcrumb: 'OutgoingTransfersReport',
                perms: {CLEAR_NAME: 'عرض'},
                permsChildren: [
                  {NAME: '/api/OutgoingTransfersModel/delete', CLEAR_NAME: 'خدمة الحذف'},]
              },
            },
            {
              path: 'view/:id', component: OutgoingTransfersModelViewComponent,
              resolve: {
                obj: InstitutionResolver,
              },
              data: {
                breadcrumb: 'OutgoingTransfersModel',
                service_key: 'outgoing-transfer-model',
                perms: {CLEAR_NAME: 'استعراض التفاصيل'},
                permsChildren: [{NAME: '/api/OutgoingTransfersModel/view', CLEAR_NAME: 'خدمة استعراض التفاصيل'}]
              }
            },

          ],
      },

    ]
  }
]

@NgModule({
  imports: [
    RouterModule.forChild(routes)
  ],
  exports: [
    RouterModule
  ]
})

export class OutgoingTransferRouting {

}
