import {Filter, FilterOperations} from "@ttwr-framework/ngx-main-visuals";

export class FilterService {
  isSuccess: boolean|null = null;
  payBankBIC!: string;
  sygsRef!: string;
  fromDate!: string | null;
  toDate!: string | null;
  transferType!: string;
  benefitBankBIC!: string;
  lastState!: string;
  addedAt!: string | null;
  insAmount!: string;
  dtAccount!: string;
  dtName!: string;
  ctAccount!: string;
  ctName!: string;
  username!: string;
  branchIdentificationCode!: string;
  payBranchBIC!: string;

  filters: Filter[] = [];

  createFilter(): Filter[] {
    this.filters = [];
    if (this.isSuccess != null) {
      let isSuccessFilter = new Filter();
      isSuccessFilter.value = this.isSuccess;
      isSuccessFilter.operation = FilterOperations.Equal;
      isSuccessFilter.attribute = 'operations.isSuccess';
      this.filters.push(isSuccessFilter)
    }
    if (this.sygsRef != null) {
      let sygsRefFilter = new Filter();
      sygsRefFilter.value = this.sygsRef;
      sygsRefFilter.operation = FilterOperations.Like;
      sygsRefFilter.attribute = 'sygsRef';
      this.filters.push(sygsRefFilter)
    }
    if (this.benefitBankBIC != null) {
      let payBankBICFilter = new Filter();
      payBankBICFilter.value = this.benefitBankBIC;
      payBankBICFilter.operation = FilterOperations.Like;
      payBankBICFilter.attribute = 'benefitBankBIC';
      this.filters.push(payBankBICFilter)
    }
    if (this.fromDate != null) {
      let fromDateFilter = new Filter();
      fromDateFilter.value = this.fromDate;
      fromDateFilter.operation = FilterOperations.MoreThanOrEqual;
      fromDateFilter.attribute = 'FromDate';
      this.filters.push(fromDateFilter)
    }
    if (this.toDate != null) {
      let toDateFilter = new Filter();
      toDateFilter.value = this.toDate;
      toDateFilter.operation = FilterOperations.LessThan;
      toDateFilter.attribute = 'ToDate';
      this.filters.push(toDateFilter)
    }
    if (this.transferType != null) {
      let transferTypeFilter = new Filter();
      transferTypeFilter.value = this.transferType;
      transferTypeFilter.operation = FilterOperations.Equal;
      transferTypeFilter.attribute = 'operations.operationName';
      this.filters.push(transferTypeFilter)
    }
    if (this.payBankBIC != null) {
      let payBankBICFilter = new Filter();
      payBankBICFilter.value = this.payBankBIC;
      payBankBICFilter.operation = FilterOperations.Like;
      payBankBICFilter.attribute = 'payBankBIC';
      this.filters.push(payBankBICFilter)
    }

    if (this.lastState != null) {
      let lastStateFilter = new Filter();
      lastStateFilter.value = this.lastState;
      lastStateFilter.operation = FilterOperations.Like;
      lastStateFilter.attribute = 'lastState';
      this.filters.push(lastStateFilter)
    }

    if (this.addedAt != null) {
      let addedAtFilter = new Filter();
      addedAtFilter.value = this.addedAt;
      addedAtFilter.operation = FilterOperations.Equal;
      addedAtFilter.attribute = 'addedAt';
      this.filters.push(addedAtFilter)
    }

    if (this.insAmount) {
      let insAmountFilter = new Filter();
      insAmountFilter.value = this.insAmount;
      insAmountFilter.operation = FilterOperations.Equal;
      insAmountFilter.attribute = 'insAmount';
      this.filters.push(insAmountFilter)
    }

    if (this.dtAccount != null) {
      let dtAccountFilter = new Filter();
      dtAccountFilter.value = this.dtAccount;
      dtAccountFilter.operation = FilterOperations.Like;
      dtAccountFilter.attribute = 'dtAccount';
      this.filters.push(dtAccountFilter)
    }

    if (this.dtName != null) {
      let dtNameFilter = new Filter();
      dtNameFilter.value = this.dtName;
      dtNameFilter.operation = FilterOperations.Like;
      dtNameFilter.attribute = 'dtName';
      this.filters.push(dtNameFilter)
    }

    if (this.ctAccount != null) {
      let ctAccountFilter = new Filter();
      ctAccountFilter.value = this.ctAccount;
      ctAccountFilter.operation = FilterOperations.Like;
      ctAccountFilter.attribute = 'ctAccount';
      this.filters.push(ctAccountFilter)
    }


    if (this.ctName != null) {
      let ctNameFilter = new Filter();
      ctNameFilter.value = this.ctName;
      ctNameFilter.operation = FilterOperations.Like;
      ctNameFilter.attribute = 'ctName';
      this.filters.push(ctNameFilter)
    }

    if (this.username != null) {
      let usernameFilter = new Filter();
      usernameFilter.value = this.username;
      usernameFilter.operation = FilterOperations.Like;
      usernameFilter.attribute = 'username';
      this.filters.push(usernameFilter)
    }

    if (this.branchIdentificationCode != null) {
      let branchIdentificationCodeFilter = new Filter();
      branchIdentificationCodeFilter.value = this.branchIdentificationCode;
      branchIdentificationCodeFilter.operation = FilterOperations.Equal;
      branchIdentificationCodeFilter.attribute = 'branchIdentificationCode';
      this.filters.push(branchIdentificationCodeFilter)
    }

    if (this.payBranchBIC != null) {
      let payBranchBICFilter = new Filter();
      payBranchBICFilter.value = this.payBranchBIC;
      payBranchBICFilter.operation = FilterOperations.Equal;
      payBranchBICFilter.attribute = 'payBranchBIC';
      this.filters.push(payBranchBICFilter)
    }

    return this.filters;
  }
}
