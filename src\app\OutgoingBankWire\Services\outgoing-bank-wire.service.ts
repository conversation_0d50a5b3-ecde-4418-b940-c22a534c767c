import {Injectable} from '@angular/core';
import {SYGSBaseService} from "../../services/SYGSBaseService";
import {Filter, Model, Sort} from "@ttwr-framework/ngx-main-visuals";
import {Observable} from "rxjs";
import {map} from "rxjs/operators";
import {HttpParams} from "@angular/common/http";

@Injectable()
export class OutgoingBankWireService extends SYGSBaseService {

  protected baseName = 'OutgoingBankWire';

  updateCustomerWithStringID(id: string, obj: any): Observable<Model> {
    if (obj.sendChangeAmount == null) {
      obj.sendChangeAmount = 0
    }
    if (obj.receiveChangeAmount == null) {
      obj.receiveChangeAmount = 0
    }
    obj['outgoingTransferId'] = id;
    return this.http.put<Model>(`/api/` + this.baseName + `/UpdateCustomerBankWire`, obj);
  }

  updateWithStringID(id: string, obj: any): Observable<Model> {
    obj['outgoingTransferId'] = id;
    return this.http.put<Model>(`/api/` + this.baseName + `/UpdateBankWire`, obj);
  }

  createCustomer(obj: any): Observable<Model> {
    if (obj.sendChangeAmount == null) {
      obj.sendChangeAmount = 0
    }
    if (obj.receiveChangeAmount == null) {
      obj.receiveChangeAmount = 0
    }
    return this.http.post<Model>(`/api/` + this.baseName + `/CreateCustomerBankWireAsInitial`, obj);
  }

  create(obj: any): Observable<Model> {
    if (obj.sendChangeAmount == null) {
      obj.sendChangeAmount = 0
    }
    if (obj.receiveChangeAmount == null) {
      obj.receiveChangeAmount = 0
    }
    return this.http.post<Model>(`/api/` + this.baseName + `/CreateBankWireAsInitial`, obj);
  }


  viewAsString(id: string): Observable<Model> {
    return this.http.get<Model>(`/api/` + this.baseName + `/GetOutgoingBankWire/` + id).pipe(map((res: any) => {
      if (res) {
        if (res.data) {
          return res.data
        }
      }
    }));
  }

  audit(id: string): Observable<Model> {
    return this.http.put<Model>(`/api/` + this.baseName + `/Audit/` + id, null);
  }

  revision(id: string, reason: string): Observable<Model> {
    return this.http.put<Model>(`/api/` + this.baseName + `/Revision`, {
      outgoingTransferId: id,
      reason: reason
    });
  }

  index(
    filter: Filter[] = [],
    sort: Sort[] = [],
    pageNumber = 0,
    pageSize = 5
  ): Observable<Model[]> {
    const params = this.getGridParams(filter, sort, pageNumber, pageSize);
    return this.http.get<Model[]>(`/api/` + this.baseName + `/GetOutgoingBankWires`, {
      params: params,
    })
  }

  getOutgoingBankWireStatusChangeEvents(
    pageNumber = 0,
    pageSize = 5,
    id: string,
  ): Observable<Model[]> {
    const params = this.getGridParams([], [], pageNumber, pageSize);
    return this.http.get<Model[]>(`/api/` + this.baseName + `/GetOutgoingBankWireStatusChangeEvents/` + id, {
      params: params,
    })
  }

  exportToExcel(filter: Filter[] = []): Observable<any> {
    let params = new HttpParams()
    filter.forEach((x, i) => {
      params = params.set('filters[' + i + '].attribute', x.attribute);
      params = params.set('filters[' + i + '].operation', x.operation);
      params = params.set('filters[' + i + '].value', x.value);
      x.useOrOperator ? params = params.set('filters[' + i + '].useOrOperator', x.useOrOperator) : null;
    })
    return this.http.get(`/api/` + this.baseName + `/ExportToExcel`, {
      responseType: 'blob',
      params: params
    })
  }
}
