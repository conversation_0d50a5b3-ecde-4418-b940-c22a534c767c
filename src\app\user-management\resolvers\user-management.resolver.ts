import {Injectable} from "@angular/core";
import {UserService} from "../services/user.service";
import {UserGroupService} from "../services/user-group.service";
import {AuhtItemService} from "../services/auht-item.service";
import {ActivatedRouteSnapshot, Resolve, RouterStateSnapshot} from "@angular/router";
import {Model} from "@ttwr-framework/ngx-main-visuals";
import {BaseService} from "../../services/base.service";
import {Observable} from "rxjs";
import {SYGSBaseService} from "../../services/SYGSBaseService";

@Injectable()
export class UserManagementResolver implements Resolve<Model> {
  map: { [key: string]: SYGSBaseService };

  constructor(private userService: UserService,
              private userGroupService: UserGroupService,
              private authItemService: AuhtItemService,
  ) {
    this.map = {
      'user': userService,
      'role': authItemService,
      'user-group': userGroupService,
    }
  }

  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<Model> {
    return this.map[route.data['service_key']].view(route.params['id']);
  }
}
