# Base angular template

## Project startup
Make sure when you start your project using this template to follow these steps
* change the line ```RewriteRule ^ /base/index.html``` in .htaccess file
* change in manifest file the following constants
    * name
    * short_name    
    * scope
    * etc...
* Changes in gitlab-ci.yml
    * PUBLISH_FOLDER
    * BASE_HREF
    * API_URL
    * the line `- sed -i -e"s#apiUrl:.*#apiUrl:'http://10.0.0.82/base-api/',#g"  src/environments/environment.ts`
    and the line under it to match required live environment api url
* Change all constants that has the value `TTWR-base-template` in angular.json file to match your demands
* npm install
* to activate permissions uncomment the line `this.addPermissionsDynamically('', this.router.config);` in ``app.component.ts`` file 

## Crud generation
To Generate the crud please run

`node node_modules/@ttwr-framework/ngx-main-visuals/cli/cli.js --model=../../../src/app/path/to/model/modelName.model.ts
 --out=../../../src/app/OutputFolderName --baseservice=../../../src/app/services/base.service.ts`
  
## Popup generation 
If you want to a component based on popup use the following command, you can specify any component to be popup
or you can add `--al-as-popup` if you want to make all components (except for index) as popup


`node node_modules/@ttwr-framework/ngx-main-visuals/cli/cli.js --model=../../../src/app/path/to/model/modelName.model.ts --update-as-popup --view-as-popup --create-as-popup --out=../../../src/app/OutputFolderName --baseservice=../../../src/app/services/base.service.ts`
 
## Exclude some components from generation
You can use `--remove-view-generation` to exclude view component from being generated, the same goes
for update and create using `--remove-update-generation` and `remove-create-generation` 


