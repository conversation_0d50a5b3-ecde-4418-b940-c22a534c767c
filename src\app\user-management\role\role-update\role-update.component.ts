import {Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import {UserGroupService} from '../../services/user-group.service';
import {MatLegacyDialog as MatDialog} from '@angular/material/legacy-dialog';
import {AuthItem} from '../../models/auth-item.model';
import {AuhtItemService} from '../../services/auht-item.service';
import {AlertService, Helper, LanguagePipe, TreeDef, TreeNode, ViewDef,} from '@ttwr-framework/ngx-main-visuals';

@Component({
  selector: 'app-role-update',
  templateUrl: './role-update.component.html',
  styleUrls: ['./role-update.component.scss'],
})

export class RoleUpdateComponent implements OnInit {
  public counter = 0;
  rootNode!: TreeNode<AuthItem>;
  perms: number[] = [];
  @ViewChild('searchInput', {static: false}) input!: ElementRef;

  public configForm: ViewDef = {
    title: 'role_update_perm_tree',
    fields: [{key: 'Name'}],
    fieldsDef: AuthItem.getModelDef(),
    obj: {},
    actionFields: [
      {
        label: 'Submit',
        delegateFunction: () => {
          const obj = {
            AuthItemChildren: this.perms.join(','),
          };
          this.auhtItemService
            .addRoleChildren(this.configForm.obj?.['Id'], obj)
            .subscribe(() => {
              this.alertService.success('Success Update');
              this.router.navigate(['view-tree/' + this.configForm.obj?.['Id']], {
                relativeTo: this.activatedRoute.parent,
              });
            });
        },
      },
      {
        label: 'Cancel',
        cssClass: 'btn-danger',
        causeValidate: false,
        delegateFunction: () => {
          this.router.navigate(['index/'], {relativeTo: this.activatedRoute.parent});
        },
      },
    ],
  };

  public config: TreeDef = {
    data: [],
    actions: [],
    nodeSelectedFunction: (node: TreeNode<AuthItem>) => {
      if (node.data && node.data.Inherited) return;
      if (node.data) this.traverseTree(node, !node.data.AddedToTree);
      this.counter++;
      this.perms = [];
      if (this.rootNode.children)
        this.rootNode.children.forEach((child) => this.getAllRequestedPerms(child, this.perms));
    },
  };

  public doneLoading!: boolean;


  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private dialog: MatDialog,
    private auhtItemService: AuhtItemService,
    private userGroupService: UserGroupService,
    private alertService: AlertService,
    private activatedRoute: ActivatedRoute,
    private languagePipe: LanguagePipe
  ) {
  }

  ngOnInit() {
    this.configForm.obj = Helper.deepCopy(this.route.snapshot.data.obj[0]);
    this.refreshTree();

  }

  public traverseTree(node: TreeNode<AuthItem>, add: boolean) {
    let parent = node.parent;
    this.dfs(node, add);

    while (add && parent && parent.parent != null) {
      this.dfs(parent, add);
      if (parent.data && (!parent.data.Inherited || this.checkIfRootModule(parent)))
        parent.data.IncludedInRequest = add;
      parent = parent.parent;
    }
  }

  public addToTree(node: TreeNode<AuthItem>, add: boolean) {
    const temp = '<span style="color: green; font-weight: bold">&#10003;</span>';
    if (node.data) {
      node.data.AddedToTree = add;
      if (!node.data.AddedToTree) {
        if (node.name.indexOf(temp) !== -1) node.name = node.name.substr(temp.length, node.name.length);
      } else {
        if (node.name.indexOf(temp) == -1) node.name = temp + node.name;
      }
    }
  }

  public refreshTree() {
    this.auhtItemService
      .getTree(this.input ? (this.input.nativeElement.value !== '' ? this.input.nativeElement.value : null) : null)
      .subscribe((res) => {
        this.fillTree(res);
        if (this.config.refreshSubject) {
          this.config.refreshSubject.next(true);
        }
        this.doneLoading = true;
      });
  }

  private getAllRequestedPerms(node: TreeNode<AuthItem>, res: number[]) {
    if (node.data && node.data.IncludedInRequest) res.push(node.data.Id);

    if (node.children)
      node.children.forEach((n) => {
        this.getAllRequestedPerms(n, res);
      });
  }

  private checkIfRootModule(node: TreeNode<AuthItem>) {
    return !node || !node.parent || !node.parent.parent;
  }

  private dfs(node: TreeNode<AuthItem>, add: boolean) {
    if (node.data && node.data.Visited[this.counter] && node.data.Visited[this.counter] === 1) return;
    if (node.data && (!node.data.Inherited || this.checkIfRootModule(node))) node.data.IncludedInRequest = add;
    if (node.data) node.data.Visited[this.counter] = 1;
    this.addToTree(node, add);
    if (node.children)
      node.children.forEach((n) => {
        if ((n.data && n.data.Inherited) || add == false) this.dfs(n, add);
      });
    return node;
  }

  private fillTree(root: any) {
    const treeNodeResult: TreeNode<AuthItem>[] = [];
    this.rootNode = {
      name: this.languagePipe.transform('Permission Tree'),
      children: [],
      parent: null,
      level: 1,
    };

    Array.prototype.forEach.call(root, (node) => {
      if (this.rootNode.children) this.rootNode.children.push(this.getTreeNodeFromDbNode(node, this.rootNode));
    });
    treeNodeResult.push(this.rootNode);

    this.config.data = treeNodeResult;
  }

  private getTreeNodeFromDbNode(dbNode: AuthItem, parent: any) {
    dbNode.AddedToTree = false;
    dbNode.IncludedInRequest = false;
    dbNode.Visited = [];
    if (dbNode.Inherited) {
      dbNode.ClearName = '<span style="color:red"> * </span>' + dbNode.ClearName;
    }
    const node: TreeNode<AuthItem> = {
      name: dbNode.ClearName,
      data: dbNode,
      parent: parent,
      children: [],
      level: parent.level + 1,
    };
    dbNode.AuthItemChildren.forEach((n) => {
      if (node.children) node.children.push(this.getTreeNodeFromDbNode(n, node));
    });
    return node;
  }
}
