import {Component, OnInit} from '@angular/core';
import {User} from '../../models/user.model';
import {ActivatedRoute, Router} from '@angular/router';
import {UserService} from '../../services/user.service';
import {Observable, throwError} from 'rxjs';
import {UserUpdateComponent} from '../user-update/user-update.component';
import {UserGroup} from '../../models/user-group.model';
import {UserGroupService} from '../../services/user-group.service';
import {MatLegacyDialog as MatDialog} from '@angular/material/legacy-dialog';
import {AddUserGroupsComponent} from '../add-user-groups/add-user-groups.component';
import {AlertService, Helper, MasterDetailDef, Model} from '@ttwr-framework/ngx-main-visuals';
import {catchError} from "rxjs/operators";

@Component({
  selector: 'app-user-view',
  templateUrl: './user-view.component.html',
  styleUrls: ['./user-view.component.scss'],
})
export class UserViewComponent implements OnInit {
  public config: MasterDetailDef = {
    masterViewConfig: {
      title: 'User View',
      fields: [
        {key: 'FullName'},
        {key: 'Username'},
        {key: 'Dob'},
        {key: 'Mobile'},
        {key: 'Phone'},
        {key: 'Email'},
        {key: 'ExpiryDate'},
      ],

      obj: {},

      fieldsDef: User.getModelDef(),
      actionFields: [
        {
          label: 'Update',
          cssClass: 'btn-info',
          delegateFunction: (obj: any) => {
            const dialogRef = this.dialog.open(UserUpdateComponent, {
              width: '50%',
              panelClass: 'ttwr-dialog',
              data: {obj: obj},
            });
            dialogRef.afterClosed().subscribe(async (res: any) => {
              if (res && this.config.masterViewConfig.obj) {
                const temp = await this.userService.view(this.config.masterViewConfig.obj['Id']).toPromise();
                this.config.masterViewConfig.obj = temp;
                if (this.config.masterViewConfig.refreshSubject) this.config.masterViewConfig.refreshSubject.next(true);
              }
            });
          },
        },
        {
          label: 'Delete',
          confirmationRequired: true,
          cssClass: 'btn-warning',
          delegateFunction: (obj) =>
            this.userService.delete(obj['Id']).subscribe(() => {
              this.alertService.success('Success Delete');
              this.router.navigate(['index/'], {relativeTo: this.activatedRoute.parent});
            }),
        },
        {
          label: 'Done',
          cssClass: 'btn-danger',
          delegateFunction: () => this.router.navigate(['index/'], {relativeTo: this.activatedRoute.parent}),
        },
      ],
    },
    detailGridsConfig: [
      {
        title: 'Groups',
        fields: [{key: 'Name'}],
        fieldsDef: UserGroup.getModelDef(),
        dataFunction: (filter, sort, pageIndex, pageSize): Observable<Model[]> => {
          if (this.config.masterViewConfig.obj)
            return this.userGroupService.indexOnUser(
              this.config.masterViewConfig.obj['Id'],
              filter,
              sort,
              pageIndex,
              pageSize
            ).pipe(catchError(err => {
              this.alertService.error(err.message);
              return throwError(err.message);
            }));
          else return new Observable<Model[]>();
        },
        actionFields: [
          {
            header: 'Actions',
            actions: [
              {
                icon: 'assets/icons/grid/ic_delete.svg',
                tooltip: 'Delete',
                cssClass: 'btn-delete',
                confirmationRequired: true,
                delegateFunction: (obj) => {
                  if (this.config.masterViewConfig.obj)
                    this.userService
                      .deleteGroup(obj['Id'], this.config.masterViewConfig.obj['Id'])
                      .subscribe(() => {
                        this.alertService.success('Success Delete');
                        this.config.detailGridsConfig.forEach((grid) =>
                          grid.refreshSubject ? grid.refreshSubject.next(true) : undefined
                        );
                      });
                },
              },
            ],
          },
        ],
        actions: [
          {
            icon: 'assets/icons/grid/ic_create.svg',
            tooltip: 'Create',
            cssClass: 'btn-create',
            delegateFunction: () => {
              const dialogRef = this.dialog.open(AddUserGroupsComponent, {
                width: '50%',
                panelClass: 'ttwr-dialog',
                data: {obj: this.config.masterViewConfig.obj},
              });
              dialogRef.afterClosed().subscribe((res: any) => {
                if (res) {
                  this.config.detailGridsConfig.forEach((grid) =>
                    grid.refreshSubject ? grid.refreshSubject.next(true) : undefined
                  );
                }
              });
            },
          },
        ],
      },
    ],
  };

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private dialog: MatDialog,
    private userService: UserService,
    private userGroupService: UserGroupService,
    private alertService: AlertService,
    private activatedRoute: ActivatedRoute,
  ) {
  }

  ngOnInit() {
    if (this.route.snapshot.data.obj) {
      this.config.masterViewConfig.obj = Helper.deepCopy(this.route.snapshot.data.obj[0]);
    }
  }
}
