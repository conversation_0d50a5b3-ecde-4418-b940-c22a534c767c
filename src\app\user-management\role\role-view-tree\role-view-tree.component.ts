import {Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import {UserGroupService} from '../../services/user-group.service';
import {MatLegacyDialog as MatDialog} from '@angular/material/legacy-dialog';
import {AuthItem} from '../../models/auth-item.model';
import {AuhtItemService} from '../../services/auht-item.service';
import {AlertService, Helper, LanguagePipe, TreeDef, TreeNode, ViewDef} from '@ttwr-framework/ngx-main-visuals';

@Component({
  selector: 'app-role-view-tree',
  templateUrl: './role-view-tree.component.html',
  styleUrls: ['./role-view-tree.component.scss'],
})
export class RoleViewTreeComponent implements OnInit {
  rootNode!: TreeNode<AuthItem>;
  Id!: number;
  @ViewChild('searchInput', {static: false}) input!: ElementRef;

  public configForm: ViewDef = {
    title: 'role_view_perm_tree',
    fields: [{key: 'Name'}],
    fieldsDef: AuthItem.getModelDef(),
    obj: {},
    actionFields: [
      {
        label: 'Cancel',
        cssClass: 'btn-danger',
        causeValidate: false,
        delegateFunction: () => {
          this.router.navigate(['index/'], {relativeTo: this.activatedRoute.parent});
        },
      },
    ],
  };

  public config: TreeDef = {
    data: [],
    actions: [],
  };

  public doneLoading!: boolean;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private dialog: MatDialog,
    private auhtItemService: AuhtItemService,
    private userGroupService: UserGroupService,
    private alertService: AlertService,
    private activatedRoute: ActivatedRoute,
    private languagePipe: LanguagePipe
  ) {
  }

  ngOnInit() {
    if (this.route.snapshot.data.obj) {
      this.configForm.obj = Helper.deepCopy(this.route.snapshot.data.obj[0]);
      this.Id = this.configForm.obj['Id'];
      this.refreshTree();
    }
  }

  public refreshTree() {
    this.auhtItemService.viewRole(this.Id).subscribe((res) => {
      if (res['status'] === 1) {
        this.fillTree(res['AuthItemChildren']);
        if (this.config.refreshSubject) {
          this.config.refreshSubject.next(true);
        }
        this.doneLoading = true;
      }
    });
  }

  private fillTree(root: any) {
    const treeNodeResult: TreeNode<AuthItem>[] = [];
    this.rootNode = {
      name: this.languagePipe.transform('Permission Tree'),
      children: [],
      parent: null,
      level: 1,
    };

    Array.prototype.forEach.call(root, (node) => {
      if (this.rootNode.children) this.rootNode.children.push(this.getTreeNodeFromDbNode(node, this.rootNode));
    });
    treeNodeResult.push(this.rootNode);

    this.config.data = treeNodeResult;
  }

  private getTreeNodeFromDbNode(dbNode: AuthItem, parent: any) {
    dbNode.AddedToTree = false;
    dbNode.IncludedInRequest = false;
    dbNode.Visited = [];

    const node: TreeNode<AuthItem> = {
      name: dbNode.ClearName,
      data: dbNode,
      parent: parent,
      children: [],
      level: parent.level + 1,
    };
    dbNode.AuthItemChildren.forEach((n) => {
      if (node.children) node.children.push(this.getTreeNodeFromDbNode(n, node));
    });
    return node;
  }
}
