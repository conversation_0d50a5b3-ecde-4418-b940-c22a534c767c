import {GridDef, LanguageService, Model, ModelLovGridComponent} from "@ttwr-framework/ngx-main-visuals";
import {Component, OnInit} from "@angular/core";
import {Banks} from "../Models/Banks.model";
import {Observable} from "rxjs";
import {BanksService} from "../Services/banks.service";

@Component({
  template: `
    <ttwr-lov-grid [config]="config" [selectName]="selectName" [selectValue]="selectValue"></ttwr-lov-grid>
  `,
})
export class LOVREBBranchesListComponent extends ModelLovGridComponent implements OnInit {
  public config: GridDef = {
    title: 'Banks',
    fields: [
      {
        key: 'bankIdentityCode'
      },
      {
        key: 'bankARName', isHidden: this.languageService.getSelectedLanguage() == 'en'
      },
      {
        key: 'bankENName', isHidden: this.languageService.getSelectedLanguage() == 'ar'
      }
    ],
    fieldsDef: Banks.getModelDef(),
    actionFields: [],
    actions: [],
    disableToggleAll: true,
    export: {
      pdf: {
        enable: false
      },
      excel: {
        enable: false
      }
    },

    dataFunction: (filter, sort, pageIndex, pageSize): Observable<Model[]> => {
      return this.bankService.bankBranches(filter, sort, pageIndex, pageSize, "REBSSYDAXXX");
    }

  }

  constructor(private bankService: BanksService,
              private languageService: LanguageService) {
    super();
  }

  ngOnInit() {
    if (this.languageService.selectedLanguage == 'ar') {
      this.selectName = 'bankARName';
    } else {
      this.selectName = 'bankENName ';
    }

    this.selectValue = 'bankIdentityCode'
  }
}
