/*
 * Copyright (c) 2019. This template belongs to Tatweer LLC.
 * Any use of this template must be authorized by the owner company.
 * The distribution of this product is prohibited under any circumstances.
 */
import {Directive, OnInit} from '@angular/core';

import {Observable, of, Subject} from 'rxjs';
import {Model, TtwrBaseInputComponent} from "@ttwr-framework/ngx-main-visuals";



@Directive({
  selector: '[ttwrBaseSelect]'
})
export class TtwrBaseSelectComponent extends TtwrBaseInputComponent implements OnInit {
  selectName: string | undefined;
  selectValue: string | undefined;
  options: Array<{ [key: string]: any }> = [];
  optionsDetails: Array<{ [key: string]: any }> = [];
  doneLoading = false;
  subject = new Subject();

  getFunc(): Observable<Model> {
    return of();
  };

  ngOnInit() {
    super.ngOnInit();
    this.subject.asObservable().subscribe(() => {
      this.getFunc().subscribe(res => {
        if (this.options)
          this.options.splice(0, this.options.length);

        this.optionsDetails.splice(0, this.optionsDetails.length);
        if (res.Items && res.Items.length!==0)

          res.Items.forEach((obj: any) => {
            this.options.push({
              value: obj[this.selectValue as keyof Model],
              label: obj[this.selectName as keyof Model]
            });
            this.optionsDetails[obj[this.selectValue as keyof Model]] = obj;
          });
        this.doneLoading = true;
      });
    });
    this.subject.next(true);
  }
}
