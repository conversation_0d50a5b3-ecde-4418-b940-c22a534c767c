import {Model, TtwrInputComponent, Types} from "@ttwr-framework/ngx-main-visuals";
import {OperationModel} from "./Operation.model";

export class IncomingTransfersModel extends Model {
  transferId!: string;
  addedAt!: Date;
  additionType!: string;
  transferringParty!: string;
  sygsRef!: string;
  orderType!: string;
  ttc!: string;
  employeeName!: string;
  payBankBIC!: string;
  payBankName!: string;
  payBranchBIC!: string;
  benefitBankBIC!: string;
  benefitBranchBIC!: string;
  payPartBIC!: string;
  benefitPartBIC!: string;
  valueDate!: string;
  optCode!: string;
  insCurrency!: string;
  insAmount!: string;
  insAmountAsWords!: string;
  exchangeRate!: string;
  dtlChg!: string;
  sendChangeAmount!: string;
  receiveChangeAmount!: string;
  settlementCurrency!: string;
  settlementAmount!: string;
  settlementAmountAsWords!: string;
  sendReference!: string;
  priority!: string;
  sendToReceiveInformation!: string;
  rmtInformation!: string;
  dtAccount!: string;
  dtName!: string;
  dtAdd!: string;
  ctAccount!: string;
  ctName!: string;
  ctNameInREB!: string;
  ctAdd!: string;
  state!: string;
  relatedReference!: string;
  checkNumber!: string;
  endrCount!: string;
  endrNames!: string;
  rebMessageId!: string;
  transferManipulated!: boolean;
  transferredCurrency!: string;
  transferredAmount!: string;
  operations!: OperationModel[];


  protected static initializeModelDef() {
    return {
      defs: {
        transferId: {
          ID: 'transferId',
          dataTypes: Types.STRING,
          label: 'transferId',
          isSortable: true,
          ui: TtwrInputComponent
        },
        transferredAmount: {
          ID: 'transferredAmount',
          dataTypes: Types.STRING,
          label: 'transferredAmount',
          isSortable: true,
          ui: TtwrInputComponent
        },
        transferredCurrency: {
          ID: 'transferredCurrency',
          dataTypes: Types.STRING,
          label: 'transferredCurrency',
          isSortable: true,
          ui: TtwrInputComponent
        },
        addedAt: {
          ID: 'addedAt',
          dataTypes: Types.DATETIME,
          label: 'addedAt',
          isSortable: true,
          ui: TtwrInputComponent
        },
        additionType: {
          ID: 'additionType',
          dataTypes: Types.STRING,
          label: 'additionType',
          isSortable: true,
          ui: TtwrInputComponent
        },
        transferringParty: {
          ID: 'transferringParty',
          dataTypes: Types.STRING,
          label: 'transferringParty',
          isSortable: true,
          ui: TtwrInputComponent
        },
        sygsRef: {
          ID: 'sygsRef',
          dataTypes: Types.STRING,
          label: 'sygsRef',
          isSortable: true,
          ui: TtwrInputComponent
        },
        orderType: {
          ID: 'orderType',
          dataTypes: Types.STRING,
          label: 'orderType',
          isSortable: true,
          ui: TtwrInputComponent
        },
        ttc: {
          ID: 'ttc',
          dataTypes: Types.STRING,
          label: 'ttc',
          isSortable: true,
          ui: TtwrInputComponent
        },
        payBankBIC: {
          ID: 'payBankBIC',
          dataTypes: Types.STRING,
          label: 'payBankBIC',
          isSortable: true,
          ui: TtwrInputComponent
        },
        payBankName: {
          ID: 'payBankName',
          dataTypes: Types.STRING,
          label: 'payBankName',
          isSortable: true,
          ui: TtwrInputComponent
        },
        employeeName: {
          ID: 'employeeName',
          dataTypes: Types.STRING,
          label: 'employeeName',
          isSortable: true,
          ui: TtwrInputComponent
        },
        payBranchBIC: {
          ID: 'payBranchBIC',
          dataTypes: Types.STRING,
          label: 'payBranchBIC',
          isSortable: true,
          ui: TtwrInputComponent
        },
        benefitBankBIC: {
          ID: 'benefitBankBIC',
          dataTypes: Types.STRING,
          label: 'benefitBankBIC',
          isSortable: true,
          ui: TtwrInputComponent
        },
        benefitBranchBIC: {
          ID: 'benefitBranchBIC',
          dataTypes: Types.STRING,
          label: 'benefitBranchBIC',
          isSortable: true,
          ui: TtwrInputComponent
        },
        payPartBIC: {
          ID: 'payPartBIC',
          dataTypes: Types.STRING,
          label: 'payPartBIC',
          isSortable: true,
          ui: TtwrInputComponent
        },
        benefitPartBIC: {
          ID: 'benefitPartBIC',
          dataTypes: Types.STRING,
          label: 'benefitPartBIC',
          isSortable: true,
          ui: TtwrInputComponent
        },
        valueDate: {
          ID: 'valueDate',
          dataTypes: Types.STRING,
          label: 'valueDate',
          isSortable: true,
          ui: TtwrInputComponent
        },
        optCode: {
          ID: 'optCode',
          dataTypes: Types.STRING,
          label: 'optCode',
          isSortable: true,
          ui: TtwrInputComponent
        },
        insCurrency: {
          ID: 'insCurrency',
          dataTypes: Types.STRING,
          label: 'insCurrency',
          isSortable: true,
          ui: TtwrInputComponent
        },
        insAmount: {
          ID: 'insAmount',
          dataTypes: Types.STRING,
          label: 'insAmount',
          isSortable: true,
          ui: TtwrInputComponent
        },
        insAmountAsWords: {
          ID: 'insAmountAsWords',
          dataType: Types.STRING,
          label: 'insAmountAsWords',
          ui: TtwrInputComponent
        },
        exchangeRate: {
          ID: 'exchangeRate',
          dataTypes: Types.STRING,
          label: 'exchangeRate',
          isSortable: true,
          ui: TtwrInputComponent
        },
        dtlChg: {
          ID: 'dtlChg',
          dataTypes: Types.STRING,
          label: 'dtlChg',
          isSortable: true,
          ui: TtwrInputComponent
        },
        sendChangeAmount: {
          ID: 'sendChangeAmount',
          dataTypes: Types.STRING,
          label: 'sendChangeAmount',
          isSortable: true,
          ui: TtwrInputComponent
        },
        receiveChangeAmount: {
          ID: 'receiveChangeAmount',
          dataTypes: Types.STRING,
          label: 'receiveChangeAmount',
          isSortable: true,
          ui: TtwrInputComponent
        },
        settlementCurrency: {
          ID: 'settlementCurrency',
          dataTypes: Types.STRING,
          label: 'settlementCurrency',
          isSortable: true,
          ui: TtwrInputComponent
        },
        settlementAmount: {
          ID: 'settlementAmount',
          dataTypes: Types.STRING,
          label: 'settlementAmount',
          isSortable: true,
          ui: TtwrInputComponent
        },
        settlementAmountAsWords: {
          ID: 'settlementAmountAsWords',
          dataType: Types.STRING,
          label: 'settlementAmountAsWords',
          ui: TtwrInputComponent
        },
        sendReference: {
          ID: 'sendReference',
          dataTypes: Types.STRING,
          label: 'sendReference',
          isSortable: true,
          ui: TtwrInputComponent
        },
        sendToReceiveInformation: {
          ID: 'sendToReceiveInformation',
          dataTypes: Types.STRING,
          label: 'sendToReceiveInformation',
          isSortable: true,
          ui: TtwrInputComponent
        },
        rmtInformation: {
          ID: 'rmtInformation',
          dataTypes: Types.STRING,
          label: 'rmtInformation',
          isSortable: true,
          ui: TtwrInputComponent
        },
        dtAccount: {
          ID: 'dtAccount',
          dataTypes: Types.STRING,
          label: 'dtAccount',
          isSortable: true,
          ui: TtwrInputComponent
        },
        dtName: {
          ID: 'dtName',
          dataTypes: Types.STRING,
          label: 'dtName',
          isSortable: true,
          ui: TtwrInputComponent
        },
        dtAdd: {
          ID: 'dtAdd',
          dataTypes: Types.STRING,
          label: 'dtAdd',
          isSortable: true,
          ui: TtwrInputComponent
        },
        ctAccount: {
          ID: 'ctAccount',
          dataTypes: Types.STRING,
          label: 'ctAccount',
          isSortable: true,
          ui: TtwrInputComponent
        },
        ctName: {
          ID: 'ctName',
          dataTypes: Types.STRING,
          label: 'ctName',
          isSortable: true,
          ui: TtwrInputComponent
        },
        ctNameInREB: {
          ID: 'ctNameInREB',
          dataTypes: Types.STRING,
          label: 'ctNameInREB',
          isSortable: true,
          ui: TtwrInputComponent
        },
        ctAdd: {
          ID: 'ctAdd',
          dataTypes: Types.STRING,
          label: 'ctAdd',
          isSortable: true,
          ui: TtwrInputComponent
        },
        relatedReference: {
          ID: 'relatedReference',
          dataTypes: Types.STRING,
          label: 'relatedReference',
          isSortable: true,
          ui: TtwrInputComponent
        },
        checkNumber: {
          ID: 'checkNumber',
          dataTypes: Types.STRING,
          label: 'checkNumber',
          isSortable: true,
          ui: TtwrInputComponent
        },
        endrCount: {
          ID: 'endrCount',
          dataTypes: Types.STRING,
          label: 'endrCount',
          isSortable: true,
          ui: TtwrInputComponent
        },
        endrNames: {
          ID: 'endrNames',
          dataTypes: Types.STRING,
          label: 'endrNames',
          isSortable: true,
          ui: TtwrInputComponent
        },
        rebMessageId: {
          ID: 'rebMessageId',
          dataTypes: Types.STRING,
          label: 'rebMessageId',
          isSortable: true,
          ui: TtwrInputComponent
        },
        transferManipulated: {
          ID: 'transferManipulated',
          dataTypes: Types.NUMBER,
          label: 'transferManipulated',
          isSortable: true,
          ui: TtwrInputComponent
        },
        priority: {
          ID: 'priority',
          dataTypes: Types.STRING,
          label: 'priority',
          isSortable: true,
          ui: TtwrInputComponent
        },
        state: {
          ID: 'state',
          dataTypes: Types.STRING,
          label: 'state',
          isSortable: true,
          ui: TtwrInputComponent
        }
      }
    }
  }
}
