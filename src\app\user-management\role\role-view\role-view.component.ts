import {Component, OnInit} from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import {Observable, throwError} from 'rxjs';
import {AlertService, Helper, MasterDetailDef, Model} from '@ttwr-framework/ngx-main-visuals';
import {UserGroup} from '../../models/user-group.model';
import {UserGroupService} from '../../services/user-group.service';
import {MatLegacyDialog as MatDialog} from '@angular/material/legacy-dialog';
import {AuthItem} from '../../models/auth-item.model';
import {AuhtItemService} from '../../services/auht-item.service';
import {AddRoleGroupsComponent} from '../add-role-groups/add-role-groups.component';
import {AddRolesComponent} from '../add-roles/add-roles.component';
import {catchError} from "rxjs/operators";

@Component({
  selector: 'app-role-view',
  templateUrl: './role-view.component.html',
  styleUrls: ['./role-view.component.scss'],
})
export class RoleViewComponent implements OnInit {
  public config: MasterDetailDef = {
    masterViewConfig: {
      title: 'Role View',
      fields: [{key: 'Name'}],
      obj: {},
      fieldsDef: AuthItem.getModelDef(),
      actionFields: [
        {
          label: 'Done',
          cssClass: 'btn-danger',
          delegateFunction: () => this.router.navigate(['index/'], {relativeTo: this.activatedRoute.parent}),
        },
      ],
    },
    detailGridsConfig: [
      {
        title: 'Groups',
        fields: [{key: 'Name'}],
        fieldsDef: UserGroup.getModelDef(),
        dataFunction: (filter, sort, pageIndex, pageSize): Observable<Model[]> => {
          if (this.config.masterViewConfig.obj)
            return this.userGroupService.indexOnRole(
              this.config.masterViewConfig.obj['Id'],
              filter,
              sort,
              pageIndex,
              pageSize
            ).pipe(catchError(err => {
              this.alertService.error(err.message);
              return throwError(err.message);
            }));
          else return new Observable<Model[]>();
        },
        actionFields: [
          {
            header: 'Actions',
            actions: [
              {
                icon: 'assets/icons/grid/ic_delete.svg',
                tooltip: 'Delete',
                cssClass: 'btn-delete',
                confirmationRequired: true,
                delegateFunction: (obj) => {
                  if (this.config.masterViewConfig.obj)
                    this.auhtItemService
                      .removeGroup(obj['Id'], this.config.masterViewConfig.obj['Id'])
                      .subscribe(() => {
                        this.alertService.success('Success Delete');
                        this.config.detailGridsConfig.forEach((grid) =>
                          grid.refreshSubject ? grid.refreshSubject.next(true) : undefined
                        );
                      });
                },
              },
            ],
          },
        ],
        actions: [
          {
            icon: 'assets/icons/grid/ic_create.svg',
            tooltip: 'Create',
            cssClass: 'btn-create',
            delegateFunction: () => {
              const dialogRef = this.dialog.open(AddRoleGroupsComponent, {
                width: '50%',
                panelClass: 'ttwr-dialog',
                data: {obj: this.config.masterViewConfig.obj},
              });
              dialogRef.afterClosed().subscribe((res: any) => {
                if (res) {
                  this.config.detailGridsConfig.forEach((grid) =>
                    grid.refreshSubject ? grid.refreshSubject.next(true) : undefined
                  );
                }
              });
            },
          },
        ],
      },
      {
        title: 'Roles',
        fields: [{key: 'Name'}],
        fieldsDef: AuthItem.getModelDef(),
        dataFunction: (filter, sort, pageIndex, pageSize): Observable<Model[]> => {
          if (this.config.masterViewConfig.obj)
            return this.auhtItemService.indexRolesUnderRole(
              this.config.masterViewConfig.obj['Id'],
              filter,
              sort,
              pageIndex,
              pageSize
            ).pipe(catchError(err => {
              this.alertService.error(err.message);
              return throwError(err.message);
            }));
          else return new Observable<Model[]>();
        },
        actionFields: [
          {
            header: 'Actions',
            actions: [
              {
                icon: 'assets/icons/grid/ic_delete.svg',
                tooltip: 'Delete',
                cssClass: 'btn-delete',
                confirmationRequired: true,
                delegateFunction: (obj) => {
                  const req = {
                    Parent: this.config.masterViewConfig.obj?.['Id'],
                    Child: obj['Id'],
                  };
                  this.auhtItemService.deleteChild(req).subscribe(() => {
                    this.alertService.success('Success Delete');
                    this.config.detailGridsConfig.forEach((grid) =>
                      grid.refreshSubject ? grid.refreshSubject.next(true) : undefined
                    );
                  });
                },
              },
            ],
          },
        ],
        actions: [
          {
            icon: 'assets/icons/grid/ic_create.svg',
            tooltip: 'Create',
            cssClass: 'btn-create',
            delegateFunction: () => {
              const dialogRef = this.dialog.open(AddRolesComponent, {
                width: '50%',
                panelClass: 'ttwr-dialog',
                data: {obj: this.config.masterViewConfig.obj},
              });
              dialogRef.afterClosed().subscribe((res: any) => {
                if (res) {
                  this.config.detailGridsConfig.forEach((grid) =>
                    grid.refreshSubject ? grid.refreshSubject.next(true) : undefined
                  );
                }
              });
            },
          },
        ],
      },
    ],
  };

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private dialog: MatDialog,
    private auhtItemService: AuhtItemService,
    private userGroupService: UserGroupService,
    private alertService: AlertService,
    private activatedRoute: ActivatedRoute,
  ) {
  }

  ngOnInit() {
    this.config.masterViewConfig.obj = Helper.deepCopy(this.route.snapshot.data.obj[0]);
  }
}
