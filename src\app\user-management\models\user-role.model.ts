import {
  Model,
  TtwrInputCheckboxComponent,
  TtwrInputComponent,
  TtwrInputLovComponent,
  Types
} from '@ttwr-framework/ngx-main-visuals';
import {SelectRoleComponent} from "../user/select-role/select-role.component";
import {Validators} from "@angular/forms";
import { LOVBankBranchesListComponent } from '../../Banks/LOVBankBranchesList/LOVBankBranchesList.component';
import { LOVREBBranchesListComponent } from '../../Banks/LOVBankBranchesList/LOVREBBranchesList.component';

export class UserRoleModel extends Model {
  role!: string;
  username!: string;
  newPassword!: string;
  employeeId!: string;
  isInactive!: boolean;

  protected static initializeModelDef() {
    return {
      defs: {
        role: {
          ID: 'role',
          dataType: Types.STRING,
          label: 'role',
          ui: SelectRoleComponent,
          config: {
            isMultiSelect: true,
          }
        },
        username: {
          ID: 'username',
          dataType: Types.STRING,
          label: 'Username',
          ui: TtwrInputComponent,
          validators: [
            {
              name: 'required',
              validator: Validators.required,
              message: 'Username is required',
            },
            {
              name: 'pattern',
              validator: Validators.pattern('[a-zA-Z0-9\\-._@+\\\\]+'),
              message: 'Invalid username',
            },
          ]
        },
        newPassword: {
          ID: 'newPassword',
          dataType: Types.STRING,
          label: 'newPassword',
          ui: TtwrInputComponent,
          validators: [
            {
              name: 'minlength',
              validator: Validators.minLength(6),
              message: 'Password must be 6 characters'
            }
          ]
        },
        employeeId: {
          ID: 'employeeId',
          dataType: Types.STRING,
          label: 'employeeId',
          ui: TtwrInputComponent,
          validators: [
            {
              name: 'required',
              validator: Validators.required,
              message: 'this field is required'
            }
          ]
        },
        isInactive: {
          ID: 'isInactive',
          dataType: Types.NUMBER,
          label: 'Active',
          defaultValue: true,
          ui: TtwrInputCheckboxComponent,
        },
        branchIdentificationCode: {
          ID: 'branchIdentificationCode',
          dataType: Types.STRING,
          label: 'branchIdentificationCode',
          ui: TtwrInputLovComponent,
          config: {
            multiSelect: false,
            gridComponent: LOVREBBranchesListComponent,
            searchValueField: 'branchIdentificationName',
          },
        },
      }
    }
  }
}
