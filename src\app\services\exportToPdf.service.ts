import { jsPDF } from 'jspdf';
import { FontService } from './FontService';
import { Injectable } from '@angular/core';
import { LanguageService } from '@ttwr-framework/ngx-main-visuals';
import 'jspdf-autotable';
import { formatDate } from '@angular/common';
import { forEach } from 'lodash';
import html2canvas from 'html2canvas';

@Injectable({
  providedIn: 'root',
})
export class ExportToPdfService {
  constructor(private languageService: LanguageService) {}

  public exportPdf(
    obj: any,
    title: string,
    fileName: string,
    isIncomingTransfer: boolean
  ) {
    const doc: jsPDF = new jsPDF('p', 'mm', 'a4');
    doc.addFileToVFS('Amiri-Regular.ttf', FontService.AmiriFont);
    doc.addFont('Amiri-Regular.ttf', 'Amiri', 'normal');
    doc.setFont('Amiri');
    doc.setLanguage('ar');

    const pageWidth = doc.internal.pageSize.getWidth();
    const pageHeight = doc.internal.pageSize.getHeight();
    const margin = 10;
    const columnWidth = (pageWidth - margin) / 2;

    // Adding title
    doc.setFontSize(22);
    doc.setTextColor('#c09d35');
    doc.text(title, pageWidth - margin, 20, { align: 'right' });

    // Adding transfer bank details
    doc.setFontSize(12);
    doc.setTextColor('#000000');

    const dataArray = this.convertDataToArray(obj);
    const arabic = /[\u0600-\u06FF]/;

    let y = 40;
    let rightColumnX = pageWidth - margin;
    let leftColumnX = pageWidth - columnWidth - margin;
    let currentX = rightColumnX; // Start with the right column

    dataArray.forEach((item) => {
      let text: string;
      if (arabic.test(this.languageService.getLang(item.value)))
        text = `${this.languageService.getLang(
          item.key
        )}: ${this.languageService.getLang(item.value)}`;
      else
        text = `${this.languageService.getLang(
          item.value
        )} :${this.languageService.getLang(item.key)}`;

      const textLines = doc.splitTextToSize(text, columnWidth);
      let lastY = y;
      textLines.forEach((line: string) => {
        if (y >= pageHeight - 20) {
          doc.addPage();
          y = 20;
          lastY = y;
          currentX = rightColumnX; // Reset to right column on new page
        }

        doc.text(line, currentX, y, { align: 'right' });
        y += 6;
      });

      if (currentX === rightColumnX) {
        currentX = leftColumnX; // Switch to left column
        y = lastY;
      } else {
        currentX = rightColumnX; // Switch to right column
        y += 8;
      }
    });

    let headers;
    let data;
    if (obj.operations && obj.operations.length > 0 && isIncomingTransfer) {
      if (y >= pageHeight - 100) {
        doc.addPage();
        y = 20;
      }
      y += 10;
      doc.setFontSize(22);
      doc.setTextColor('#127e97');
      doc.text(':العمليات', pageWidth - margin, y, { align: 'right' });

      // Adding transfer bank details
      doc.setFontSize(12);
      doc.setTextColor('#000000');
      y += 5;

      headers = [
        [
          {
            content: 'العملية ناجحة؟',
            styles: { halign: 'right', cellWidth: 20 },
          },
          {
            content: 'نتيجة التفويض',
            styles: { halign: 'right', cellWidth: 25 },
          },
          {
            content: 'رقم العملية المصرفية',
            styles: { halign: 'right', cellWidth: 30 },
          },
          {
            content: 'رسالة العملية',
            styles: { halign: 'right', cellWidth: 30 },
          },
          {
            content: 'نتيجة العملية',
            styles: { halign: 'right', cellWidth: 20 },
          },
          {
            content: 'تاريخ التنفيذ',
            styles: { halign: 'right', cellWidth: 25 },
          },
          {
            content: 'اسم العملية',
            styles: { halign: 'right', cellWidth: 20 },
          },
          {
            content: 'معرف العملية',
            styles: { halign: 'right', cellWidth: 20 },
          },
        ],
      ];

      data = obj.operations.map((operation: any) => [
        {
          content: this.languageService.getLang(operation.isSuccess),
          styles: { halign: 'right' },
        },
        {
          content: operation.result.authorizeCode,
          styles: { halign: 'right' },
        },
        {
          content: operation.result.xapiReferenceNumber,
          styles: { halign: 'right' },
        },
        { content: operation.result.message, styles: { halign: 'left' } },
        { content: operation.result.code, styles: { halign: 'right' } },
        {
          content: operation.executeDate
            ? formatDate(
                new Date(operation.executeDate),
                'yyyy-MM-dd hh:mm:ss',
                'en'
              )
            : '-',
          styles: { halign: 'right' },
        },
        {
          content: this.languageService.getLang(operation.operationName),
          styles: { halign: 'right' },
        },
        { content: operation.operationId, styles: { halign: 'right' } },
      ]);
    } else if (
      obj.operations &&
      obj.operations.length > 0 &&
      !isIncomingTransfer
    ) {
      if (y >= pageHeight - 100) {
        doc.addPage();
        y = 20;
      }
      y += 10;
      doc.setFontSize(22);
      doc.setTextColor('#127e97');
      doc.text(':العمليات', pageWidth - margin, y, { align: 'right' });

      // Adding transfer bank details
      doc.setFontSize(12);
      doc.setTextColor('#000000');
      y += 5;

      headers = [
        [
          {
            content: 'العملية ناجحة؟',
            styles: { halign: 'right', cellWidth: 20 },
          },
          {
            content: 'مرجعية البنك',
            styles: { halign: 'right', cellWidth: 40 },
          },
          { content: 'اسم الموظف', styles: { halign: 'right', cellWidth: 30 } },
          { content: 'الخطأ', styles: { halign: 'right', cellWidth: 35 } },
          {
            content: 'سبب التصحيح',
            styles: { halign: 'right', cellWidth: 20 },
          },
          {
            content: 'تاريخ التنفيذ',
            styles: { halign: 'right', cellWidth: 25 },
          },
          {
            content: 'اسم العملية',
            styles: { halign: 'right', cellWidth: 20 },
          },
        ],
      ];

      data = obj.operations.map((operation: any) => [
        {
          content: this.languageService.getLang(operation.isSuccess),
          styles: { halign: 'right' },
        },
        {
          content: operation.xapiReferenceNumber
            ? operation.xapiReferenceNumber
            : '-',
          styles: { halign: 'right' },
        },
        {
          content: operation.employeeName ? operation.employeeName : '-',
          styles: { halign: 'right' },
        },
        {
          content: operation.exception ? operation.exception : '-',
          styles: { halign: 'left' },
        },
        {
          content: operation.correctionReason
            ? operation.correctionReason
            : '-',
          styles: { halign: 'right' },
        },
        {
          content: operation.executeDate
            ? formatDate(
                new Date(operation.executeDate),
                'yyyy-MM-dd hh:mm:ss',
                'en'
              )
            : '-',
          styles: { halign: 'right' },
        },
        {
          content: this.languageService.getLang(operation.eventName),
          styles: { halign: 'right' },
        },
      ]);
    }
    (doc as any).autoTable({
      startY: y,
      head: headers,
      body: data,
      theme: 'grid',
      tableWidth: 'auto',
      margin: { right: margin },
      styles: { textDirection: 'rtl', font: 'Amiri', fontSize: 11 },
      didDrawCell: (data: any) => {
        data.cell.styles.cellWidth = 'wrap';
      },
    });
    doc.save(`${fileName}.pdf`);
  }
  public exportOutgoingBankWireToPdf(obj: any, fileName: string) {
    // Create HTML content for PDF generation
    let htmlContent = OutgoingBankWireHtml();

    // Create a temporary div to render the HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlContent;
    tempDiv.style.position = 'absolute';
    tempDiv.style.left = '-9999px';
    tempDiv.style.top = '-9999px';
    tempDiv.style.width = '750px'; // Match the max-width in CSS
    tempDiv.style.backgroundColor = '#ffffff';
    document.body.appendChild(tempDiv);

    // Convert HTML to canvas using html2canvas
    html2canvas(tempDiv, {
      scale: 2, // Higher scale for better quality
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      width: 750,
      height: tempDiv.scrollHeight,
      scrollX: 0,
      scrollY: 0,
    })
      .then((canvas) => {
        // Remove the temporary div
        document.body.removeChild(tempDiv);

        // Create PDF with A5 paper size
        const doc: jsPDF = new jsPDF('p', 'mm', 'a5');
        doc.addFileToVFS('Amiri-Regular.ttf', FontService.AmiriFont);
        doc.addFont('Amiri-Regular.ttf', 'Amiri', 'normal');
        doc.setFont('Amiri');
        doc.setLanguage('ar');

        // Get A5 dimensions
        const pageWidth = doc.internal.pageSize.getWidth(); // 148mm
        const pageHeight = doc.internal.pageSize.getHeight(); // 210mm
        const margin = 5; // 5mm margin

        // Calculate image dimensions to fit A5 with margins
        const maxWidth = pageWidth - margin * 2; // 138mm
        const maxHeight = pageHeight - margin * 2; // 200mm

        // Convert canvas to image data
        const imgData = canvas.toDataURL('image/png');

        // Calculate scaling to fit the page
        const canvasWidth = canvas.width;
        const canvasHeight = canvas.height;
        const ratio = Math.min(
          maxWidth / (canvasWidth * 0.264583),
          maxHeight / (canvasHeight * 0.264583)
        ); // Convert px to mm

        const imgWidth = canvasWidth * 0.264583 * ratio;
        const imgHeight = canvasHeight * 0.264583 * ratio;

        // Center the image on the page
        const x = (pageWidth - imgWidth) / 2;
        const y = margin;

        // Add image to PDF
        doc.addImage(imgData, 'PNG', x, y, imgWidth, imgHeight);

        // Save the PDF
        doc.save(`${fileName}.pdf`);
      })
      .catch((error) => {
        console.error('Error generating PDF:', error);
        // Remove the temporary div in case of error
        if (document.body.contains(tempDiv)) {
          document.body.removeChild(tempDiv);
        }
      });
  }

  private convertDataToArray(data: any) {
    let result = [];
    for (let key in data) {
      if (data.hasOwnProperty(key) && key !== 'operations') {
        result.push({ key: key, value: data[key] });
      }
    }
    return result;
  }
}

export const OutgoingBankWireHtml = () => {
  return `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <title>إشعار زبون - حوالة صادرة</title>
  <style>
    body {
      font-family: "Arial", sans-serif;
      direction: rtl;
      text-align: right;
      margin: 40px;
      background-color: #f8f8f8;
    }

    .container {
      max-width: 750px;
      background-color: #fff;
      padding: 35px 40px;
      border: 1px solid #ccc;
      margin: auto;
      box-shadow: 2px 2px 10px rgba(0,0,0,0.1);
    }

    .header-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 20px;
      margin-bottom: 10px;
      flex-wrap: wrap;
    }

    .logo {
      width: 80px;
    }

    .header-title {
      flex: 1;
      text-align: center;
    }

    .header-title h2, .header-title h3 {
      margin: 0;
    }

    .field-pair {
      display: flex;
      justify-content: space-between;
      align-items: stretch;
      margin: 10px 0;
    }

    .field {
      flex: 1;
      min-width: 280px;
      display: flex;
      align-items: center;
      gap: 2px; /* تقليل المسافة بين العناصر داخل الحقل */
    }

    .label {
      width: auto;  /* تقليل العرض الثابت */
      font-weight: bold;
      font-size: 15px;
      text-align: right;
      margin-left: 4px; /* تقليل المسافة بين التسمية والمدخل */
      white-space: nowrap;
    }

    .label-divider {
      width: 1px;
      height: 20px;
      background-color: #ccc;
      margin: 0 2px;
    }

    .input {
      flex: 1;
      padding: 8px 10px;
      border: 1px solid #ddd;
      background-color: #fdfdfd;
    }

    .field-divider {
      width: 1px;
      background-color: #ccc;
      margin: 0 10px;
    }

    .section-divider {
      margin: 25px 0;
      border-top: 1px dashed #aaa;
    }

    .divider-line {
      height: 1px;
      background-color: #999;
      margin: 4px 0 15px;
    }

    .signature-row {
      display: flex;
      justify-content: space-between;
      gap: 20px;
      margin-top: 20px;
    }

    .signature-section {
      flex: 1;
    }

    .centered-field {
      display: flex;
      justify-content: center;
      margin-top: 10px;
      margin-bottom: 0;
    }

    .centered-field-inner {
      display: flex;
      align-items: center;
      direction: rtl;
      gap: 2px;
    }

    .centered-field .input {
      width: 150px;
      height: 6px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header-container">
      <img src="https://ebank.reb-sy.com/assets/REB-logo.svg" class="logo" alt="شعار البنك">
      <div class="header-title">
        <h2>إشعار زبون</h2>
        <h3>حــــــوالة صــــادرة</h3>
      </div>
    </div>

    <div class="centered-field">
      <div class="centered-field-inner">
        <span class="label">رقــم الــحــــــوالــة</span>
        <div class="label-divider"></div>
        <div class="input"></div>
      </div>
    </div>
    <div class="divider-line"></div>

    <!-- الحقول -->
    <div class="field-pair">
      <div class="field"><span class="label">اســم الزبـون الــمــسـتـفـيد</span><div class="label-divider"></div><div class="input"></div></div>
      <div class="field-divider"></div>
      <div class="field"><span class="label">اسـم الــزبـــــون الـــــدافــع</span><div class="label-divider"></div><div class="input"></div></div>
    </div>

    <div class="field-pair">
      <div class="field"><span class="label">البنــــــــك الــمــســــتـفـيــد</span><div class="label-divider"></div><div class="input"></div></div>
      <div class="field-divider"></div>
      <div class="field"><span class="label">الــفــــــــــــــرع الــــــدافــع</span><div class="label-divider"></div><div class="input"></div></div>
    </div>

    <div class="field-pair">
      <div class="field"><span class="label">الحـســــــاب الــمــســتفـيـد</span><div class="label-divider"></div><div class="input"></div></div>
      <div class="field-divider"></div>
      <div class="field"><span class="label">الــحــســـــــــــاب الــــدافــع</span><div class="label-divider"></div><div class="input"></div></div>
	  
    </div>

    <div class="field-pair">
      <div class="field"><span class="label">تــاريــخ الـحــوالــة</span><div class="label-divider"></div><div class="input"></div></div>
      <div class="field-divider"></div>
      <div class="field"><span class="label">تــاريــخ الــحـــــــق</span><div class="label-divider"></div><div class="input"></div></div>
    </div>

    <div class="field-pair">
      <div class="field"><span class="label">الـمــبــلــغ كــتــابـةً</span><div class="label-divider"></div><div class="input"></div></div>
      <div class="field-divider"></div>
      <div class="field"><span class="label">الـمــبــلــغ رقــمــــاً</span><div class="label-divider"></div><div class="input"></div></div>
    </div>

    <div class="field-pair">
      <div class="field"><span class="label">ســــبــب الــحــوالة</span><div class="label-divider"></div><div class="input"></div></div>
    </div>

    <div class="field-pair">
      <div class="field"><span class="label">مــلاحــظــات إضــافــيــة</span><div class="label-divider"></div><div class="input" style="width:100%"></div></div>
    </div>

    <div class="section-divider"></div>

    <div class="signature-row">
      <div class="signature-section">
        <div class="field"><span class="label">ختم الفــــــــرع</span><div class="label-divider"></div><div class="input"></div></div>
      </div>
      <div class="signature-section">
        <div class="field"><span class="label">اسم الزبون الثلاثي</span><div class="label-divider"></div><div class="input"></div></div>
        <div class="field"><span class="label">توقيـــــــــع الزبون</span><div class="label-divider"></div><div class="input"></div></div>
      </div>
    </div>
  </div>
</body>
</html>

`;
};
