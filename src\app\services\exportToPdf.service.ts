import { jsPDF } from 'jspdf';
import { FontService } from './FontService';
import { Injectable } from '@angular/core';
import { LanguageService } from '@ttwr-framework/ngx-main-visuals';
import 'jspdf-autotable';
import { formatDate } from '@angular/common';
import { forEach } from 'lodash';

@Injectable({
  providedIn: 'root',
})
export class ExportToPdfService {
  constructor(private languageService: LanguageService) {}

  public exportPdf(
    obj: any,
    title: string,
    fileName: string,
    isIncomingTransfer: boolean
  ) {
    const doc: jsPDF = new jsPDF('p', 'mm', 'a4');
    doc.addFileToVFS('Amiri-Regular.ttf', FontService.AmiriFont);
    doc.addFont('Amiri-Regular.ttf', 'Amiri', 'normal');
    doc.setFont('Amiri');
    doc.setLanguage('ar');

    const pageWidth = doc.internal.pageSize.getWidth();
    const pageHeight = doc.internal.pageSize.getHeight();
    const margin = 10;
    const columnWidth = (pageWidth - margin) / 2;

    // Adding title
    doc.setFontSize(22);
    doc.setTextColor('#c09d35');
    doc.text(title, pageWidth - margin, 20, { align: 'right' });

    // Adding transfer bank details
    doc.setFontSize(12);
    doc.setTextColor('#000000');

    const dataArray = this.convertDataToArray(obj);
    const arabic = /[\u0600-\u06FF]/;

    let y = 40;
    let rightColumnX = pageWidth - margin;
    let leftColumnX = pageWidth - columnWidth - margin;
    let currentX = rightColumnX; // Start with the right column

    dataArray.forEach((item) => {
      let text: string;
      if (arabic.test(this.languageService.getLang(item.value)))
        text = `${this.languageService.getLang(
          item.key
        )}: ${this.languageService.getLang(item.value)}`;
      else
        text = `${this.languageService.getLang(
          item.value
        )} :${this.languageService.getLang(item.key)}`;

      const textLines = doc.splitTextToSize(text, columnWidth);
      let lastY = y;
      textLines.forEach((line: string) => {
        if (y >= pageHeight - 20) {
          doc.addPage();
          y = 20;
          lastY = y;
          currentX = rightColumnX; // Reset to right column on new page
        }

        doc.text(line, currentX, y, { align: 'right' });
        y += 6;
      });

      if (currentX === rightColumnX) {
        currentX = leftColumnX; // Switch to left column
        y = lastY;
      } else {
        currentX = rightColumnX; // Switch to right column
        y += 8;
      }
    });

    let headers;
    let data;
    if (obj.operations && obj.operations.length > 0 && isIncomingTransfer) {
      if (y >= pageHeight - 100) {
        doc.addPage();
        y = 20;
      }
      y += 10;
      doc.setFontSize(22);
      doc.setTextColor('#127e97');
      doc.text(':العمليات', pageWidth - margin, y, { align: 'right' });

      // Adding transfer bank details
      doc.setFontSize(12);
      doc.setTextColor('#000000');
      y += 5;

      headers = [
        [
          {
            content: 'العملية ناجحة؟',
            styles: { halign: 'right', cellWidth: 20 },
          },
          {
            content: 'نتيجة التفويض',
            styles: { halign: 'right', cellWidth: 25 },
          },
          {
            content: 'رقم العملية المصرفية',
            styles: { halign: 'right', cellWidth: 30 },
          },
          {
            content: 'رسالة العملية',
            styles: { halign: 'right', cellWidth: 30 },
          },
          {
            content: 'نتيجة العملية',
            styles: { halign: 'right', cellWidth: 20 },
          },
          {
            content: 'تاريخ التنفيذ',
            styles: { halign: 'right', cellWidth: 25 },
          },
          {
            content: 'اسم العملية',
            styles: { halign: 'right', cellWidth: 20 },
          },
          {
            content: 'معرف العملية',
            styles: { halign: 'right', cellWidth: 20 },
          },
        ],
      ];

      data = obj.operations.map((operation: any) => [
        {
          content: this.languageService.getLang(operation.isSuccess),
          styles: { halign: 'right' },
        },
        {
          content: operation.result.authorizeCode,
          styles: { halign: 'right' },
        },
        {
          content: operation.result.xapiReferenceNumber,
          styles: { halign: 'right' },
        },
        { content: operation.result.message, styles: { halign: 'left' } },
        { content: operation.result.code, styles: { halign: 'right' } },
        {
          content: operation.executeDate
            ? formatDate(
                new Date(operation.executeDate),
                'yyyy-MM-dd hh:mm:ss',
                'en'
              )
            : '-',
          styles: { halign: 'right' },
        },
        {
          content: this.languageService.getLang(operation.operationName),
          styles: { halign: 'right' },
        },
        { content: operation.operationId, styles: { halign: 'right' } },
      ]);
    } else if (
      obj.operations &&
      obj.operations.length > 0 &&
      !isIncomingTransfer
    ) {
      if (y >= pageHeight - 100) {
        doc.addPage();
        y = 20;
      }
      y += 10;
      doc.setFontSize(22);
      doc.setTextColor('#127e97');
      doc.text(':العمليات', pageWidth - margin, y, { align: 'right' });

      // Adding transfer bank details
      doc.setFontSize(12);
      doc.setTextColor('#000000');
      y += 5;

      headers = [
        [
          {
            content: 'العملية ناجحة؟',
            styles: { halign: 'right', cellWidth: 20 },
          },
          {
            content: 'مرجعية البنك',
            styles: { halign: 'right', cellWidth: 40 },
          },
          { content: 'اسم الموظف', styles: { halign: 'right', cellWidth: 30 } },
          { content: 'الخطأ', styles: { halign: 'right', cellWidth: 35 } },
          {
            content: 'سبب التصحيح',
            styles: { halign: 'right', cellWidth: 20 },
          },
          {
            content: 'تاريخ التنفيذ',
            styles: { halign: 'right', cellWidth: 25 },
          },
          {
            content: 'اسم العملية',
            styles: { halign: 'right', cellWidth: 20 },
          },
        ],
      ];

      data = obj.operations.map((operation: any) => [
        {
          content: this.languageService.getLang(operation.isSuccess),
          styles: { halign: 'right' },
        },
        {
          content: operation.xapiReferenceNumber
            ? operation.xapiReferenceNumber
            : '-',
          styles: { halign: 'right' },
        },
        {
          content: operation.employeeName ? operation.employeeName : '-',
          styles: { halign: 'right' },
        },
        {
          content: operation.exception ? operation.exception : '-',
          styles: { halign: 'left' },
        },
        {
          content: operation.correctionReason
            ? operation.correctionReason
            : '-',
          styles: { halign: 'right' },
        },
        {
          content: operation.executeDate
            ? formatDate(
                new Date(operation.executeDate),
                'yyyy-MM-dd hh:mm:ss',
                'en'
              )
            : '-',
          styles: { halign: 'right' },
        },
        {
          content: this.languageService.getLang(operation.eventName),
          styles: { halign: 'right' },
        },
      ]);
    }
    (doc as any).autoTable({
      startY: y,
      head: headers,
      body: data,
      theme: 'grid',
      tableWidth: 'auto',
      margin: { right: margin },
      styles: { textDirection: 'rtl', font: 'Amiri', fontSize: 11 },
      didDrawCell: (data: any) => {
        data.cell.styles.cellWidth = 'wrap';
      },
    });
    doc.save(`${fileName}.pdf`);
  }
  public exportOutgoingBankWireToHtml(obj: any, fileName: string) {
    const dataArray = this.convertDataToArray(obj);

    // Create HTML structure
    let htmlContent = `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${fileName}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');

        body {
            font-family: 'Amiri', serif;
            margin: 20px;
            direction: rtl;
            text-align: right;
            background-color: #f9f9f9;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #c09d35;
            padding-bottom: 20px;
        }

        .title {
            font-size: 24px;
            color: #c09d35;
            font-weight: bold;
            margin: 0;
        }

        .details-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 30px;
        }

        .detail-item {
            padding: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            background-color: #fafafa;
        }

        .detail-label {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .detail-value {
            color: #666;
        }

        .operations-section {
            margin-top: 30px;
        }

        .operations-title {
            font-size: 20px;
            color: #127e97;
            font-weight: bold;
            margin-bottom: 15px;
            border-bottom: 1px solid #127e97;
            padding-bottom: 10px;
        }

        .operations-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .operations-table th,
        .operations-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
        }

        .operations-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }

        .operations-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        @media print {
            body {
                background-color: white;
            }
            .container {
                box-shadow: none;
                border: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">تفاصيل الحوالة البنكية الصادرة</h1>
        </div>

        <div class="details-grid">`;

    // Add data items to HTML
    dataArray.forEach((item) => {
      const label = this.languageService.getLang(item.key);
      const value = this.languageService.getLang(item.value);

      htmlContent += `
            <div class="detail-item">
                <div class="detail-label">${label}</div>
                <div class="detail-value">${value}</div>
            </div>`;
    });

    htmlContent += `
        </div>`;

    // Add operations table if exists
    if (obj.operations && obj.operations.length > 0) {
      htmlContent += `
        <div class="operations-section">
            <div class="operations-title">العمليات</div>
            <table class="operations-table">
                <thead>
                    <tr>
                        <th>معرف العملية</th>
                        <th>اسم العملية</th>
                        <th>تاريخ التنفيذ</th>
                        <th>سبب التصحيح</th>
                        <th>الخطأ</th>
                        <th>اسم الموظف</th>
                        <th>مرجعية البنك</th>
                        <th>العملية ناجحة؟</th>
                    </tr>
                </thead>
                <tbody>`;

      obj.operations.forEach((operation: any) => {
        htmlContent += `
                    <tr>
                        <td>${operation.operationId || '-'}</td>
                        <td>${
                          this.languageService.getLang(operation.eventName) ||
                          '-'
                        }</td>
                        <td>${
                          operation.executeDate
                            ? formatDate(
                                new Date(operation.executeDate),
                                'yyyy-MM-dd hh:mm:ss',
                                'en'
                              )
                            : '-'
                        }</td>
                        <td>${operation.correctionReason || '-'}</td>
                        <td style="text-align: left;">${
                          operation.exception || '-'
                        }</td>
                        <td>${operation.employeeName || '-'}</td>
                        <td>${operation.xapiReferenceNumber || '-'}</td>
                        <td>${
                          this.languageService.getLang(operation.isSuccess) ||
                          '-'
                        }</td>
                    </tr>`;
      });

      htmlContent += `
                </tbody>
            </table>
        </div>`;
    }

    htmlContent += `
    </div>
</body>
</html>`;

    // Create and download HTML file
    const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${fileName}.html`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  }

  private convertDataToArray(data: any) {
    let result = [];
    for (let key in data) {
      if (data.hasOwnProperty(key) && key !== 'operations') {
        result.push({ key: key, value: data[key] });
      }
    }
    return result;
  }
}
