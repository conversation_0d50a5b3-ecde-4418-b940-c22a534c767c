import { jsPDF } from 'jspdf';
import { FontService } from './FontService';
import { Injectable } from '@angular/core';
import { LanguageService } from '@ttwr-framework/ngx-main-visuals';
import 'jspdf-autotable';
import { formatDate } from '@angular/common';
import { forEach } from 'lodash';

@Injectable({
  providedIn: 'root',
})
export class ExportToPdfService {
  constructor(private languageService: LanguageService) {}

  public exportPdf(
    obj: any,
    title: string,
    fileName: string,
    isIncomingTransfer: boolean
  ) {
    const doc: jsPDF = new jsPDF('p', 'mm', 'a4');
    doc.addFileToVFS('Amiri-Regular.ttf', FontService.AmiriFont);
    doc.addFont('Amiri-Regular.ttf', 'Amiri', 'normal');
    doc.setFont('Amiri');
    doc.setLanguage('ar');

    const pageWidth = doc.internal.pageSize.getWidth();
    const pageHeight = doc.internal.pageSize.getHeight();
    const margin = 10;
    const columnWidth = (pageWidth - margin) / 2;

    // Adding title
    doc.setFontSize(22);
    doc.setTextColor('#c09d35');
    doc.text(title, pageWidth - margin, 20, { align: 'right' });

    // Adding transfer bank details
    doc.setFontSize(12);
    doc.setTextColor('#000000');

    const dataArray = this.convertDataToArray(obj);
    const arabic = /[\u0600-\u06FF]/;

    let y = 40;
    let rightColumnX = pageWidth - margin;
    let leftColumnX = pageWidth - columnWidth - margin;
    let currentX = rightColumnX; // Start with the right column

    dataArray.forEach((item) => {
      let text: string;
      if (arabic.test(this.languageService.getLang(item.value)))
        text = `${this.languageService.getLang(
          item.key
        )}: ${this.languageService.getLang(item.value)}`;
      else
        text = `${this.languageService.getLang(
          item.value
        )} :${this.languageService.getLang(item.key)}`;

      const textLines = doc.splitTextToSize(text, columnWidth);
      let lastY = y;
      textLines.forEach((line: string) => {
        if (y >= pageHeight - 20) {
          doc.addPage();
          y = 20;
          lastY = y;
          currentX = rightColumnX; // Reset to right column on new page
        }

        doc.text(line, currentX, y, { align: 'right' });
        y += 6;
      });

      if (currentX === rightColumnX) {
        currentX = leftColumnX; // Switch to left column
        y = lastY;
      } else {
        currentX = rightColumnX; // Switch to right column
        y += 8;
      }
    });

    let headers;
    let data;
    if (obj.operations && obj.operations.length > 0 && isIncomingTransfer) {
      if (y >= pageHeight - 100) {
        doc.addPage();
        y = 20;
      }
      y += 10;
      doc.setFontSize(22);
      doc.setTextColor('#127e97');
      doc.text(':العمليات', pageWidth - margin, y, { align: 'right' });

      // Adding transfer bank details
      doc.setFontSize(12);
      doc.setTextColor('#000000');
      y += 5;

      headers = [
        [
          {
            content: 'العملية ناجحة؟',
            styles: { halign: 'right', cellWidth: 20 },
          },
          {
            content: 'نتيجة التفويض',
            styles: { halign: 'right', cellWidth: 25 },
          },
          {
            content: 'رقم العملية المصرفية',
            styles: { halign: 'right', cellWidth: 30 },
          },
          {
            content: 'رسالة العملية',
            styles: { halign: 'right', cellWidth: 30 },
          },
          {
            content: 'نتيجة العملية',
            styles: { halign: 'right', cellWidth: 20 },
          },
          {
            content: 'تاريخ التنفيذ',
            styles: { halign: 'right', cellWidth: 25 },
          },
          {
            content: 'اسم العملية',
            styles: { halign: 'right', cellWidth: 20 },
          },
          {
            content: 'معرف العملية',
            styles: { halign: 'right', cellWidth: 20 },
          },
        ],
      ];

      data = obj.operations.map((operation: any) => [
        {
          content: this.languageService.getLang(operation.isSuccess),
          styles: { halign: 'right' },
        },
        {
          content: operation.result.authorizeCode,
          styles: { halign: 'right' },
        },
        {
          content: operation.result.xapiReferenceNumber,
          styles: { halign: 'right' },
        },
        { content: operation.result.message, styles: { halign: 'left' } },
        { content: operation.result.code, styles: { halign: 'right' } },
        {
          content: operation.executeDate
            ? formatDate(
                new Date(operation.executeDate),
                'yyyy-MM-dd hh:mm:ss',
                'en'
              )
            : '-',
          styles: { halign: 'right' },
        },
        {
          content: this.languageService.getLang(operation.operationName),
          styles: { halign: 'right' },
        },
        { content: operation.operationId, styles: { halign: 'right' } },
      ]);
    } else if (
      obj.operations &&
      obj.operations.length > 0 &&
      !isIncomingTransfer
    ) {
      if (y >= pageHeight - 100) {
        doc.addPage();
        y = 20;
      }
      y += 10;
      doc.setFontSize(22);
      doc.setTextColor('#127e97');
      doc.text(':العمليات', pageWidth - margin, y, { align: 'right' });

      // Adding transfer bank details
      doc.setFontSize(12);
      doc.setTextColor('#000000');
      y += 5;

      headers = [
        [
          {
            content: 'العملية ناجحة؟',
            styles: { halign: 'right', cellWidth: 20 },
          },
          {
            content: 'مرجعية البنك',
            styles: { halign: 'right', cellWidth: 40 },
          },
          { content: 'اسم الموظف', styles: { halign: 'right', cellWidth: 30 } },
          { content: 'الخطأ', styles: { halign: 'right', cellWidth: 35 } },
          {
            content: 'سبب التصحيح',
            styles: { halign: 'right', cellWidth: 20 },
          },
          {
            content: 'تاريخ التنفيذ',
            styles: { halign: 'right', cellWidth: 25 },
          },
          {
            content: 'اسم العملية',
            styles: { halign: 'right', cellWidth: 20 },
          },
        ],
      ];

      data = obj.operations.map((operation: any) => [
        {
          content: this.languageService.getLang(operation.isSuccess),
          styles: { halign: 'right' },
        },
        {
          content: operation.xapiReferenceNumber
            ? operation.xapiReferenceNumber
            : '-',
          styles: { halign: 'right' },
        },
        {
          content: operation.employeeName ? operation.employeeName : '-',
          styles: { halign: 'right' },
        },
        {
          content: operation.exception ? operation.exception : '-',
          styles: { halign: 'left' },
        },
        {
          content: operation.correctionReason
            ? operation.correctionReason
            : '-',
          styles: { halign: 'right' },
        },
        {
          content: operation.executeDate
            ? formatDate(
                new Date(operation.executeDate),
                'yyyy-MM-dd hh:mm:ss',
                'en'
              )
            : '-',
          styles: { halign: 'right' },
        },
        {
          content: this.languageService.getLang(operation.eventName),
          styles: { halign: 'right' },
        },
      ]);
    }
    (doc as any).autoTable({
      startY: y,
      head: headers,
      body: data,
      theme: 'grid',
      tableWidth: 'auto',
      margin: { right: margin },
      styles: { textDirection: 'rtl', font: 'Amiri', fontSize: 11 },
      didDrawCell: (data: any) => {
        data.cell.styles.cellWidth = 'wrap';
      },
    });
    doc.save(`${fileName}.pdf`);
  }
  public exportOutgoingBankWireToPdf(obj: any, fileName: string) {
    const doc: jsPDF = new jsPDF('p', 'mm', 'a5');
    doc.addFileToVFS('Amiri-Regular.ttf', FontService.AmiriFont);
    doc.addFont('Amiri-Regular.ttf', 'Amiri', 'normal');
    doc.setFont('Amiri');
    doc.setLanguage('ar');

    const pageWidth = doc.internal.pageSize.getWidth();
    const pageHeight = doc.internal.pageSize.getHeight();
    const margin = 10;
    const columnWidth = (pageWidth - margin) / 2;

    const dataArray = this.convertDataToArray(obj);
    const arabic = /[\u0600-\u06FF]/;

    let y = 40;
    let rightColumnX = pageWidth - margin;
    let leftColumnX = pageWidth - columnWidth - margin;
    let currentX = rightColumnX; // Start with the right column

    doc.save(`${fileName}.pdf`);
  }

  private convertDataToArray(data: any) {
    let result = [];
    for (let key in data) {
      if (data.hasOwnProperty(key) && key !== 'operations') {
        result.push({ key: key, value: data[key] });
      }
    }
    return result;
  }
}
