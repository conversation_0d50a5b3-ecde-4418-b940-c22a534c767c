import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {Filter, Model, Sort} from '@ttwr-framework/ngx-main-visuals';
import {SYGSBaseService} from "../../services/SYGSBaseService";

@Injectable()
export class UserGroupService extends SYGSBaseService {
  protected baseName = 'userGroup';

  indexOnUser(
    id: any,
    filter: Filter[] = [],
    sort: Sort[] = [],
    pageNumber = 0,
    pageSize = 5
  ): Observable<Model[]> {
    const params = this.getGridParams(filter, sort, pageNumber, pageSize);
    return this.http.get<Model[]>(`/api/` + this.baseName + `/indexOnUser/` + id, {
      params: params,
    });
  }

  addUsers(id: any, obj: Model): Observable<Model> {
    return this.http.post<Model>(`/api/` + this.baseName + `/addUsers/` + id, obj);
  }

  addRoles(id: any, obj: Model): Observable<Model> {
    return this.http.post<Model>(`/api/` + this.baseName + `/addRoles/` + id, obj);
  }

  indexOnRole(
    id: any,
    filter: Filter[],
    sort: Sort[],
    pageNumber = 0,
    pageSize = 5
  ): Observable<Model[]> {
    const params = this.getGridParams(filter, sort, pageNumber, pageSize);
    return this.http.get<Model[]>(`/api/` + this.baseName + `/indexOnRole/` + id, {
      params: params,
    });
  }

  deleteRole(id: number, groupId: string | number): Observable<Model> {
    return this.http.post<Model>(`/api/` + this.baseName + `/deleteRole/` + groupId, {
      Role: id,
    });
  }

  deleteUser(id: number, groupId: string | number): Observable<Model> {
    return this.http.post<Model>(`/api/` + this.baseName + `/deleteUser/` + groupId, {
      User: id,
    });
  }
}
