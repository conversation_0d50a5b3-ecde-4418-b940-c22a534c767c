import {Component, Inject} from '@angular/core';
import {AlertService, FormDef} from '@ttwr-framework/ngx-main-visuals';
import {UserGroup} from '../../models/user-group.model';
import {UserGroupService} from '../../services/user-group.service';
import {MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA, MatLegacyDialogRef as MatDialogRef} from '@angular/material/legacy-dialog';

@Component({
  selector: 'app-user-group-create',
  templateUrl: './user-group-create.component.html',
  styleUrls: ['./user-group-create.component.scss'],
})
export class UserGroupCreateComponent {
  dialogTitle='User Group Create';
  public config: FormDef = {
    titleHidden:true,
    fields: [{key: 'Name'}, {key: 'Code'}, {key: '_UserGroupRoles'}],
    fieldsDef: UserGroup.getModelDef(),
    obj: {},
    actionFields: [
      {
        label: 'Submit',
        delegateFunction: (obj) =>
          this.userGroupService.create(obj).subscribe(() => {
            this.alertService.success('Success Create');
            this.dialogRef.close(true);
          }),
      },
      {
        label: 'Cancel',
        cssClass: 'btn-danger',
        causeValidate: false,
        delegateFunction: () => this.dialogRef.close(),
      },
    ],
  };

  constructor(
    private userGroupService: UserGroupService,
    public dialogRef: MatDialogRef<UserGroupCreateComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private alertService: AlertService,
  ) {
  }
}
