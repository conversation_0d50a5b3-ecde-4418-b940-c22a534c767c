import {Model} from "@ttwr-framework/ngx-main-visuals";
import {NgModule} from "@angular/core";
import {RouterModule, Routes} from "@angular/router";
import {BanksMainComponent} from "./banks-main/banks-main.component";
import {BanksComponent} from "./BanksComponents/banks.component";
import {BanksIndexComponent} from "./BanksComponents/banks-index/banks-index.component";
import {BanksCreateComponent} from "./BanksComponents/banks-create/banks-create.component";
import {BanksViewComponent} from "./BanksComponents/banks-view/banks-view.component";
import {InstitutionResolver} from "../services/InstitutionResolver";
import {BanksUpdateComponent} from "./BanksComponents/banks-update/banks-update.component";

const routes: Routes = [
  {
    path: 'Banks', component: BanksMainComponent, children: [
      {
        path: 'banks',
        component: BanksComponent,
        data: {perms: {CLEAR_NAME: 'banks'}},
        children:
          [
            {
              path: 'index', component: BanksIndexComponent,
              data: {
                perms: {CLEAR_NAME: 'عرض'},
                permsChildren: [
                  {NAME: '/api/Banks/delete', CLEAR_NAME: 'خدمة الحذف'},]
              }
            },
            {
              path: 'create', component: BanksCreateComponent,
              data: {
                perms: {CLEAR_NAME: 'انشاء'},
                permsChildren: [{NAME: '/api/Banks/create', CLEAR_NAME: 'خدمة الانشاء'}]
              }
            },
            {
              path: 'view/:id', component: BanksViewComponent,
              resolve: {
                obj: InstitutionResolver,
              },
              data: {
                service_key: 'banks',
                perms: {CLEAR_NAME: 'استعراض التفاصيل'},
                permsChildren: [{NAME: '/api/Banks/view', CLEAR_NAME: 'خدمة استعراض التفاصيل'}]
              }
            },
            {
              path: 'update/:id', component: BanksUpdateComponent,
              resolve: {
                obj: InstitutionResolver,
              },
              data: {
                service_key: 'banks',
                perms: {CLEAR_NAME: 'تعديل'},
                permsChildren: [{NAME: '/api/Banks/update', CLEAR_NAME: 'خدمة التعديل'}]
              }
            },
          ],
      },

    ]
  }
]

@NgModule({
  imports: [
    RouterModule.forChild(routes)
  ],
  exports: [
    RouterModule
  ]
})
export class BanksRouting extends Model {


}
