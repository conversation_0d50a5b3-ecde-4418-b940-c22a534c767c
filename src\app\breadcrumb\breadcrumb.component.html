<breadcrumb #parent>
  <ol class="breadcrumb">
    <li class="breadcrumb-item breadcrumb-style">
      <a routerLink="/dashboard">{{ 'Home'|i18n }}</a>
    </li>
    <ng-template ngFor let-route [ngForOf]="parent.breadcrumbs">
      <li *ngIf="!route.terminal" class="breadcrumb-item breadcrumb-style">
        <a [routerLink]="[route.url]">{{ route.displayName|i18n }}</a>
      </li>
      <li *ngIf="route.terminal" class="breadcrumb-item active"
          aria-current="page">{{ route.displayName|i18n }}</li>
    </ng-template>
  </ol>
</breadcrumb>
