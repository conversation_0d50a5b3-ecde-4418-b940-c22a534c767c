import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {HttpParams} from '@angular/common/http';
import {Filter, Helper, Model, Sort} from "@ttwr-framework/ngx-main-visuals";
import {SYGSBaseService} from "../../services/SYGSBaseService";

@Injectable()
export class AuhtItemService extends SYGSBaseService {
  protected baseName = 'authItem';

  getTree(searchName = null): Observable<Model[]> {
    let filter = '';
    if (searchName) filter = Helper.addFilter({attribute: 'Name', operation: 'like', value: searchName}, '');
    const params = new HttpParams().set('filter', filter);
    return this.http.get<Model[]>(`/api/` + this.baseName + `/indexOnNotRole`, {params: params});
  }

  addGroups(id: any, obj: Model): Observable<Model> {
    return this.http.post<Model>(`/api/` + this.baseName + `/addGroups/` + id, obj);
  }

  indexOnGroup(
    id: any,
    filter: Filter[],
    sort: Sort[],
    pageNumber = 0,
    pageSize = 5
  ): Observable<Model[]> {
    const params = this.getGridParams(filter, sort, pageNumber, pageSize);
    return this.http.get<Model[]>(`/api/` + this.baseName + `/indexOnGroup/` + id, {
      params: params,
    });
  }

  indexRolesUnderRole(
    id: any,
    filter: Filter[],
    sort: Sort[],
    pageNumber = 0,
    pageSize = 5
  ): Observable<Model[]> {
    const params = this.getGridParams(filter, sort, pageNumber, pageSize);
    return this.http.get<Model[]>(`/api/` + this.baseName + `/indexRolesUnderRole/` + id, {
      params: params,
    });
  }

  createRole(obj: Model): Observable<Model> {
    return this.http.post<Model>(`/api/` + this.baseName + `/createRole`, obj);
  }

  deleteChild(obj: Model): Observable<Model> {
    return this.http.post<Model>(`/api/` + this.baseName + `/deleteChild`, obj);
  }

  removeGroup(id: number, roleId: number | string): Observable<Model> {
    return this.http.post<Model>(`/api/` + this.baseName + `/deleteGroup/` + roleId, {
        Group: id,
      });
  }

  view(id: number): Observable<Model> {
    // const filter = SharedHelper.addFilter({attribute: 'ID', operation: '=', value: id}, '');
    // return this.index(filter, '', '', 0, 1);
    return this.viewRole(id);
  }

  addRoleChildren(id: any, obj: Model): Observable<Model> {
    return this.http.post<Model>(`/api/` + this.baseName + `/addRoleChildren/` + id, obj);
  }

  updateRole(id: any, obj: Model): Observable<Model> {
    return this.http.post<Model>(`/api/` + this.baseName + `/updateRole/` + id, obj);
  }

  viewRole(Id: number) {
    return this.http.get<Model>(`/api/` + this.baseName + `/viewRole/` + Id);
  }
}
