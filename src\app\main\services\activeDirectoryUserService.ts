import {SYGSBaseService} from "../../services/SYGSBaseService";
import {Observable} from "rxjs";
import {Model} from "@ttwr-framework/ngx-main-visuals";
import {Injectable} from "@angular/core";


@Injectable({providedIn: 'root'})
export class ActiveDirectoryUserService extends SYGSBaseService {

  protected baseName = 'ActiveDirectoryUser'


  GetUserInformation(): Observable<Model> {
    return this.http.get<Model>(`/api/` + this.baseName + `/GetUserInformation`);
  }

}
