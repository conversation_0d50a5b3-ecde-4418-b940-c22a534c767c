import {Component} from '@angular/core';
import {BankWireType} from '../../Models/BankWireType.model';

import {ActivatedRoute, Router} from '@angular/router';

import {BankWireTypeService} from '../../Services/bank-wire-type.service';
import {Observable} from 'rxjs';
import {AlertService, GridDef, Model} from '@ttwr-framework/ngx-main-visuals';


@Component({
  selector: 'app-bank-wire-type-index',
  templateUrl: './bank-wire-type-index.component.html',
  styleUrls: ['./bank-wire-type-index.component.scss']
})
export class BankWireTypeIndexComponent {

  public config: GridDef = {
    title: 'BankWireType Index',
    fields: [
      {key: 'type'},
      {key: 'enName'},
      {key: 'arName'},
    ],
    actionFields: [
      {
        header: 'Actions',
        actions: [
          {
            icon: 'assets/icons/grid/ic_view.svg',
            cssClass: 'btn-view',
            tooltip: 'View',
            delegateFunction: (obj) => this.router.navigate(['view/' + obj['Id']], {relativeTo: this.activatedRoute.parent})

          },
          {
            icon: 'assets/icons/grid/ic_edit.svg',
            cssClass: 'btn-edit',
            tooltip: 'Edit',
            delegateFunction: (obj) => this.router.navigate(['update/' + obj['Id']], {relativeTo: this.activatedRoute.parent})

          },
          {
            icon: 'assets/icons/grid/ic_delete.svg',
            cssClass: 'btn-delete',
            tooltip: 'Delete',
            confirmationRequired: true,
            delegateFunction: (obj) => this.bankWireTypeService.delete(obj['Id']).subscribe(() => {
              this.alertService.success('Success Delete');
              if (this.config.refreshSubject)
                this.config.refreshSubject.next(true);
            }),
          },
        ]
      }
    ],
    fieldsDef: BankWireType.getModelDef(),
    actions: [
      {
        icon: 'assets/icons/grid/ic_create.svg',
        cssClass: 'btn-create',
        tooltip: 'Create',
        delegateFunction: () => this.router.navigate(['create/'], {relativeTo: this.activatedRoute.parent})

      },
    ],
    dataFunction: (filter, sort, pageIndex, pageSize): Observable<Model[]> => {
      return this.bankWireTypeService.index(filter, sort, pageIndex, pageSize);
    }
  };

  constructor(private bankWireTypeService: BankWireTypeService, private alertService: AlertService,
              private router: Router, private activatedRoute: ActivatedRoute,) {
  }

}
