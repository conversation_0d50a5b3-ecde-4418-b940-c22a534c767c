@use 'sass:map';
@use '@angular/material' as mat;
@import 'theme/theme_overriding';

/**********************************************/

@import 'theme/ttwr-theme-mixin/inputMixin';

@include horizontal-formGroup;
//@include border-radius-mat-form-field(0px);

/**********************************************/

:root {
  --blue: #3f4d67;
  --cyan: #268f9c;
  --yellow: #ffc107;
  --green: #ab800;
  --red: #e3694f;
  --gray: #dedede;
  --gray-light: #ededed;
  --gray-lighter: #fafafa;
  --gray-dark: #b7b7b7;
  --primary: var(--blue);
  --accent: var(--cyan);
  --success: var(--green);
  --danger: var(--red);
  --warning: var(--yellow);
  --default: var(--gray);
  --PoliceBlue: #225b64;
  --SteelTeal: #568890;
  --DesertSand: #e2ce9b;
  --SatinSheenGold: #c09d35;
  --LightSilver: #d6d6d6;
  --Argent: #c0c0c0;
  --black: #000000;
  --white: #ffffff;
  --AmericanSilver: #d0d0d0;
  //--animate-duration: 3s;
  //--animate-delay: 3s;
}


/****************************************/

html,
body {
  height: 100%;
}

a:not([href]),
a:not([href]):hover {
  color: $light-primary-text;
}

a:not([href]),
a:not([href]):hover {
  color: $light-primary-text;
}

a:hover {
  cursor: pointer;
}

svg-icon {
  display: inline-flex;
}

//menu
.mat-menu-content:not(:empty) {
  padding: 0 !important;
}

//
//.mat-drawer-container {
//  background-color: #f4f4f4;
//}

/**********************************************/

button {
  white-space: nowrap;
}

:focus,
button:focus {
  outline: none;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
textarea:-webkit-autofill,
textarea:-webkit-autofill:hover,
textarea:-webkit-autofill:focus,
select:-webkit-autofill,
select:-webkit-autofill:hover,
select:-webkit-autofill:focus {
  -webkit-box-shadow: 0 0 0 30px #fff inset !important;
}


/******************* forms ***************************/

.buttons-section,
.mat-dialog-actions {
  margin-top: 1rem;
  justify-content: flex-end;
  flex-wrap: wrap;

  > div,
  > button {
    margin: 2px 4px;
  }

  button {
    min-width: 100px;
    margin-top: 0.2rem;
    //padding-bottom: 3px;
    // line-height: 30px;
  }
}

.mat-form-field-subscript-wrapper {
  margin-top: 0.1em;
}

// add item button
.btn-fieldActions {
  padding: 0.5rem;
  display: inline-flex;
  align-items: center;

  svg {
    height: 15px;
    width: 15px;
  }
}


/*  radio input */

.mat-radio-container {
  width: 15px !important;
  height: 15px !important;

  .mat-radio-outer-circle {
    border-color: mat.get-color-from-palette(map.get($my-app-theme, primary), 500) !important;
  }

  .mat-radio-outer-circle,
  .mat-radio-inner-circle {
    width: 15px !important;
    height: 15px !important;
  }

  .mat-radio-checked .mat-radio-inner-circle {
    transform: scale(1) !important;
  }
}


/*  checkbox input */

.mat-checkbox-background,
.mat-checkbox-frame {
  //border-radius: 4px !important;
}


/*  ttwr input love  */

.search-box {
  outline: none !important;
}

//colors
// file browse icon
.browse-button {
  svg {
    path {
      fill: mat.get-color-from-palette(map.get($my-app-theme, primary), 500) !important;
    }
  }
}

.mat-form-field {
  mat-icon {
    color: mat.get-color-from-palette(map.get($my-app-theme, primary), 500);
  }

  .mat-select-arrow-wrapper {
    color: mat.get-color-from-palette(map.get($my-app-theme, primary), 500);
    padding: 0 8px;
  }
}


/******************* Main container *********************/

.menu-container {
  display: -webkit-flex;
  /* Safari */
  -webkit-flex-wrap: wrap;
  /* Safari 6.1+ */
  display: flex;
  flex-wrap: wrap;
  padding: 1rem;
  padding-#{$start-direction}: 0px;

  .menu-item {
    button:after {
      content: ' \25BC';
      margin-#{$start-direction}: 10px;
      position: relative;
      top: 4px;
    }

    margin: 3px 7px;

    > .mat-menu-item {
      > p {
        margin: 0px !important;
      }

      line-height: 2.5rem;
      height: 2.5rem;
      color: $light-primary-text;
      background-color: mat.get-color-from-palette(map.get($my-app-theme, primary), 500);

      &:hover,
      &:focus,
      &.active,
      &[aria-expanded='true'] {
        background-color: mat.get-color-from-palette(map.get($my-app-theme, primary), 700);
      }
    }
  }
}

.mat-menu-panel {
  min-height: auto !important;
  max-height: 200px !important;

  &::-webkit-scrollbar {
    width: 20px;
  }

  &::-webkit-scrollbar-thumb {
    background: mat.get-color-from-palette(map.get($my-app-theme, primary), 400);
    border-radius: 3px;
    border-left: 3px solid white;
    border-right: 3px solid white;
  }

  &::-webkit-scrollbar-track {
    background: var(--gray-light);
    border: 8px solid white;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: mat.get-color-from-palette(map.get($my-app-theme, primary), 600);
  }
}

//
mat-sidenav-content.mat-drawer-content {
  display: flex;
  flex-direction: column;
  min-height: 78vh;

  main {
    flex: 1 1 auto;

    .main-container {
      width: 90%;
      margin-bottom: 1rem;
    }
  }
}

mat-toolbar {
  ul.navigation-items {
    display: flex;
    align-items: center;
    justify-content: center;
    list-style: none;
    margin-bottom: 0;
    flex: 1 1 auto;
    height: 100%;
    padding: 0;

    li {
      &:not(:first-child) {
        margin-#{$start-direction}: 1rem;
      }

      a {
        display: flex;
        align-items: center;

        .logo {
          margin: 0.2rem 1rem;
        }

        svg-icon {
          svg {
            width: 20px;
            height: 20px;
          }
        }
      }

      label {
        color: $light-primary-text;
        margin-bottom: 0;
      }
    }
  }
}

footer {
  margin-top: 1rem;
  width: 100%;
  padding: 1rem 5% 0.5rem 5%;
  background-color: #fff;
  display: flex;
  flex-direction: row;
  justify-content: space-between;

  .footer-info {
    display: flex;
    flex-direction: row;
    flex: 0 0 46%;
  }

  .logo-footer {
    width: 79px;
    margin: -30px 6px;
  }

  .footer-copyright {
    display: flex;
    flex-direction: row;
    padding-top: 5px;
    text-align: left;
    color: mat.get-color-from-palette(map.get($my-app-theme, primary), 500);

    .ttwr {
      color: mat.get-color-from-palette(map.get($my-app-theme, primary), 500);
      font-weight: 600;
      cursor: pointer;

      &:hover {
        color: mat.get-color-from-palette(map.get($my-app-theme, accent), 500);
      }
    }
  }
}


/************* Start index pages ************/

ttwr-grid {
  .index-header {
    justify-content: flex-end;
    flex-wrap: wrap;

    .btn {
      line-height: 1.7 !important;
      margin-top: 0.2rem;

      svg,
      svg path {
        //fill:$dark-primary-text;
      }
    }
  }

  .table-responsive {
    overflow-y: hidden;
  }

  .mat-table {
    width: 100%;
    //border-collapse: separate;
    -webkit-box-shadow: null;
    -moz-box-shadow: null;
    box-shadow: null;

    .mat-header-row {
      min-height: 37px !important;

      .mat-header-cell {
        background-color: mat.get-color-from-palette(map.get($my-app-theme, primary), 500);
        color: $light-primary-text;
        text-align: center !important;

        .mat-sort-header-container {
          justify-content: center;
        }
      }

      .header-action-cell {
        background-color: mat.get-color-from-palette(map.get($my-app-theme, primary), 500);
      }
    }

    tr.mat-row,
    tr.mat-footer-row {
      height: 40px !important;
    }

    mat-header-cell,
    td.mat-cell,
    td.mat-footer-cell {
      background-clip: padding-box;
      border: 3px solid transparent !important;
      text-align: center;
      vertical-align: middle;
    }

    .mat-cell {
      &.mat-column-Actions {
        padding-top: 3px !important;
        white-space: nowrap;
      }

      span a {
        svg {
          width: 20px;
          height: 20px;
        }
      }

      .btn-action-col {
        white-space: nowrap;
        //color:$bg_primary_dark;
        padding: 5px 10px;
        margin: 0px 5px;
        border-radius: 15px;
        background-color: #e4e4e4;
        box-shadow: 0px 2px 3px 0px #9e9e9e;

        &:hover {
          cursor: pointer;
          background-color: #cbcbcb;
        }
      }
    }

    a:not([href]):not([tabindex]) {
      color: mat.get-color-from-palette(map.get($my-app-theme, accent), 500);
    }
  }

  .table-row:focus {
    background-color: mat.get-color-from-palette(map.get($my-app-theme, accent), 50);
  }
}


/* Start  paginator */

.mat-paginator,
.mat-paginator-page-size .mat-select-trigger {
  color: mat.get-color-from-palette(map.get($my-app-theme, primary), 500);
}

.mat-paginator {
  background: transparent;

  .mat-paginator-page-size {
    padding-top: 11px;

    .mat-form-field-appearance-legacy .mat-form-field-infix {
      padding: 0 2px;
      border-top: 0px;
      line-height: 18px;
    }

    .mat-form-field-type-mat-select {
      .mat-select-arrow-wrapper {
        padding: 0;
      }

      .mat-form-field-flex {
        padding: 0px 7px;
        border-radius: 4px;
        border: 0.07rem solid mat.get-color-from-palette(map.get($my-app-theme, primary), 500);
      }
    }

    .mat-form-field-ripple {
      background-color: transparent;
    }

    .mat-form-field-underline {
      background-color: transparent !important;
    }

    .mat-form-field.mat-focused .mat-form-field-ripple {
      background-color: transparent !important;
    }
  }

  .mat-paginator-range-actions {
    .mat-paginator-range-label {
      margin: 0px 10px !important;
    }

    .mat-button-wrapper {
      color: mat.get-color-from-palette(map.get($my-app-theme, primary), 500);

      svg {
        display: none;
      }
    }

    .mat-paginator-navigation-next {
      .mat-button-wrapper {
        &:after {
          content: '\25C0';
          color: transparent;
          background: transparent url('../icons/grid/ic_prev.svg') center center no-repeat;
          transform: scalex(-1 * $transform-direction);
          display: flex;
          align-items: center;
        }
      }
    }

    .mat-paginator-navigation-previous {
      .mat-button-wrapper {
        &:after {
          content: '\25BA';
          color: transparent;
          background: transparent url('../icons/grid/ic_next.svg') center center no-repeat;
          transform: scalex(-1 * $transform-direction);
          display: flex;
          align-items: center;
        }
      }
    }
  }
}


/************* End index pages *********************/


/************* Star create/update pages ************/


/* dialog */

.ttwr-dialog {
  .mat-dialog-container {
    padding: 0px;
    box-shadow: null;

    .mat-dialog-title {
      padding: 0.8rem 1.5rem;
      margin-bottom: 0;
      background-color: mat.get-color-from-palette(map.get($my-app-theme, primary), 500);

      h1 {
        padding: 0;
        margin-bottom: 0;
        color: $light-primary-text;
      }

      [mat-dialog-close] {
        position: relative;
        height: 0;

        svg {
          width: 0.7rem;
        }
      }
    }

    .mat-dialog-content {
      max-height: 70vh;
      min-height: 160px;
      margin: 0;
      padding-bottom: 0.5rem;

      .header-content {
        h1 {
          font-size: 1rem;
        }
      }
    }
  }
}

.ttwr-confirm-dialog {
  .mat-dialog-container {
    .mat-dialog-content {
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
  }
}

//for customizing confirm-dialog's project
//.base-confirm-dialog
//{
//  min-width: 35vw;
//
//  .mat-dialog-container {
//    border-radius: 0.5rem;
//    padding: 0;
//    app-confirmation-dialog {
//      display: flex;
//      flex-direction: column;
//      align-items: center;
//
//      .body-content {
//        overflow: initial;
//      }
//
//      .mat-dialog-content {
//        text-align: center;
//        font-size: 1rem;
//        font-weight: bold;
//        width: 100%;
//        padding: 3rem 0 2rem;
//      }
//
//      .mat-dialog-actions {
//        width: 100%;
//        margin: 0;
//        padding: 0;
//        min-height: 2.6rem;
//        align-items: stretch;
//
//        button {
//          flex: 1 1 50%;
//          margin: 0 !important;
//          border-radius: 0;
//
//          &:first-child {
//            @include button-variant(mat.get-color-from-palette($mat-blue-base, 500), mat.get-color-from-palette($mat-blue-base, 500));
//          }
//
//          &:nth-child(2) {
//            @include button-variant(mat.get-color-from-palette($mat-gray-base, 500), mat.get-color-from-palette($mat-gray-base, 500));
//          }
//        }
//
//      }
//    }
//  }
//}

/************* End index pages ********************/


/************* Start Details View *****************/

.details-view {
  padding: 5px 15px 0px 15px !important;
  //background-color: #f4f4f4;
  margin-bottom: 2px;
}


/************* Start Tree ************/

.tree-search {
  margin-top: 25px;

  svg-icon {
    margin: 15px 10px;

    svg {
      fill: mat.get-color-from-palette(map.get($my-app-theme, primary), 500);
    }
  }
}

.tree-container {
  padding: 1rem 0.3rem;
}

mat-tree {
  li {
    overflow: hidden;
  }

  > mat-nested-tree-node > li,
  > mat-tree-node > li {
    //border-radius: 0.8rem;
    //@include border-bottom-radius(0);
  }

  > mat-nested-tree-node > li > ul {
    //padding-inline-start: 0;
  }

  ul,
  li {
    //border-bottom: 0.15rem solid #fff;
  }

  .node-content {
    //font-size: 1rem;
  }

  .background-color-1 {
    background-color: mat.get-color-from-palette(map.get($my-app-theme, primary), 500) !important;

    .node-content,
    mat-icon {
      color: $light-primary-text;
    }
  }

  //.background-color-3 {
  //  .node-content, mat-icon {
  //    opacity: 0.9;
  //  }
  //}
  .mat-tree-node,
  .mat-nested-tree-node {
    //color: $dark-primary-text;
  }

  //.background-highlight:not(.background-color-1):hover {
  //  background-color: #cdcdcd !important;
  //}
}


/************* End Tree ************/


/************* Start Tab ************/

.mat-tab-group {
  padding-top: 1rem;

  .mat-tab-label-container {
    .mat-tab-label {
      height: 38px !important;
      background-color: #e4e4e4;
      color: mat.get-color-from-palette(map.get($my-app-theme, primary), 500);
      opacity: 1 !important;
      //@include border-top-radius(10px);
      &:not(:first-child) {
        margin-#{$start-direction}: 0.2rem;
      }

      &.mat-tab-label-active {
        background-color: mat.get-color-from-palette(map.get($my-app-theme, primary), 500);
        color: var(--white);
      }
    }
  }

  .mat-tab-body {
    border: 0.5px solid #dddd;
    //border-radius: 5px;
    border-top: 0px;
    padding: 20px;
  }

  &.mat-primary .mat-ink-bar {
    background-color: transparent;
  }
}


/************* End Tab ************/


/****************** mat-tooltip *******************/

.mat-tooltip {
  //background-color: mat.get-color-from-palette(map.get($my-app-theme, accent), 500);
  //opacity: 0.8;
  margin: 3px 14px !important;
}


/***************** toast *********************/

.toast-top-left {
  top: 67px !important;
}


/************* Start Other ************/

legend {
  width: 20% !important;
}

legend a.ng-tns-c9-6.ng-star-inserted {
  width: 100% !important;
}

.ui-dropdown,
.border-input {
  border: 1px solid mat.get-color-from-palette($mat-gray-base, 900) !important;
}

p-dropdown.form-control {
  padding: 0 !important;
}

.ui-inputtext {
  margin: 0;
}

.form-control .ui-dropdown {
  width: 100% !important;
  height: 100% !important;
}


/************* Start Scrollbar ************/

::-webkit-scrollbar {
  width: 10px;
  height: 8px;
}


/* Track */

::-webkit-scrollbar-track {
  background: #e9ecef;
  border: 2px solid #f7f7f7;
}


/* Handle */

::-webkit-scrollbar-thumb {
  background: #bdbdbd;
  //border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #9e9e9e;
}

mat-table::-webkit-scrollbar-thumb {
  background: mat.get-color-from-palette(map.get($my-app-theme, primary), 400);
}

mat-table::-webkit-scrollbar-thumb:hover {
  background: mat.get-color-from-palette(map.get($my-app-theme, primary), 700);
}


/************** Media ************/

@media print {
  mat-sidenav-container.mat-drawer-container.mat-sidenav-container.isPrinting {
    display: none;
  }
  app-print-layout {
    display: block;
  }
  table.table-bordered {
    border: 1px solid black !important;
  }
  table.table-bordered > thead > tr > th {
    border: 1px solid black !important;
  }
  table.table-bordered > tbody > tr > td {
    border: 1px solid black !important;
  }
  .no-print,
  .no-print * {
    display: none !important;
  }
}


/*******responsiveness ***********/

@media (max-width: 1024px) {
  .ttwr-dialog {
    width: 70% !important;
  }
}

@media (max-width: 992px) {
  .ttwr-dialog {
    width: 90% !important;
    max-width: 90vw !important;
  }
}

@media (max-width: 600px) {
  mat-sidenav-content.mat-drawer-content {
    mat-toolbar {
      padding: 0 0.5rem !important;

      ul.navigation-items {
        li {
          &:not(:first-child) {
            margin-#{$start-direction}: 0.4rem;
          }

          a {
            .logo {
              margin: 0.2rem;
              width: 50%;
            }

            &.user-link svg {
              width: 30px !important;
            }
          }
        }
      }
    }
  }
  .footer .footer-info {
    flex-direction: column !important;
  }
}

//Logo Size For Toolbar

.logo-size {
  width: 70px;
  height: 50px;
}

//Side nav

.sidenav-style {
  border-top-#{$end-direction}-radius: 34px;
  border-bottom-#{$end-direction}-radius: 34px;
  height: 70% !important;
  margin-top: 55px !important;
}

.mat-expansion-panel-header {
  padding-#{$start-direction}: 0px !important;
  margin-bottom: 5px;
}


mat-expansion-panel {
  background-color: transparent !important;
}


.item-content, .sub-item {
  padding-#{$start-direction}: 5px !important;
}

.icons-position {
  margin-#{$end-direction}: 5%;
}

.li-border {
  border-left: 1px solid rgba(var(--white), 0.89);
  border-right: 1px solid rgba(var(--white), 0.89);
  width: 47px;
  padding-#{$start-direction}: 11px;
}

//Change mat-sidenav-container height to disable scroll

mat-sidenav-container {
  height: 78%;
}

//Table style
th.header-action-cell {
  display: block;
  padding: 0px;
  margin-top: 1px;
}

tr.mat-header-row {
  height: 3px !important;
}

.ttwr-grid {
  width: 70%;
  justify-content: center;
  align-items: center;
}

th.header-action-cell {
  display: table-cell !important;
  background-color: var(--white) !important;
  padding: 5px !important;
}

th {
  color: var(--black);
  font-size: 0.4em;
  padding: 1px !important;
  height: 15px;
}

th:first-child {
  border-top-#{$start-direction}-radius: 11px !important;

  .mat-sort-header {
    border-top-#{$start-direction}-radius: 11px !important;
  }
}

th:last-child {
  border-top-#{$end-direction}-radius: 11px !important;
}

td.mat-cell.cdk-cell.ng-star-inserted {
  background-color: var(--white) !important;
  color: rgba(var(--PoliceBlue), 0.79) !important;
  border-bottom-width: 0px !important;
}

tr:last-child {
  td:first-child {
    border-bottom-#{$start-direction}-radius: 11px;
  }

  td:last-child {
    border-bottom-#{$end-direction}-radius: 11px;
  }
}

tbody:before {
  content: "@";
  display: block;
  line-height: 5px;
  text-indent: -99999px;
}

div .mat-header-cell {
  background-color: var(--white) !important;
  color: var(--black) !important;
  border-bottom-width: 0px !important;
}

table.mat-table {
  background-color: transparent;
}

.table-responsive {
  background-color: transparent;
}

.mat-focus-indicator.mat-tooltip-trigger.mat-raised-button.mat-button-base.btn.btn-primary.btn-create {
  width: 78px;
  height: 26px;
  background-color: var(--SteelTeal) !important;
  padding: 5px 10px !important;
}

.btn-export {
  span {
    font-size: 11px;
    color: var(--white);
  }
}

.mat-button-wrapper {
  justify-content: center;
  align-items: center;
  display: flex;
}

.mat-sort-header-content, div.mat-header-cell {
  color: var(--SatinSheenGold) !important;
  font-weight: 700;
}

#ic_add {
  width: 15px;
}

// Scroll style
::-webkit-scrollbar {
  height: 7px !important;
}

::-webkit-scrollbar-thumb {
  background: var(--AmericanSilver);
  border-radius: 10px;
}

::-webkit-scrollbar-track {
  margin-left: 18vw;
  margin-right: 26vw;
}

//Breadcrumb

.breadcrumb {
  li:after {
    content: "/";
  }

  a {
    padding-#{$end-direction}: 5px;
  }

  li {
    padding-#{$start-direction}: 5px !important;
  }

  li:first-child {
    padding-#{$start-direction}: 0px !important;
  }

  li:last-child:after {
    content: "" !important;
  }

  li:last-child:before {
    content: "" !important;
  }

  li:nth-child(2):before {
    content: "" !important;
  }

  li:nth-child(2) {
    padding-#{$end-direction}: 0px !important;

    a {
      padding-#{$start-direction}: 5px;
    }
  }
}

//Master details design

.mat-ripple.mat-tab-label.mat-focus-indicator {
  border-radius: 11px;
  background-color: var(--SteelTeal) !important;
  color: var(--white);
}

// Button done style

.Done-style {
  background-color: var(--SteelTeal);
  color: var(--white);
  border-color: var(--SteelTeal);
  border-radius: 6px;
}

.Done-style:hover {
  background-color: var(--PoliceBlue);
  color: var(--white);
  border-color: var(--SteelTeal);
  border-radius: 6px;
}

h1 {
  color: var(--SatinSheenGold);
}

.label-color {
  color: var(--SatinSheenGold);
}

.danger-color {
  color: var(--danger);
}

.master-view-design {
  background-color: var(--gray-lighter);
  border-radius: 11px;
}

//Login style

.forget-password-style {
  float: #{$start-direction};
  color: var(--cyan) !important;
}

.remember-me-style {
  float: #{$end-direction};
}

//filter design

.mat-checkbox-checkmark-path {
  stroke: var(--PoliceBlue) !important;

}

.mat-checkbox-checked .mat-checkbox-background,
.mat-checkbox-indeterminate .mat-checkbox-background {
  background-color: var(--LightSilver) !important;

}

.date-style {
  .mat-focus-indicator {
    top: 8px !important;

    svg {
      width: 100%;
    }
  }

  .mat-form-field-prefix {
    padding-left: 5px;
  }
}

.filter-design {
  mat-panel-title {
    padding: 5px;
  }

  .mat-checkbox-inner-container {
    margin-right: 0px !important;
  }

  .mat-checkbox-frame {
    border-color: var(--SteelTeal) !important;
  }

  mat-checkbox {
    label {
      display: flex;
      align-items: center;
      height: 100%;
      color: rgba(var(--black), 0.54);
    }
  }
}


//Submit button style

.submit-style, .submit-style:hover, .submit-style:focus {
  background-color: var(--SteelTeal);
  color: var(--white);
  height: 31px;
  width: 100px;
  padding: 0px !important;
  float: #{$end-direction};
  justify-content: left;

  span {
    justify-content: center;
  }
}

.isSuccessfulDesign {
  align-items: center;
}

.field-design {
  .mat-form-field {
    width: 90% !important;
  }
}

.mat-column-valueDate {
  width: 10%;
}

// Confirm dialog design

.ttwr-confirm-dialog, .ttwr-dialog {
  .mat-dialog-container {
    border-radius: 11px;
  }

  .mat-dialog-title {
    background-color: var(--SteelTeal) !important;
  }

  .btn-primary {
    background-color: var(--SteelTeal) !important;
    color: var(--white) !important;
    border-color: var(--SteelTeal) !important;
    border-radius: 6px !important;
  }

  .btn-danger {
    background-color: rgba(var(--black), 16) !important;
    color: var(--SteelTeal) !important;
    border-color: rgba(var(--black), 16) !important;
    border-radius: 6px !important;
  }

  .btn-danger:hover, .btn-primary:hover {
    box-shadow: -1px 1px 7px 0px var(--Argent);
  }

}

.ttwr-confirm-dialog {
  width: 30% !important;
}


//App menu buttons

app-menu {
  button {
    background-color: var(--SteelTeal) !important;
    border-radius: 6px;
  }
}

.user-name-design {
  margin-top: 10px !important;
  padding-#{$start-direction}: 5px !important;
}

//Button primary and danger design

.btn-info {
  background-color: var(--SteelTeal);
  color: var(--white);
  border-color: var(--SteelTeal);
  border-radius: 6px;
}

.btn-info:hover, .btn-info:focus {
  background-color: var(--SteelTeal);
  color: var(--white);
  border-color: var(--SteelTeal);
  border-radius: 6px;
  box-shadow: -1px 1px 7px 0px var(--Argent);
}

.btn-danger {
  background-color: rgba(var(--black), 16);
  color: var(--SteelTeal);
  border-color: rgba(var(--black), 16);
  border-radius: 6px;
}

.btn-danger:hover, .btn-danger:focus {
  background-color: rgba(var(--black), 16);
  color: var(--SteelTeal);
  border-color: rgba(var(--black), 16);
  border-radius: 6px;
  box-shadow: -1px 1px 7px 0px var(--Argent);
}

.btn-warning {
  background-color: var(--DesertSand);
  color: var(--white);
  border-color: var(--SatinSheenGold);
  border-radius: 6px;
}

.btn-warning:hover, .btn-warning:focus {
  background-color: var(--DesertSand);
  color: var(--white);
  border-color: var(--SatinSheenGold);
  border-radius: 6px;
  box-shadow: -1px 1px 7px 0px var(--Argent);
}

.text-preline {
  white-space: pre-line;
}

.cdk-column-ctAccount.mat-column-ctAccount {
  direction: ltr !important;
}

.ct-account-value {
  direction: ltr;
  display: flex;
  justify-content: end;
}

ttwr-form{
  ttwr-input-date > div:nth-of-type(1) {
    height: 0px !important;
  }

  ttwr-input-select > div:nth-of-type(1) {
    height: 0px !important;
  }
}

