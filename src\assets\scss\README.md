# Base angular Style
How use it?


## Main style
**_main.scss** is the file where you write your general code one time for multi-language website -two directions-.

First we create two config files **_direction-ltr.scss** and **_direction-rtl.scss** ,
then add @import the correct config direction file and the inner **_main.scss** file in each of them.

--------------------------------------

How to use the variables in **config-directions Files**?
* CSS Directions & text-align:

    **$start-direction** for LTR languages is left & for Semitic(RTL) languages is right. **$direction** for LTR languages is ltr & for Semitic languages is rtl.
 
     Example:
    ```
    body{
      direction: $direction;
      text-align: $start-direction;
    }
    ```
* CSS Positions:

    In Positioning, put the right and left in variables according to your needs.
    
    ```
    position: absolute;
    #{$start-direction}: 50%;}
    ```
* c<PERSON>gins/Paddings/Borders:
    
    Example:
    ```
    margin-#{$end-direction}: 10px;
    padding-#{$start-direction}: 10px;
    border-#{$end-direction}-width: 2px;
    ```

* CSS Floats:
    ```
    float: $start-direction;
    ```

* CSS Transform: translateX:
    
    When working with transform you need to change number from positive to negative or vice versa.
    Let’s say we have box transform:translateX(200px) in English. We multiply it by $transform-direction (=1), in English the result would be 200px (1*200px), but in rtl it would be -200px (-1*200px).
    
    Example:
    ```
    transform: translateX(200px * $transform-direction);
    ```

## Theme
 A theme consists of configurations for the individual color and typography systems in Angular Material. 
we can edit it by two ways:
* Defining a custom theme like **customizing-ar.scss** (https://material.angular.io/guide/theming#defining-a-custom-theme)

    A typical theme file will look something like this:
     ```
    @import '@angular/material/theming';
    // Plus imports for other components in your app.
    
    // Include the common styles for Angular Material. We include this here so that you only
    // have to load a single css file for Angular Material in your app.
    // Be sure that you only ever include this mixin once!
    @include mat-core();
    
    // Define the palettes for your theme using the Material Design palettes available in palette.scss
    // (imported above). For each palette, you can optionally specify a default, lighter, and darker
    // hue. Available color palettes: https://material.io/design/color/
    $candy-app-primary: mat-palette($mat-indigo);
    $candy-app-accent:  mat-palette($mat-pink, A200, A100, A400);
    
    // The warn palette is optional (defaults to red).
    $candy-app-warn:    mat-palette($mat-red);
    
    // Create the theme object. A theme consists of configurations for individual
    // theming systems such as `color` or `typography`.
    $candy-app-theme: mat-light-theme((
      color: (
        primary: $candy-app-primary,
        accent: $candy-app-accent,
        warn: $candy-app-warn,  
      )
    ));
    
    // Include theme styles for core and each component used in your app.
    // Alternatively, you can import and @include the theme mixins for each component
    // that you are using.
    @include angular-material-theme($candy-app-theme);
     ```

* Add overriding style

    There is two file I have built for make it easier and the **Angular theme** is very restricted.
    * **theme-overriding.scss**
    
        this file is general for all projects, so you can edit it in your own project and add your additonal code in **_main.scss** .
        
        For example, to remove the padding from a dialog:
        
             // Add this to your global stylesheet after your theme setup
             .mat-dialog-container {
               padding: 0;
             }
             
    * **ttwr-theme-mixin/InputMixin.scss**
       there is multi mixins for "mat-form-field".
        
        Example: **login.component.scss**
    
             
        @import "../../../assets/scss/theme/ttwr-theme-mixin/inputMixin";
        
        $important: !important; // we use it when we need
        $host-ngDeep: ":host ::ng-deep"; // we use it just in component.css , we don't need it in _main.scss file
        @include border-underline;
      
        
        //we can customize one field by defining its class in component.ts file like:
        //fields: [{key: 'username',cssClass:{field:'form-group-username'}}]
        //then add this lines
        //@include border-outline(".form-group-username");
        //@include border-radius-mat-form-field(5px,".form-group-username");
        
_____________________________________  
## Ttwr Framework Classes

**1] ttwr-grid**

* index-header

    It includes all buttons above the tables
    - you can change its location to the right and the left with "justify-content" or invert its by "flex-direction".
    
    - you can change the buttons' order by changing the "order" property of each btn.

    Notes:
    The order property we can only use it with "display: flex".
    
    -If you change the index-header direction, you will have to change the "margin" property because the first-child has changed.
    
    -you can only change the icon of the Create button and all the added buttons. The remaining button icons (filter, expand, print and export to ...) are fixed. You can only change their color and size.

    - The icons' color can be changed by specifying the "fill" or "stroke" properties of the tags contained in the svg.

* btn-action-col

    It expresses all the added buttons without icons to the actions column -by default-, and it should be added if it doesn't exist.



**2] ttwr-form**

* buttons-section

    It includes all buttons at the bottom of the Form
    - you can change its location to right and left with justify-content or invert its by "flex-direction".

* btn-fieldActions

It expresses the buttons that added next to the input field.

* browse-button, search-button, clear-search-button

    It express the buttons within the fields in ttwr-input, according to the type of field.

**3] The Dialogs**
* The "ttwr-dialog" class should be added in order for the design to take place

* the "base-confirm-dialog" class should be added if the design of an app-confirmation-dialog is different from a regular dialog (ttwr-dialog)

    
