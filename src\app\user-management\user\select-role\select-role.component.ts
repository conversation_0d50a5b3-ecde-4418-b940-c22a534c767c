import {Component, OnInit} from '@angular/core';
import {UserService} from "../../services/user.service";
import {AlertService, LanguageService, TtwrBaseSelectComponent} from "@ttwr-framework/ngx-main-visuals";

@Component({
  selector: 'app-select-role',
  templateUrl: './select-role.component.html',
})
export class SelectRoleComponent extends TtwrBaseSelectComponent implements OnInit {

  constructor(private userService: UserService, private languageService: LanguageService,
              private alertService: AlertService) {
    super();
    this.selectValue = 'id';
    this.selectName = 'name';
  }

  // getFunc() {
  //   return this.userService.getRoles();
  // }


  ngOnInit(): void {
    this.userService.getRoles().subscribe((res: any) => {
      if (res) {
        res.items.forEach((item: any) => {
          this.options.push({value: item.id, label: this.languageService.getLang(item.name)})
        })
      }

    }, (err) => {
      this.alertService.error(err)
    })
    this.doneLoading = true;
  }

}
