import {Component} from '@angular/core';
import {UserGroup} from '../../models/user-group.model';
import {ActivatedRoute, Router} from '@angular/router';
import {UserGroupService} from '../../services/user-group.service';
import {Observable, throwError} from 'rxjs';
import {AlertService, GridDef, Model} from '@ttwr-framework/ngx-main-visuals';
import {MatLegacyDialog as MatDialog} from '@angular/material/legacy-dialog';
import {UserGroupCreateComponent} from '../user-group-create/user-group-create.component';
import {UserGroupUpdateComponent} from '../user-group-update/user-group-update.component';
import {AuhtItemService} from '../../services/auht-item.service';
import {catchError} from "rxjs/operators";

@Component({
  selector: 'app-user-group-index',
  templateUrl: './user-group-index.component.html',
  styleUrls: ['./user-group-index.component.scss'],
})
export class UserGroupIndexComponent {
  public config: GridDef = {
    title: 'User groups',
    fields: [{key: 'Name'}, {key: 'Code'}],
    actionFields: [
      {
        header: 'Actions',
        actions: [
          {
            icon: 'assets/icons/grid/ic_view.svg',
            tooltip: 'View',
            cssClass: 'btn-view',
            delegateFunction: (obj) =>
              this.router.navigate(['view/' + obj['Id']], {relativeTo: this.activatedRoute.parent}),
          },
          {
            icon: 'assets/icons/grid/ic_edit.svg',
            tooltip: 'Edit',
            cssClass: 'btn-edit',
            delegateFunction: async (obj) => {
              const dialogRef = this.dialog.open(UserGroupUpdateComponent, {
                width: '50%',
                panelClass: 'ttwr-dialog',
                data: {obj: obj},
              });
              dialogRef.afterClosed().subscribe((res: any) => {
                if (res && this.config.refreshSubject) {
                  this.config.refreshSubject.next(true);
                }
              });
            },
          },
          {
            icon: 'assets/icons/grid/ic_delete.svg',
            tooltip: 'Delete',
            cssClass: 'btn-delete',
            confirmationRequired: true,
            delegateFunction: (obj) =>
              this.userGroupService.delete(obj['Id']).subscribe(() => {
                this.alertService.success('Success Delete');
                if (this.config.refreshSubject) this.config.refreshSubject.next(true);
              }),
          },
        ],
      },
    ],
    fieldsDef: UserGroup.getModelDef(),
    actions: [
      {
        icon: 'assets/icons/grid/ic_create.svg',
        tooltip: 'Create',
        cssClass: 'btn-create',
        delegateFunction: () => {
          const dialogRef = this.dialog.open(UserGroupCreateComponent, {
            width: '50%',
            panelClass: 'ttwr-dialog',
          });
          dialogRef.afterClosed().subscribe((res: any) => {
            if (res && this.config.refreshSubject) {
              this.config.refreshSubject.next(true);
            }
          });
        },
      },
    ],
    dataFunction: (filter, sort, pageIndex, pageSize): Observable<Model[]> => {
      return this.userGroupService.index(filter, sort, pageIndex, pageSize).pipe(catchError(err => {
        this.alertService.error(err.messag);
        return throwError(err.messag);
      }));
    },
  };

  constructor(
    private userGroupService: UserGroupService,
    private dialog: MatDialog,
    private auhtItemService: AuhtItemService,
    private alertService: AlertService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
  ) {
  }
}
