import {Component, Input, OnInit} from '@angular/core';
import {MenuItem} from "../models/MenuItem";

@Component({
  selector: 'app-menu',
  templateUrl: './menu.component.html',
  styleUrls: ['./menu.component.scss'],
})
export class MenuComponent implements OnInit {
  @Input() items!: MenuItem[];

  ngOnInit() {
    this.items &&
    this.items.forEach((item) => {
      // TODO must be used with dfs
      if (item.items) {
        let menuVisible = false;
        item.items.forEach((subItem: any) => {
          subItem.visible =

          menuVisible = menuVisible || subItem.visible;

          // TODO: remove these lines
          menuVisible = true;
          subItem.visible = true;
        });

      } else
      // TODO: remove these lines
      item.visible = true;
    });
  }
}
