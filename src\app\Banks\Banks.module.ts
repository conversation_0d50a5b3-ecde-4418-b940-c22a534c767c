import {MaterialModule, Model} from "@ttwr-framework/ngx-main-visuals";
import {NgModule} from "@angular/core";
import {BanksRouting} from "./Banks.routing";
import {BanksMainComponent} from './banks-main/banks-main.component';
import {BanksService} from "./Services/banks.service";
import {BanksCreateComponent} from "./BanksComponents/banks-create/banks-create.component";
import {BanksIndexComponent} from "./BanksComponents/banks-index/banks-index.component";
import {BanksUpdateComponent} from "./BanksComponents/banks-update/banks-update.component";
import {BanksViewComponent} from "./BanksComponents/banks-view/banks-view.component";
import {BanksComponent} from "./BanksComponents/banks.component";
import {SharedModule} from "../shared/shared.module";
import {LOVBankListComponent} from "./LOVBankList/LOVBankList.component";
import {LOVBankCustomerComponent} from "./LOVBankCustomer/LOVBankCustomer.component";
import {
  LOVREBBranchesListComponent
} from "./LOVBankBranchesList/LOVREBBranchesList.component";
import {LOVBankBranchesListComponent} from "./LOVBankBranchesList/LOVBankBranchesList.component";

@NgModule({
  imports: [
    BanksRouting,
    SharedModule
  ],
  exports: [],
  declarations: [
    BanksMainComponent,
    BanksCreateComponent,
    BanksIndexComponent,
    BanksUpdateComponent,
    BanksViewComponent,
    BanksComponent,
    LOVBankListComponent,
    LOVBankCustomerComponent,
    LOVREBBranchesListComponent,
    LOVBankBranchesListComponent
  ],
  providers: [
    BanksService
  ]
})

export class BanksModule extends Model {

}
