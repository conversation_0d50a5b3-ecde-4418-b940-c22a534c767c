<mat-accordion class="mat-accordion-position">
  <span *ngFor="let item of items"
        [ngClass]="{'parent-without-children': item.items == undefined,'parent-with-child':item.items!=undefined}">
    <span *ngIf="item.visible && item.items">
      <mat-expansion-panel
        class="mat-elevation-z mat-expansion-color"
        (opened)="isExpanded = true"
        #rla="routerLinkActive"
        [expanded]="isExpanded ? rla.isActive : false"
        [routerLinkActive]="['active-sub-menu']"
        [routerLinkActiveOptions]="{ exact: true }">
        <mat-expansion-panel-header class="mat-header-style">
          <mat-panel-title>
            <!-- Cabeceras del submenu -->
            <div
              fxLayout="row"
              class="item-content">
              <span *ngIf="isExpanded" class="label">{{(item.label ? item.label : '') | i18n }}</span>
              <svg-icon src="{{ item.icon }}" *ngIf="item.icon"></svg-icon>
            </div>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <span *ngFor="let subItem of item.items">
          <mat-list-item
            *ngIf="subItem.visible"
            [routerLink]="subItem.routerLink"
            (click)="!!item.command && item.command()"
            [routerLinkActive]="['active']"
            [routerLinkActiveOptions]="{ exact: true }"
            (click)="closeListItemExpanded($event) && sidenavPosition != 'side' && ref.close()">
            <!-- Entradas de cada submenú  -->
            <div
              class="item-content sub-item"
            >
              <p> {{ (subItem.label ? subItem.label : '') | i18n }}</p>
            </div>
          </mat-list-item>
        </span>
      </mat-expansion-panel>
    </span>
    <span *ngIf="item.items == undefined">
      <mat-list-item
        *ngIf="item.visible"
        (click)="!!item.command && item.command()"
        [routerLink]="item.routerLink"
        [routerLinkActive]="['active']"
        [routerLinkActiveOptions]="{ exact: true }"
        (click)="
          closeListItemExpanded($event) &&
            sidenavPosition != 'side' &&
            ref.close()
        "
      >
        <!-- Entradas principales -->
        <div
          fxLayout="row"
          class="item-content"
        >
           <span class="label">{{
             (item.label ? item.label : '') | i18n
             }}</span>
          <svg-icon *ngIf="item.icon" src="{{ item.icon }}"></svg-icon>

        </div>
      </mat-list-item>
    </span>
  </span>
</mat-accordion>
