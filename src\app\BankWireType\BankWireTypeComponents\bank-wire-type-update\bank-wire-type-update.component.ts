import {Component, OnInit} from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import {BankWireType} from '../../Models/BankWireType.model';
import {BankWireTypeService} from '../../Services/bank-wire-type.service';
import {AlertService,Helper, FormDef} from '@ttwr-framework/ngx-main-visuals';

@Component({
  selector: 'app-bank-wire-type-update',
  templateUrl: './bank-wire-type-update.component.html',
  styleUrls: ['./bank-wire-type-update.component.scss']
})
export class BankWireTypeUpdateComponent implements OnInit {

  public config: FormDef = {
    title: 'BankWireType Update',
    fields: [
      {key: 'type'},
      {key: 'enName'},
      {key: 'arName'},
    ],
    fieldsDef: BankWireType.getModelDef(),
    actionFields: [
      {
        label: 'Submit',
        cssClass: 'btn-primary',
        delegateFunction: (obj) => this.bankWireTypeService.update(this.config.obj?.['Id'], obj).subscribe(() => {
          this.alertService.success('Success Update');
          this.router.navigate(['view/' + this.config.obj?.['Id']], {relativeTo: this.activatedRoute.parent});
        }),
      },
      {
        label: 'Cancel',
        cssClass: 'btn-danger',
        causeValidate: false,
        delegateFunction: () => this.router.navigate(['view/' + this.config.obj?.['Id']], {relativeTo: this.activatedRoute.parent}),
      }
    ]
  };

  constructor(private route: ActivatedRoute, private router: Router,
              private bankWireTypeService: BankWireTypeService, private alertService: AlertService, private activatedRoute: ActivatedRoute) {
  }

  ngOnInit() {
     if(this.route.snapshot.data.obj)
       this.config.obj =Helper.deepCopy(this.route.snapshot.data.obj)
  }

}
