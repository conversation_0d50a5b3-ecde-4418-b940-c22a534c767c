{"name": "angular12-base-project", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve --port 4202", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^15.2.10", "@angular/cdk": "^15.2.9", "@angular/common": "^15.2.10", "@angular/compiler": "^15.2.10", "@angular/core": "^15.2.10", "@angular/forms": "^15.2.10", "@angular/material": "^15.2.9", "@angular/platform-browser": "^15.2.10", "@angular/platform-browser-dynamic": "^15.2.10", "@angular/router": "^15.2.10", "@ttwr-framework/ngx-main-visuals": "^2.0.6", "angular-crumbs": "^3.0.1", "bootstrap": "^5.1.3", "jwt-decode": "^3.1.2", "ngx-permissions": "^8.1.1", "rxjs": "^7.4.0", "tslib": "^2.3.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "^15.2.11", "@angular/cli": "^15.2.11", "@angular/compiler-cli": "^15.2.10", "@types/jasmine": "~3.8.0", "@types/lodash": "^4.14.175", "@types/node": "^12.11.1", "jasmine-core": "~3.8.0", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.0.3", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "typescript": "~4.9.5"}}