import {Component, Inject, OnInit} from '@angular/core';
import {MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA, MatLegacyDialogRef as MatDialogRef} from '@angular/material/legacy-dialog';
import {UserGroup} from '../../models/user-group.model';
import {UserGroupService} from '../../services/user-group.service';
import {AlertService, FormDef, Helper} from '@ttwr-framework/ngx-main-visuals';

@Component({
  selector: 'app-add-group-users',
  templateUrl: './add-group-users.component.html',
  styleUrls: ['./add-group-users.component.scss'],
})
export class AddGroupUsersComponent implements OnInit {
  dialogTitle='Add Users';
  public config: FormDef = {
    titleHidden:true,
    fields: [{key: '_UserGroupUsers', parameterRequestName: 'Users'}],
    fieldsDef: UserGroup.getModelDef(),
    obj: {},
    actionFields: [
      {
        label: 'Submit',
        delegateFunction: (obj) => {
          this.userGroupService.addUsers(this.data.obj.Id, obj).subscribe(() => {
            this.alertService.success('Success Update');
            this.dialogRef.close(true);
          })
        }
      },
      {
        label: 'Cancel',
        cssClass: 'btn-danger',
        causeValidate: false,
        delegateFunction: () => this.dialogRef.close(),
      },
    ],
  };

  constructor(
    public dialogRef: MatDialogRef<AddGroupUsersComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private userGroupService: UserGroupService,
    private alertService: AlertService,
  ) {
  }

  ngOnInit() {
  }
}
