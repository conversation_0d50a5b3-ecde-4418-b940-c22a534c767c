import { Component } from '@angular/core';
import { OutgoingBankWireModel } from '../../Models/OutgoingBankWire.model';

import { ActivatedRoute, Router } from '@angular/router';

import { OutgoingBankWireService } from '../../Services/outgoing-bank-wire.service';
import { Observable } from 'rxjs';
import {
  AlertService,
  Filter,
  GridDef,
  LanguageService,
  Model,
} from '@ttwr-framework/ngx-main-visuals';
import { OutgoingBankWireRevisionComponent } from '../../outgoing-bank-wire-revision/outgoing-bank-wire-revision.component';
import { MatLegacyDialog as MatDialog } from '@angular/material/legacy-dialog';
import { FilterService } from '../../../services/filter.service';
import { formatNumber } from '@angular/common';
import { lowerFirst } from 'lodash';
import { OutgoingBankWireTypeComponent } from '../outgoing-bank-wire-type/outgoing-bank-wire-type.component';
import { ExportToPdfService } from 'src/app/services/exportToPdf.service';

@Component({
  selector: 'app-outgoing-bank-wire-model-index',
  templateUrl: './outgoing-bank-wire-index.component.html',
  styleUrls: ['./outgoing-bank-wire-index.component.scss'],
})
export class OutgoingBankWireIndexComponent {
  public filterArray: Filter[] = [];
  public config: GridDef = {
    title: 'OutgoingBankWireModel Index',
    fields: [
      { key: 'addedAt' },
      { key: 'sygsRef', isHidden: true },
      { key: 'benefitBankBICName', isSortable: false },
      { key: 'benefitBranchBICName', isSortable: false },
      { key: 'payBranchBICName', isSortable: false },
      { key: 'insCurrency' },
      {
        key: 'insAmount',
        displayCellValueFunc: (val) => {
          if (val) {
            return formatNumber(val, 'en', '3.0-0');
          } else return '-';
        },
      },
      { key: 'exchangeRate' },
      { key: 'settlementCurrency' },
      {
        key: 'settlementAmount',
        displayCellValueFunc: (val) => {
          if (val) {
            return formatNumber(val, 'en', '3.0-0');
          } else return '-';
        },
      },
      { key: 'sendReference', isHidden: true },
      {
        key: 'lastState',
        displayCellValueFunc: (val) => {
          if (val) {
            return this.languageService.getLang(val);
          } else {
            return '-';
          }
        },
        isSortable: false,
      },
      { key: 'dtAccount' },
      { key: 'ctAccount' },
    ],
    actionFields: [
      {
        header: 'Actions',
        actions: [
          {
            icon: 'assets/icons/grid/ic_view.svg',
            cssClass: 'btn-view',
            tooltip: 'View',
            delegateFunction: (obj) => {
              this.router.navigate([]).then((result) => {
                window.open(
                  'OutgoingBankWire/outgoing-bank-wire-model/view/' + obj['id'],
                  '_blank'
                );
              });
            },
          },
          {
            icon: 'assets/icons/grid/ic_view.svg',
            cssClass: 'btn-print',
            tooltip: 'Print',
            delegateFunction: (obj) => {
              this.exportToPdfService.exportOutgoingBankWireToPdf(
                obj,
                'outgoing-bank-wire-details'
              );
            },
          },
          {
            icon: 'assets/icons/grid/ic_edit.svg',
            cssClass: 'btn-edit',
            tooltip: 'Edit',
            visible: (obj: any) => {
              if (
                (obj.lastState == 'Initial' ||
                  obj.lastState == 'NeedCorrect') &&
                (localStorage
                  .getItem('roles')
                  ?.includes('OutgoingBankWireManagement') ||
                  localStorage.getItem('roles')?.includes('SuperAdmin'))
              ) {
                return true;
              } else {
                return false;
              }
            },
            delegateFunction: (obj) => {
              if (obj.orderType == 'MT103') {
                this.router.navigate(
                  ['customerOutgoingBankWireUpdate/' + obj['id']],
                  { relativeTo: this.activatedRoute.parent }
                );
              } else {
                this.router.navigate(['OutgoingBankWireUpdate/' + obj['id']], {
                  relativeTo: this.activatedRoute.parent,
                });
              }
            },
          },
          {
            icon: 'assets/icons/revision.svg',
            cssClass: 'btn-edit',
            tooltip: 'Revision',
            visible: (obj: any) => {
              if (
                (obj.lastState == 'Initial' ||
                  obj.lastState == 'NeedCorrect') &&
                (localStorage
                  .getItem('roles')
                  ?.includes('OutgoingBankWireAuditing') ||
                  localStorage.getItem('roles')?.includes('SuperAdmin'))
              ) {
                return true;
              } else {
                return false;
              }
            },
            delegateFunction: (obj) => {
              this.dialog
                .open(OutgoingBankWireRevisionComponent, {
                  width: '50%',
                  panelClass: 'ttwr-dialog',
                  data: {
                    id: obj.id,
                  },
                })
                .afterClosed()
                .subscribe(() => {
                  this.config.refreshSubject?.next(true);
                });
            },
          },
          {
            icon: 'assets/icons/send.svg',
            cssClass: 'btn-edit',
            tooltip: 'Send',
            visible: (obj: any) => {
              if (
                (obj.lastState == 'Initial' ||
                  obj.lastState == 'NeedCorrect') &&
                (localStorage
                  .getItem('roles')
                  ?.includes('OutgoingBankWireAuditing') ||
                  localStorage.getItem('roles')?.includes('SuperAdmin'))
              ) {
                return true;
              } else {
                return false;
              }
            },
            confirmationRequired: true,
            delegateFunction: (obj) =>
              this.outgoingBankWireModelService
                .audit(obj.id)
                .subscribe((res: any) => {
                  if (res.code == 0) {
                    this.alertService.success('Success Audit');
                    this.config.refreshSubject?.next(true);
                  } else {
                    this.alertService.error(res.message);
                  }
                }),
          },
          // {
          //   icon: 'assets/icons/grid/ic_delete.svg',
          //   cssClass: 'btn-delete',
          //   tooltip: 'Delete',
          //   confirmationRequired: true,
          //   delegateFunction: (obj) => this.outgoingBankWireModelService.delete(obj['Id']).subscribe(() => {
          //     this.alertService.success('Success Delete');
          //     if (this.config.refreshSubject)
          //       this.config.refreshSubject.next(true);
          //   }),
          // },
        ],
      },
    ],
    disableFiltrableColumns: true,
    export: {
      excel: {
        enable: false,
      },
      pdf: {
        enable: false,
      },
    },
    disableToggleAll: true,
    fieldsDef: OutgoingBankWireModel.getModelDef(),
    actions: [
      {
        icon: 'assets/icons/grid/ic_create.svg',
        cssClass: 'btn-create',
        tooltip: 'Create',
        visible: (obj: any) => {
          if (
            localStorage
              .getItem('roles')
              ?.includes('OutgoingBankWireManagement') ||
            localStorage.getItem('roles')?.includes('SuperAdmin')
          ) {
            return true;
          } else {
            return false;
          }
        },
        delegateFunction: () => {
          this.dialog.open(OutgoingBankWireTypeComponent, {
            width: '50%',
            panelClass: 'ttwr-dialog',
          });
        },
      },
      {
        label: 'Export To EXCEL',
        cssClass: 'btn-create btn-export',
        visible: (obj: any) => {
          if (
            localStorage
              .getItem('roles')
              ?.includes('OutgoingBankWireManagement') ||
            localStorage.getItem('roles')?.includes('SuperAdmin') ||
            localStorage.getItem('roles')?.includes('OutgoingBankWireAuditing')
          ) {
            return true;
          } else {
            return false;
          }
        },
        delegateFunction: () => {
          let fromFilter = this.filterArray?.find(
            (a) => a.attribute == 'FromDate'
          );
          let toFilter = this.filterArray?.find((a) => a.attribute == 'ToDate');

          if (
            this.filterArray.length == 0 ||
            fromFilter == null ||
            toFilter == null ||
            (new Date(toFilter.value).getTime() -
              new Date(fromFilter.value).getTime()) /
              (1000 * 60 * 60 * 24) >
              31
          ) {
            this.alertService.error(
              'The start and end date must be specified with a maximum range of 31 days'
            );
          } else {
            this.outgoingBankWireModelService
              .exportToExcel(this.filterArray)
              .subscribe(
                (res: any) => {
                  this.downloadFile(res);
                },
                (error) => {
                  this.alertService.error(error.message);
                }
              );
          }
        },
      },
    ],
    dataFunction: (filter, sort, pageIndex, pageSize): Observable<Model[]> => {
      return this.outgoingBankWireModelService.index(
        this.filterArray,
        sort,
        pageIndex,
        pageSize
      );
    },
  };

  constructor(
    private outgoingBankWireModelService: OutgoingBankWireService,
    private alertService: AlertService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private dialog: MatDialog,
    private languageService: LanguageService,
    private filterService: FilterService,
    private exportToPdfService: ExportToPdfService
  ) {}

  submitFilter(event: any) {
    if (event == true) {
      this.filterArray = this.filterService.createFilter();
      this.config.refreshSubject?.next(true);
    }
  }

  downloadFile(data: any) {
    const blob = new Blob([data], { type: 'application/vnd.ms-excel' });
    const url = window.URL.createObjectURL(blob);
    window.open(url);
  }
}
