import {Model, TtwrInputComponent} from "@ttwr-framework/ngx-main-visuals";
import {Validators} from "@angular/forms";

export class LoginFormModel extends Model {
  username!: string;
  password!: string;

  protected static initializeModelDef() {
    return {
      defs: {
        username: {
          ID: 'username',
          dataType: 'text',
          placeholder: 'Username',
          label: 'Username',
          ui: TtwrInputComponent,
          validators: [
            {
              name: 'required',
              validator: Validators.required,
              message: 'Username is required',
            },
          ],
        },
        password: {
          ID: 'password',
          dataType: 'password',
          placeholder: '************',
          label: 'Password',
          ui: TtwrInputComponent,
          validators: [
            {
              name: 'required',
              validator: Validators.required,
              message: 'Password is required',
            },
            {
              name: 'passwordMustMatch',
              message: 'Passwords must match',
            },
            {
              name: 'authentication_failed',
              message: 'Authentication Failed',
            },
          ],
        },
      },
    };
  }
}
