import {Component, OnInit} from '@angular/core';
import {BankWireType} from '../../Models/BankWireType.model';
import {ActivatedRoute, Router} from '@angular/router';
import {BankWireTypeService} from '../../Services/bank-wire-type.service';
import {AlertService, Helper,ViewDef} from '@ttwr-framework/ngx-main-visuals';


@Component({
  selector: 'app-bank-wire-type-view',
  templateUrl: './bank-wire-type-view.component.html',
  styleUrls: ['./bank-wire-type-view.component.scss']
})
export class BankWireTypeViewComponent implements OnInit {

  public config: ViewDef = {
    title: 'BankWireType',
    fields: [
      {key: 'type'},
      {key: 'enName'},
      {key: 'arName'},
    ],
    fieldsDef: BankWireType.getModelDef(),
    actionFields: [
      {
        label: 'Update',
        cssClass: 'btn-info',
        delegateFunction: (obj) => this.router.navigate(['update/' + obj['Id']], {relativeTo: this.activatedRoute.parent})

      },
      {
        label: 'Delete',
        cssClass: 'btn-warning',
        confirmationRequired: true,
        delegateFunction: (obj) => this.bankWireTypeService.delete(this.config.obj['Id']).subscribe(() => {
          this.alertService.success('Success Delete');
          this.router.navigate(['index/'], {relativeTo: this.activatedRoute.parent});
        }),
      },
      {
        label: 'Done',
        cssClass: 'btn-danger',
        delegateFunction: (obj) => this.router.navigate(['index/'], {relativeTo: this.activatedRoute.parent})
      },
    ]
  };

  constructor(private route: ActivatedRoute, private router: Router,
              private bankWireTypeService: BankWireTypeService, private alertService: AlertService, private activatedRoute: ActivatedRoute) {
  }

  ngOnInit() {
    if(this.route.snapshot.data.obj)
       this.config.obj =Helper.deepCopy(this.route.snapshot.data.obj)
  }

}
