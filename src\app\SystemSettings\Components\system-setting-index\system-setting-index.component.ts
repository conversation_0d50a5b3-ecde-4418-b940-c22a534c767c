import {Component} from '@angular/core';
import {BehaviorSubject, Observable} from "rxjs";
import {SystemSettingService} from "../../Services/SystemSettingService";
import {switchMap} from "rxjs/operators";
import {SystemSetting} from "../../Types/SystemSetting.types";
import {MatLegacyDialog as MatDialog} from "@angular/material/legacy-dialog";
import {SystemSettingUpdateComponent} from "../system-setting-update/system-setting-update.component";

@Component({
  selector: 'app-system-setting-index',
  templateUrl: './system-setting-index.component.html',
  styleUrls: ['./system-setting-index.component.scss']
})
export class SystemSettingIndexComponent {

  private emitFetchSubject = new BehaviorSubject(true);

  public systemSettings: Observable<any>;

  constructor(private systemSettingService: SystemSettingService, private dialog: MatDialog) {
    this.systemSettings = this.emitFetchSubject.pipe(
      switchMap(() => this.systemSettingService.getAll())
    );
  }

  public edit(config: SystemSetting) {
    const ref = this.dialog.open(SystemSettingUpdateComponent, {
      width: '50%',
      panelClass: 'ttwr-dialog',
      data: config,
    });
    ref.afterClosed()
      .subscribe((res) => {
        this.refreshConfigurations()
      });
  }

  refreshConfigurations() {
    this.emitFetchSubject.next(true);
  }
}
