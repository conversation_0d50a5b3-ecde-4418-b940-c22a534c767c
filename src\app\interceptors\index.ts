import {HTTP_INTERCEPTORS} from '@angular/common/http';

import {RefreshTokenInterceptor} from './token.interceptor';
import {UserDataInterceptor} from './user-data.interceptor';
import {LoaderInterceptor} from "./loader.interceptor";


export const httpInterceptorProviders = [
  {provide: HTTP_INTERCEPTORS, useClass: RefreshTokenInterceptor, multi: true},
  {provide: HTTP_INTERCEPTORS, useClass: UserDataInterceptor, multi: true},
  {provide: HTTP_INTERCEPTORS, useClass: LoaderInterceptor, multi: true},
];
