import {Component, Inject, OnInit} from '@angular/core';
import {AlertService, FormDef, Helper} from '@ttwr-framework/ngx-main-visuals';
import {MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA, MatLegacyDialogRef as MatDialogRef} from '@angular/material/legacy-dialog';
import {User} from '../../models/user.model';
import {UserService} from '../../services/user.service';

@Component({
  selector: 'app-add-user-groups',
  templateUrl: './add-user-groups.component.html',
  styleUrls: ['./add-user-groups.component.scss'],
})
export class AddUserGroupsComponent implements OnInit {
  dialogTitle='Add to groups';
  public config: FormDef = {
    titleHidden:true,
    fields: [{key: 'UserGroupS'}],
    fieldsDef: User.getModelDef(),
    obj: {},
    actionFields: [
      {
        label: 'Submit',
        delegateFunction: (obj) =>
          this.userService.addGroups(this.config.obj?.['Id'], obj).subscribe(() => {
            this.alertService.success('Success Update');
            this.dialogRef.close(true);
          }),
      },
      {
        label: 'Cancel',
        cssClass: 'btn-danger',
        causeValidate: false,
        delegateFunction: () => this.dialogRef.close(),
      },
    ],
  };

  constructor(
    public dialogRef: MatDialogRef<AddUserGroupsComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private userService: UserService,
    private alertService: AlertService,
  ) {
  }

  ngOnInit() {
    this.config.obj = Helper.deepCopy(this.data['obj']);
  }
}
