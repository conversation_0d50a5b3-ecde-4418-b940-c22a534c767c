import {Component} from '@angular/core';
import {Observable, throwError} from 'rxjs';
import {AlertService, GridDef, Model} from '@ttwr-framework/ngx-main-visuals';
import {AuhtItemService} from '../../services/auht-item.service';
import {MatLegacyDialog as MatDialog} from '@angular/material/legacy-dialog';
import {AuthItem} from '../../models/auth-item.model';
import {ActivatedRoute, Router} from '@angular/router';
import {RoleCreateComponent} from '../role-create/role-create.component';
import {RoleUpdateNameComponent} from '../role-update-name/role-update-name.component';
import {catchError} from "rxjs/operators";

@Component({
  selector: 'app-role-index',
  templateUrl: './role-index.component.html',
  styleUrls: ['./role-index.component.scss'],
})
export class RoleIndexComponent {
  public config: GridDef = {
    title: 'Roles',
    fields: [{key: 'Name'}],
    actionFields: [
      {
        header: 'Actions',
        actions: [
          {
            icon: 'assets/icons/grid/ic_view.svg',
            tooltip: 'View',
            cssClass: 'btn-view',
            delegateFunction: (obj) =>
              this.router.navigate(['view/' + obj['Id']], {relativeTo: this.activatedRoute.parent}),
          },
          {
            label: 'Permission Tree',
            tooltip: 'role_view_perm_tree',
            cssClass: 'btn-action-col',
            delegateFunction: (obj) =>
              this.router.navigate(['view-tree/' + obj['Id']], {relativeTo: this.activatedRoute.parent}),
          },
          {
            icon: 'assets/icons/grid/ic_edit.svg',
            tooltip: 'role_update_perm_tree',
            cssClass: 'btn-edit',
            delegateFunction: (obj) =>
              this.router.navigate(['update/' + obj['Id']], {relativeTo: this.activatedRoute.parent}),
          },
          {
            label: 'Update name',
            tooltip: 'Update name',
            cssClass: 'btn-action-col',
            delegateFunction: (obj) => {
              const dialogRef = this.dialog.open(RoleUpdateNameComponent, {
                width: '50%',
                panelClass: 'ttwr-dialog',
                data: {obj: obj},
              });
              dialogRef.afterClosed().subscribe((res: any) => {
                if (res && this.config.refreshSubject) {
                  this.config.refreshSubject.next(true);
                }
              });
            },
          },
          {
            icon: 'assets/icons/grid/ic_delete.svg',
            tooltip: 'Delete',
            cssClass: 'btn-delete',
            confirmationRequired: true,
            delegateFunction: (obj) =>
              this.auhtItemService.delete(obj['Id']).subscribe(() => {
                this.alertService.success('Success Delete');
                if (this.config.refreshSubject) this.config.refreshSubject.next(true);
              }),
          },
        ],
      },
    ],
    fieldsDef: AuthItem.getModelDef(),
    actions: [
      {
        icon: 'assets/icons/grid/ic_create.svg',
        tooltip: 'Create',
        cssClass: 'btn-create',
        delegateFunction: () => {
          const dialogRef = this.dialog.open(RoleCreateComponent, {
            width: '50%',
            panelClass: 'ttwr-dialog',
          });
          dialogRef.afterClosed().subscribe((res: any) => {
            if (res && this.config.refreshSubject) {
              this.config.refreshSubject.next(true);
            }
          });
        },
      },
    ],
    dataFunction: (filter, sort, pageIndex, pageSize): Observable<Model[]> => {
      return this.auhtItemService.index(filter, sort, pageIndex, pageSize).pipe(catchError(err => {
        this.alertService.error(err.message);
        return throwError(err.message);
      }));
    },
  };

  constructor(
    private dialog: MatDialog,
    private router: Router,
    private auhtItemService: AuhtItemService,
    private activatedRoute: ActivatedRoute,
    private alertService: AlertService,
  ) {
  }
}
