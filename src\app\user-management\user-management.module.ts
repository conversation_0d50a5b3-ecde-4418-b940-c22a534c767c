import {NgModule} from '@angular/core';
import {UserIndexComponent} from './user/user-index/user-index.component';
import {UserCreateComponent} from './user/user-create/user-create.component';
import {UserUpdateComponent} from './user/user-update/user-update.component';
import {UserViewComponent} from './user/user-view/user-view.component';
import {UserComponent} from './user/user.component';
import {UserService} from './services/user.service';
import {UserManagementRouting} from './user-management.routing';
import {SharedModule} from '../shared/shared.module';
import {RoleIndexComponent} from './role/role-index/role-index.component';
import {RoleComponent} from './role/role.component';
import {PermissionComponent} from './permission/permission.component';
import {PermissionTreeComponent} from './permission/permission-index/permission-tree.component';
import {MainComponent} from './main/main.component';
import {UserGroupIndexComponent} from './user-group/user-group-index/user-group-index.component';
import {UserGroupCreateComponent} from './user-group/user-group-create/user-group-create.component';
import {UserGroupUpdateComponent} from './user-group/user-group-update/user-group-update.component';
import {UserGroupViewComponent} from './user-group/user-group-view/user-group-view.component';
import {UserGroupComponent} from './user-group/user-group.component';
import {UserGroupService} from './services/user-group.service';
import {AuhtItemService} from './services/auht-item.service';
import {RoleLovGridComponent, UserGroupLovGrid} from './models/auth-item.model';
import {AddRoleGroupsComponent} from './role/add-role-groups/add-role-groups.component';
import {UserLovGridComponent} from './models/user.model';
import {AddGroupUsersComponent} from './user-group/add-group-users/add-group-users.component';
import {AddUserGroupsComponent} from './user/add-user-groups/add-user-groups.component';
import {RoleViewComponent} from './role/role-view/role-view.component';
import {AddGroupRolesComponent} from './user-group/add-group-roles/add-group-roles.component';
import {RoleCreateComponent} from './role/role-create/role-create.component';
import {RoleUpdateComponent} from './role/role-update/role-update.component';
import {RoleViewTreeComponent} from './role/role-view-tree/role-view-tree.component';
import {AddRolesComponent} from './role/add-roles/add-roles.component';
import {RoleUpdateNameComponent} from './role/role-update-name/role-update-name.component';
import {UserManagementResolver} from "./resolvers/user-management.resolver";
import {TtwrBaseSelectComponent} from "./ttwr-base-select.component";
import {TtwrBaseInputComponent} from "./ttwr-base-input.component";
import {SelectRoleComponent} from './user/select-role/select-role.component';
import {FormsModule, ReactiveFormsModule} from "@angular/forms";


@NgModule({
    declarations: [
        MainComponent,
        UserIndexComponent,
        UserCreateComponent,
        UserUpdateComponent,
        UserViewComponent,
        UserComponent,
        RoleIndexComponent,
        RoleComponent,
        PermissionTreeComponent,
        PermissionComponent,
        UserGroupIndexComponent,
        UserGroupCreateComponent,
        UserGroupUpdateComponent,
        UserGroupViewComponent,
        UserGroupComponent,
        RoleLovGridComponent,
        AddRoleGroupsComponent,
        UserGroupLovGrid,
        UserLovGridComponent,
        AddGroupUsersComponent,
        AddUserGroupsComponent,
        RoleViewComponent,
        AddGroupRolesComponent,
        RoleCreateComponent,
        RoleUpdateComponent,
        RoleViewTreeComponent,
        AddRolesComponent,
        RoleUpdateNameComponent,
        TtwrBaseSelectComponent,
        TtwrBaseInputComponent,
        SelectRoleComponent,
    ],
    imports: [SharedModule, UserManagementRouting, FormsModule, ReactiveFormsModule],
    providers: [UserService, UserGroupService, AuhtItemService, UserManagementResolver]
})
export class UserManagementModule {
}
