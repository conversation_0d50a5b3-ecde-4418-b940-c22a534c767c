import {Injectable} from "@angular/core";
import {HttpClient, HttpParams} from "@angular/common/http";
import {Filter, Model, Sort} from "@ttwr-framework/ngx-main-visuals";
import {Observable} from "rxjs";

@Injectable()
export class SYGSBaseService {

  protected baseName!: string;

  constructor(protected http: HttpClient) {
  }

  public getGridParams(filter: Filter[] = [], sort: Sort[] = [], pageNumber = 0, pageSize = 5) {
    let params = new HttpParams()
      .set('page', (pageNumber + 1).toString())
      .set('pageSize', pageSize.toString());

    filter.forEach((x, i) => {
      params = params.set('filters[' + i + '].attribute', x.attribute);
      params = params.set('filters[' + i + '].operation', x.operation);
      params = params.set('filters[' + i + '].value', x.value);
      x.useOrOperator ? params = params.set('filters[' + i + '].useOrOperator', x.useOrOperator) : null;
    })

    sort.forEach((x, i) => {
      params = params.set('sort[' + i + '].attribute', x.attribute)
      params = params.set('sort[' + i + '].direction', x.direction)
    })

    return params;
  }

  create(obj: any): Observable<Model> {
    return this.http.post<Model>(`/api/` + this.baseName + `/create`, obj);
  }

  update(id: number, obj: any): Observable<Model> {
    return this.http.post<Model>(`/api/` + this.baseName + `/update/` + id, obj);
  }

  updateWithStringID(id: string, obj: any): Observable<Model> {
    return this.http.post<Model>(`/api/` + this.baseName + `/update/` + id, obj);
  }

  delete(id: number): Observable<Model> {
    return this.http.post<Model>(`/api/` + this.baseName + `/delete/` + id, null);
  }

  view(id: number): Observable<Model> {
    return this.http.get<Model>(`/api/` + this.baseName + `/view/` + id);
  }

  viewAsString(id: string): Observable<Model> {
    return this.http.get<Model>(`/api/` + this.baseName + `/view/` + id);
  }

  index(
    filter: Filter[] = [],
    sort: Sort[] = [],
    pageNumber = 0,
    pageSize = 5
  ): Observable<Model[]> {
    const params = this.getGridParams(filter, sort, pageNumber, pageSize);
    return this.http.get<Model[]>(`/api/` + this.baseName + `/index`, {
      params: params,
    });
  }
}
