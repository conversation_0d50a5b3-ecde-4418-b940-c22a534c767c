import {Model, TtwrInputComponent, Types} from "@ttwr-framework/ngx-main-visuals";

export class OperationModel extends Model {
  operationId!: string;
  operationName!: string;
  executeDate!: Date;
  resultCode!: string;
  resultMessage!: string;
  resultXapiReferenceNumber!: string;
  resultAuthorizeCode!: string;
  isSuccess!: boolean;
  reverseReason!: string;
  employeeName!: string;


  protected static initializeModelDef() {
    return {
      defs: {
        operationId: {
          ID: 'operationId',
          dataTypes: Types.STRING,
          label: 'operationId',
          isSortable: true,
          ui: TtwrInputComponent
        },
        operationName: {
          ID: 'operationName',
          dataTypes: Types.STRING,
          label: 'operationName',
          isSortable: true,
          ui: TtwrInputComponent
        },
        reverseReason: {
          ID: 'reverseReason',
          dataTypes: Types.STRING,
          label: 'reverseReason',
          isSortable: true,
          ui: TtwrInputComponent
        },
        executeDate: {
          ID: 'executeDate',
          dataTypes: Types.DATETIME,
          label: 'executeDate',
          isSortable: true,
          ui: TtwrInputComponent
        },
        resultCode: {
          ID: 'resultCode',
          dataTypes: Types.STRING,
          label: 'resultCode',
          isSortable: true,
          ui: TtwrInputComponent
        },
        resultMessage: {
          ID: 'resultMessage',
          dataTypes: Types.STRING,
          label: 'resultMessage',
          isSortable: true,
          ui: TtwrInputComponent
        },
        resultXapiReferenceNumber: {
          ID: 'resultXapiReferenceNumber',
          dataTypes: Types.STRING,
          label: 'resultXapiReferenceNumber',
          isSortable: true,
          ui: TtwrInputComponent
        },
        resultAuthorizeCode: {
          ID: 'resultAuthorizeCode',
          dataTypes: Types.STRING,
          label: 'resultAuthorizeCode',
          isSortable: true,
          ui: TtwrInputComponent
        },
        isSuccess: {
          ID: 'isSuccess',
          dataTypes: Types.NUMBER,
          label: 'isSuccess',
          isSortable: true,
          ui: TtwrInputComponent
        },
        employeeName: {
          ID: 'employeeName',
          dataTypes: Types.STRING,
          label: 'employeeName',
          isSortable: true,
          ui: TtwrInputComponent
        },
      }
    }
  }
}
