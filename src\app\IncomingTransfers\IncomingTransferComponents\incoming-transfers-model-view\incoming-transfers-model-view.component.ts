import {Component, ElementRef, Inject, LOCALE_ID, OnInit, ViewChild} from '@angular/core';
import {IncomingTransfersModel} from '../../Models/IncomingTransfers.model';
import {ActivatedRoute, Router} from '@angular/router';
import {IncomingTransfersModelService} from '../../Services/incoming-transfers-model.service';
import {AlertService, Helper, LanguageService, MasterDetailDef, Model, ViewDef} from '@ttwr-framework/ngx-main-visuals';
import {OperationModel} from "../../Models/Operation.model";
import {Observable, of} from "rxjs";
import {formatDate, formatNumber} from "@angular/common";
import {ExportToPdfService} from "../../../services/exportToPdf.service";


@Component({
  selector: 'app-incoming-transfers-model-view',
  templateUrl: './incoming-transfers-model-view.component.html',
  styleUrls: ['./incoming-transfers-model-view.component.scss']
})
export class IncomingTransfersModelViewComponent implements OnInit {
  changeEvent!: OperationModel;

  public config: MasterDetailDef = {
    masterViewConfig: {
      title: 'IncomingTransfersModel',
      fields: [
        {
          key: 'ctAccount', cssClass: {field: 'col-md-6 col-12', value: 'label-color ct-account-value'}
        },
        {key: 'ctName', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'ctAdd', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'rmtInformation', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'transferId', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {
          key: 'addedAt', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}, displayCellValueFunc: (val) => {
            if (val) {
              return formatDate(new Date(val), 'hh:mm:ss yyyy-MM-dd', this.locale);
            } else return '-'
          }
        },
        // {
        //   key: 'additionType', displayCellValueFunc: (val) => {
        //     if (val) {
        //       return this.languageService.getLang(val)
        //     } else return '-'
        //   }, cssClass: {field: 'col-md-6 col-12', value: 'label-color'}
        // },
        {key: 'transferringParty', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'sygsRef', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {
          key: 'orderType', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}, displayCellValueFunc: (val) => {
            if (val) {
              return this.languageService.getLang(val)
            } else {
              return '-'
            }
          }
        },
        {key: 'ttc', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'payBankBIC', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'payBranchBIC', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'benefitBankBIC', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'benefitBranchBIC', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'payPartBIC', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'benefitPartBIC', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'valueDate', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'optCode', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {
          key: 'insCurrency', displayCellValueFunc: (val) => {
            if (val) {
              return this.languageService.getLang(val)
            } else return '-'
          }, cssClass: {field: 'col-md-6 col-12', value: 'label-color'}
        },
        {
          key: 'insAmount', cssClass: {field: 'col-md-6 col-12', value: 'label-color'},
          displayCellValueFunc: (val) => {
            if (val) {
              return formatNumber(val, this.locale, '3.0-2');
            } else return '-'
          }
        },
        {key: 'insAmountAsWords', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'exchangeRate', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'dtlChg', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'sendChangeAmount', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'receiveChangeAmount', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {
          key: 'settlementCurrency', displayCellValueFunc: (val) => {
            if (val) {
              return this.languageService.getLang(val)
            } else return '-'
          }, cssClass: {field: 'col-md-6 col-12', value: 'label-color'}
        },
        {
          key: 'settlementAmount', cssClass: {field: 'col-md-6 col-12', value: 'label-color'},
          displayCellValueFunc: (val) => {
            if (val) {
              return formatNumber(val, this.locale, '3.0-2');
            } else return '-'
          }
        },
        {key: 'settlementAmountAsWords', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'sendReference', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {
          key: 'priority',
          cssClass: {field: 'col-md-6 col-12', value: 'label-color'},
          displayCellValueFunc: (val: any) => {
            if (val) {
              return this.languageService.getLang(val)
            } else {
              return '-'
            }
          }
        },
        {key: 'sendToReceiveInformation', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'dtAccount', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'dtName', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'dtAdd', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'state', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'relatedReference', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'checkNumber', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'endrCount', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'endrNames', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'rebMessageId', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {
          key: 'transferManipulated', displayCellValueFunc: (val) => {
            if (val) {
              return this.languageService.getLang('Yes')
            } else if (val == false) {
              return this.languageService.getLang('No')
            } else return '-'
          }, cssClass: {field: 'col-md-6 col-12', value: 'label-color'}
        },
      ],
      fieldsDef: IncomingTransfersModel.getModelDef(),
      actionFields: [
        {
          label: 'Done',
          cssClass: 'Done-style',
          delegateFunction: (obj) => this.router.navigate(['index/'], {relativeTo: this.activatedRoute.parent})
        },
        {
          label: 'Export to PDF',
          cssClass: 'Done-style',
          delegateFunction: (obj) => {
            var title: string = 'تفاصيل حوالة واردة';
            this.exportToPdfService.exportPdf(obj, title, obj.sygsRef, true);
          }
        },
      ]
    },
    detailGridsConfig: [
      {
        title: 'Operations',
        fields: [
          {key: 'operationId'},
          {
            key: 'operationName', displayCellValueFunc: (val) => {
              if (val) {
                return this.languageService.getLang(val)
              } else return '-'
            }
          },
          {
            key: 'executeDate', displayCellValueFunc: (val) => {
              if (val) {
                return formatDate(new Date(val), 'hh:mm:ss yyyy-MM-dd', this.locale);
              } else return '-'
            }
          },
          {key: 'resultCode'},
          {key: 'resultMessage'},
          {key: 'resultXapiReferenceNumber'},
          {key: 'resultAuthorizeCode'},
          {key: 'reverseReason'},
          {
            key: 'isSuccess', displayCellValueFunc: (val) => {
              if (val) {
                return this.languageService.getLang('Yes')
              } else if (val == false) {
                return this.languageService.getLang('No')
              } else return '-'
            }
          },
          {key: 'employeeName'},
        ],
        disableToggleAll: true,
        export: {
          excel: {
            enable: false,
          },
          pdf: {
            enable: false
          }
        },
        disableFiltrableColumns: true,

        fieldsDef: OperationModel.getModelDef(),
        dataFunction: (filter: any, sort: any, pageIndex: any, pageSize: any): Observable<Model[]> => {
          let events: any = {
            TotalCount: this.changeEvent.length,
            PageSize: this.changeEvent.length,
            Items: this.changeEvent,
            CurrentPage: 1
          }
          return of(events)
        },
        actionFields: [],
        actions: [],
      }
    ]

  };

  constructor(private route: ActivatedRoute, private router: Router,
              private incomingTransfersModelService: IncomingTransfersModelService,
              private alertService: AlertService, private activatedRoute: ActivatedRoute,
              private languageService: LanguageService,
              private exportToPdfService: ExportToPdfService,
              @Inject(LOCALE_ID) public locale: string) {
  }

  ngOnInit() {
    if (this.activatedRoute.snapshot.data.obj) {
      this.config.masterViewConfig.obj = Helper.deepCopy(this.activatedRoute.snapshot.data.obj)
      this.changeEvent = this.config.masterViewConfig.obj.operations
    }

  }
}
