import {Component} from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import {AlertService, FormDef, LanguagePipe} from "@ttwr-framework/ngx-main-visuals";
import {LoginFormModel} from "../models/login-form.model";
import {AuthenticationService} from "../services/authentication.service";
import {ConfigService} from "../../services/config.service";

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
})
export class LoginComponent {
  currentYear: number;
  title: string;
  description = 'Base template description';
  logo = {
    url: 'assets/icons/logo/Logo_REB.png',
  };
  footerLogo = {
    url: 'assets/icons/logo/newTatweerLogo.png',
  };
  private isLogin: boolean;
  private returnUrl!: string;
  public config: FormDef = {
    fields: [
      {key: 'username', inputIcon: 'assets/icons/ic_user.svg', sideLabel: false, cssClass: {field: 'w-100'}},
      {key: 'password', inputIcon: 'assets/icons/ic_password.svg', sideLabel: false, cssClass: {field: 'w-100'}},
    ],
    fieldsDef: LoginFormModel.getModelDef(),
    obj: {},
    actionFields: [
      {
        label: 'Login',
        delegateFunction: async (obj) => {
          await this.authenticationService.login(obj.username, obj.password).catch(err =>{
            this.alertService.error(err.error)
          });
          this.returnUrl = this.route.snapshot.queryParams.returnUrl || '/';
          this.router.navigate([this.returnUrl]);
        },
      },
    ],
  };

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private authenticationService: AuthenticationService,
    private alertService: AlertService,
    private languagePipe: LanguagePipe,
    private configService: ConfigService
  ) {
    this.title = configService.configuration.appName
    this.currentYear = new Date().getFullYear();
    this.isLogin = localStorage.getItem('token') !== null;
    if (this.isLogin) {
      this.router.navigate(['./']);
    }
  }
}
