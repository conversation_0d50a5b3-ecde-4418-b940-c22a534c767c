import {Injectable} from '@angular/core';
import {SYGSBaseService} from "../../services/SYGSBaseService";
import {Filter, Model, Sort} from "@ttwr-framework/ngx-main-visuals";
import {Observable} from "rxjs";

@Injectable()
export class BankWireTypeService extends SYGSBaseService {

  protected baseName = 'BankWireType';


  getTypes(
    filter: Filter[] = [],
    sort: Sort[] = [],
    pageNumber = 0,
    pageSize = 5,
    type: string
  ): Observable<Model[]> {
    let params = this.getGridParams(filter, sort, pageNumber, pageSize);
    params = params.set('transferOrderType', type)
    return this.http.get<Model[]>(`/api/` + this.baseName + `/GetTypes`, {
      params: params,
    });
  }
}
