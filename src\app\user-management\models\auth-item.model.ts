import {
  AlertService,
  GridDef,
  Model,
  ModelLovGridComponent,
  TtwrInputComponent,
  TtwrInputLovComponent,
  Types,
} from '@ttwr-framework/ngx-main-visuals';
import {Component, OnInit} from '@angular/core';
import {Observable, throwError} from 'rxjs';
import {AuhtItemService} from '../services/auht-item.service';
import {UserGroupService} from '../services/user-group.service';
import {catchError} from "rxjs/operators";

export class AuthItem extends Model {
  Id!: number;
  Name!: string;
  ClearName!: string;
  AddedToTree!: boolean;
  Inherited!: boolean;
  AuthItemChildren!: AuthItem[];
  IncludedInRequest!: boolean;
  Visited!: number[];
  GroupId!: string;

  protected static initializeModelDef() {
    return {
      defs: {
        Id: {
          ID: 'Id',
          dataType: Types.STRING,
        },
        Name: {
          label: 'Name',
          placeholder: 'Name',
          dataType: Types.STRING,
          ui: TtwrInputComponent,
        },
        AuthItemChild: {
          label: 'Name',
          placeholder: 'Name',
          dataType: Types.STRING,
          ui: TtwrInputComponent,
        },
        GroupId: {
          dataType: Types.STRING,
          placeholder: 'User groups',
          label: 'User groups',
          ui: TtwrInputLovComponent,
          config: {
            popUpHeader: 'User groups',
            gridComponent: UserGroupLovGrid,
            searchValueField: 'GroupName',
            multiSelect: true,
          },
        },
        Roles: {
          dataType: Types.STRING,
          placeholder: 'Roles',
          label: 'Roles',
          ui: TtwrInputLovComponent,
          config: {
            popUpHeader: 'Add Roles',
            gridComponent: RoleLovGridComponent,
            multiSelect: true,
          },
        },
        GroupName: {
          ID: 'GroupName',
          dataType: Types.STRING,
          placeholder: 'Group name',
          label: 'Group name',
          ui: TtwrInputComponent,
        },
      },
    };
  }
}

@Component({
  selector: 'app-role-lov-grid',
  template:
    '<ttwr-lov-grid [config]="config" [selectName]="selectName" [selectValue]="selectValue"></ttwr-lov-grid>',
})
export class RoleLovGridComponent extends ModelLovGridComponent implements OnInit {
  public config: GridDef = {
    title: 'Roles',
    fields: [{key: 'Name', isSortable: true}],
    actionFields: [],
    fieldsDef: AuthItem.getModelDef(),
    actions: [],
    dataFunction: (): Observable<Model[]> => {
      return this.auhtItemService.index().pipe(catchError(err => {
        this.alertService.error(err.message);
        return throwError(err.message);
      }));
    },
  };

  constructor(private auhtItemService: AuhtItemService,
              private alertService: AlertService) {
    super();
  }

  ngOnInit() {
    this.selectName = 'Name';
    this.selectValue = 'Id';
  }
}

@Component({
  selector: 'app-user-group-lov-grid',
  template:
    '<ttwr-lov-grid [config]="config" [selectName]="selectName" [selectValue]="selectValue"></ttwr-lov-grid>',
})
export class UserGroupLovGrid extends ModelLovGridComponent implements OnInit {
  public config: GridDef = {
    title: 'Groups',
    fields: [{key: 'Name', isSortable: true}],
    actionFields: [],
    fieldsDef: AuthItem.getModelDef(),
    actions: [],
    dataFunction: (filter, sort, pageIndex, pageSize): Observable<Model> => {
      return this.userGroupService.index(filter, sort, pageIndex, pageSize).pipe(catchError(err => {
        this.alertService.error(err.message);
        return throwError(err.message);
      }));
    },
  };

  constructor(private userGroupService: UserGroupService,
              private alertService: AlertService) {
    super();
  }

  ngOnInit() {
    this.selectName = 'Name';
    this.selectValue = 'Id';
  }
}
