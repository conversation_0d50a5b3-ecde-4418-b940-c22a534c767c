import {Component, Inject, OnInit} from '@angular/core';
import {AlertService, FormDef, Helper} from '@ttwr-framework/ngx-main-visuals';
import {AuthItem} from '../../models/auth-item.model';
import {AuhtItemService} from '../../services/auht-item.service';
import {MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA, MatLegacyDialogRef as MatDialogRef} from '@angular/material/legacy-dialog';

@Component({
  selector: 'app-role-update-name',
  templateUrl: './role-update-name.component.html',
  styleUrls: ['./role-update-name.component.scss'],
})
export class RoleUpdateNameComponent implements OnInit {
  dialogTitle='Role Create';
  public config: FormDef = {
    titleHidden:true,
    fields: [{key: 'Name'}],
    fieldsDef: AuthItem.getModelDef(),
    obj: {},
    actionFields: [
      {
        label: 'Submit',
        delegateFunction: (obj) =>
          this.auhtItemService.updateRole(this.config.obj?.['Id'], obj).subscribe(() => {
            this.alertService.success('Success Update');
            this.dialogRef.close(true);
          }),
      },
      {
        label: 'Cancel',
        cssClass: 'btn-danger',
        causeValidate: false,
        delegateFunction: () => this.dialogRef.close(),
      },
    ],
  };

  constructor(
    private auhtItemService: AuhtItemService,
    public dialogRef: MatDialogRef<RoleUpdateNameComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private alertService: AlertService,
  ) {
  }

  ngOnInit() {
    this.config.obj = Helper.deepCopy(this.data['obj']);
  }
}
