import {Component, HostListener, ViewChild} from '@angular/core';
import {Router} from '@angular/router';
import {PingService} from '../../services/ping.service';
import {ConfigService} from "../../services/config.service";
import {PrintService} from "@ttwr-framework/ngx-main-visuals";
import {AuthenticationService} from "../services/authentication.service";
import {UserService} from "../../user-management/services/user.service";
import {StyleService} from "../../services/style.service";
import {ActiveDirectoryUserService} from "../services/activeDirectoryUserService";

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
})
export class HomeComponent {
  title: string;
  description = 'SYGS';
  languages = [
    {
      name: 'English',
      value: 'en',
    },
    {
      name: 'Arabic',
      value: 'ar',
    },
  ];
  logo = {
    url: 'assets/icons/logo/Logo_REB.png',
  };
  footerLogo = {
    url: 'assets/icons/logo/newTatweerLogo.png',
  };
  currentYear: number;

  @ViewChild('sidenav') public sidenav: any;
  menuItems: { [key: string]: string }[] = [];
  menu: { [key: string]: any }[] = [];
  sidenavPosition = 'side';
  isSidenavOpen = true;
  sideNavIsExpanded = true;
  user!: string | null;
  isSocialAffairsInstitution: boolean = true;

  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    if (event.target.innerWidth < 960) {
      this.isSidenavOpen = false;
      this.sidenavPosition = 'over';
      this.sideNavIsExpanded = true
    } else {
      this.sidenavPosition = 'side';
      this.isSidenavOpen = true;
      this.sideNavIsExpanded = true
    }
  }

  constructor(
    private pingService: PingService,
    public printService: PrintService,
    private authenticationService: AuthenticationService,
    private styleService: StyleService,
    public router: Router,
    public userService: UserService,
    private configService: ConfigService,
    private activeDirectoryUserService: ActiveDirectoryUserService
  ) {
    this.title = this.configService.configuration.appName;
    this.currentYear = new Date().getFullYear();
    setTimeout(() => {
      this.menuItems = this.menu;
    });
    if (localStorage.getItem('username') && (localStorage.getItem('roles'))) {
      this.user = localStorage.getItem('username');
      this.AddMenuItem();
    } else {
      this.userService.GetUserProfile().subscribe((res: any) => {
        localStorage.setItem('username', res.userName)
        let allRoles: string = '';
        res.roles.forEach((item: any) => {
          allRoles += item + ', '
        })
        localStorage.setItem('roles', allRoles)
        this.user = res.userName
        this.AddMenuItem();
      })

    }

  }

  ngOnInit(): void {
  }

  AddMenuItem() {
    this.menu = [
      {
        routerLink: '/system-setting/index',
        label: 'SystemSettings',
        code: 'u-m',
        visible: localStorage.getItem('roles')?.includes('SuperAdmin')
      },
      {
        routerLink: '/users-managements/users/index',
        label: 'User Management',
        // icon: 'assets/icons/dashboard/ic_users.svg',
        code: 'u-m',
        visible: localStorage.getItem('roles')?.includes('SuperAdmin')
      },
      {
        routerLink: '',
        label: 'Incoming transfers',
        items: [
          {
            routerLink: '/incoming-transfers/new-incoming-transfer/index',
            label: 'New',
            visible: localStorage.getItem('roles')?.includes('IncomingBankWireManagement') ||
              localStorage.getItem('roles')?.includes('SuperAdmin')
            // visible: true,
          },
          {
            routerLink: '/incoming-transfers/incoming-transfers-model/index',
            label: 'Report',
            visible: localStorage.getItem('roles')?.includes('IncomingBankWireReporting') ||
              localStorage.getItem('roles')?.includes('SuperAdmin')
          }
        ],
        visible: localStorage.getItem('roles')?.includes('IncomingBankWireReporting') ||
          localStorage.getItem('roles')?.includes('SuperAdmin') ||
          localStorage.getItem('roles')?.includes('IncomingBankWireManagement')
      },
      {
        routerLink: '/OutgoingBankWire/outgoing-bank-wire-model/index',
        label: 'Outgoing transfers',
        visible: localStorage.getItem('roles')?.includes('OutgoingBankWireReporting') ||
          localStorage.getItem('roles')?.includes('OutgoingBankWireManagement') ||
          localStorage.getItem('roles')?.includes('OutgoingBankWireAuditing') ||
          localStorage.getItem('roles')?.includes('SuperAdmin')
        // items: [
        //   {
        //     routerLink: '/OutgoingBankWire/outgoing-bank-wire-model/index',
        //     label: 'New',
        //     visible: true
        //   },
        //   {
        //     routerLink: '/outgoing-transfers/outgoing-transfers-model/index',
        //     label: 'Report',
        //     visible: true
        //   }
        // ],
      },
    ]
    this.menuItems = this.menu;
  }

  onChangeLanguage(language: string) {
    this.styleService.languageChanged.next(language);
  }

  logout() {
    this.authenticationService.logoutWithCallApi();
  }
}
