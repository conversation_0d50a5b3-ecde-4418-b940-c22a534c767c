
<div class="menu-container">
  <div *ngFor="let item of items">
    <div *ngIf="item.items && item.visible !== false" class="menu-item">
      <button [matMenuTriggerFor]="subMenu" [routerLinkActiveOptions]="{exact:true}" mat-menu-item
              routerLinkActive="active">
        {{(item.label ? item.label : '') | i18n }}
      </button>
      <mat-menu #subMenu="matMenu" xPosition="after">
        <div *ngFor="let subItem of item.items">
          <a *ngIf="subItem.visible !== false" [routerLink]="subItem.routerLink?  subItem.routerLink: ' '"
             mat-menu-item><p
            *ngIf="subItem.label">{{subItem.label | i18n }}</p></a>
        </div>
      </mat-menu>
    </div>
    <div *ngIf="item.items==undefined && item.visible !== false" class="menu-item">
      <a [routerLinkActiveOptions]="{exact:true}" [routerLink]="item && item.routerLink ? item.routerLink : ' '"
         mat-menu-item
         routerLinkActive="active">  <!-- exact: true; means that the URL matches the link exactly-->
        <p *ngIf="item.label">{{item.label| i18n }}</p></a>
    </div>
  </div>
</div>
