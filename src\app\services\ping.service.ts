import {Injectable} from '@angular/core';
import {HttpClient, HttpHeaders} from '@angular/common/http';
import {FW_Properties} from "@ttwr-framework/ngx-main-visuals";
import {ConfigService} from "./config.service";

@Injectable({
  providedIn: 'root'
})
export class PingService {
  constructor(private _http: HttpClient, private configService: ConfigService) {
    setInterval(() => {
      this.callPingService();
    }, 10000);
    this.callPingService();
  }

  callPingService() {
    if (localStorage.getItem('token') !== null) {
      this._http
        .get<any>(this.configService.configuration.pingUrl, {
          headers: new HttpHeaders({
            [FW_Properties.IGNORE_LOADING_REQ_HEADER]: 'true',
          }),
        })
        .subscribe();
    }
  }
}
