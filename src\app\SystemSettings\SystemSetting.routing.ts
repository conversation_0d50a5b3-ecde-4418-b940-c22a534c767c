import {RouterModule, Routes} from "@angular/router";
import {SystemSettingIndexComponent} from "./Components/system-setting-index/system-setting-index.component";
import {SystemSettingComponent} from "./SystemSetting.component";
import {NgModule} from "@angular/core";

export const routes: Routes = [
  {
    path: 'system-setting',
    component: SystemSettingComponent,
    children: [
      {
        path: 'index',
        component: SystemSettingIndexComponent,
        data: { perms: { ClearName: 'استعراض إعدادات النظام' } },
      }
    ],
  },
];

@NgModule({
  imports: [
    RouterModule.forChild(routes)
  ],
  exports: [
    RouterModule
  ]
})

export class SystemSettingRouting {

}
