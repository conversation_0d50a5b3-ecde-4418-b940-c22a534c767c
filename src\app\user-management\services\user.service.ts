import {Injectable} from '@angular/core';
import jwt_decode from "jwt-decode";
import {HttpClient, HttpParams} from "@angular/common/http";
import {Filter, LanguageService, Model, Sort} from "@ttwr-framework/ngx-main-visuals";
import {Observable} from "rxjs";
import {SYGSBaseService} from "../../services/SYGSBaseService";
import {map} from "rxjs/operators";

@Injectable({
  providedIn: 'root'
})
export class UserService extends SYGSBaseService {
  protected baseName = 'Account';

  constructor(protected http: HttpClient, private languageService: LanguageService) {
    super(http);
  }

  private _username = null;

  get username(): any {
    return this._username;
  }

  indexOnGroup(
    id: any,
    filter: Filter[],
    sort: Sort[],
    pageNumber = 0,
    pageSize = 5
  ): Observable<Model[]> {
    const params = this.getGridParams(filter, sort, pageNumber, pageSize);
    return this.http.get<Model[]>(`/api/` + this.baseName + `/indexOnGroup/` + id, {
      params: params,
    });
  }

  updateWithStringId(id: string, obj: any): Observable<Model> {
    return this.http.put<Model>(`/api/` + this.baseName + `/UpdateUser`, {
      userId: id,
      roles: obj.role,
      userName: obj.username,
      employeeId: obj.employeeId,
      isInactive: obj.isInactive,
      workingStartHour: obj.workingStartHour,
      workingEndHour: obj.workingEndHour,
      branchIdentificationCode: obj.branchIdentificationCode,
    });
  }

  create(obj: any): Observable<Model> {
    return this.http.post<Model>(`/api/` + this.baseName + `/CreateUser`, obj);
  }

  addGroups(id: any, obj: Model): Observable<Model> {
    return this.http.post<Model>(`/api/` + this.baseName + `/addGroups/` + id, obj);
  }

  deleteGroup(id: number, userId: string | number): Observable<Model> {
    return this.http.post<Model>(`/api/` + this.baseName + `/deleteGroup/` + userId, {
      UserGroup: id,
    });
  }

  getRoles(): Observable<Model[]> {
    return this.http.get<Model[]>(`/api/` + this.baseName + `/Roles/`)
  }

  viewCurrentUser(): void {
    var decoded = null;
    try {
      decoded = jwt_decode<any>(localStorage.getItem('token') ?? '');
    } catch (error) {
      localStorage.removeItem('token')
      window.location.reload()
      console.log('Invalid token format', error);
      throw error;
    }
    this._username = decoded['name'];
  }


  getUsers(
    filter: Filter[] = [],
    sort: Sort[] = [],
    pageNumber = 0,
    pageSize = 5,
    isActive: boolean,
  ): Observable<Model[]> {
    let params = this.getGridParams(filter, sort, pageNumber, pageSize);
    params = params.set('isActive', isActive)
    return this.http.get<Model[]>(`/api/` + this.baseName + `/Users`, {
      params: params,
    }).pipe(map((obj: any) => {
      if (obj) {
        obj['items']?.forEach((item: any) => {
          item.roles = '';
          if (item.userRoles != null && item.userRoles.length > 0) {
            item.userRoles.forEach((role: any) => {
              item.roles += this.languageService.getLang(role.role.name) + ' , ';
            })
            item.roles = item.roles.substring(0, item.roles.length - 2);
          }
        })
        return obj;
      }
    }));
  }

  GetUserProfile() {
    return this.http.get(`/api/` + this.baseName + `/GetUserProfile`)
  }

  ActivatedUser(id: string) {
    return this.http.put(`/api/` + this.baseName + `/ActivateUser/` + id, {})
  }

  getUserById(id: string) {
    return this.http.get<any>(`/api/${this.baseName}/GetUser/Id?id=${id}`);
  }
}
