import {GridDef, LanguageService, Model, ModelLovGridComponent} from "@ttwr-framework/ngx-main-visuals";
import {Component, OnInit} from "@angular/core";
import {BankWireType} from "../Models/BankWireType.model";
import {Observable} from "rxjs";
import {BankWireTypeService} from "../Services/bank-wire-type.service";
import {ActivatedRoute, Router} from "@angular/router";

@Component({
  template: `
    <ttwr-lov-grid [config]="config" [selectName]="selectName" [selectValue]="selectValue"></ttwr-lov-grid>
  `
})
export class LOVBankWireTypeListComponent extends ModelLovGridComponent implements OnInit {
  public config: GridDef = {
    title: 'BankWireTypes',
    fields: [
      {
        key: 'type'
      },
      {
        key: 'enName', isHidden: this.languageService.getSelectedLanguage() == 'ar'
      },
      {
        key: 'arName', isHidden: this.languageService.getSelectedLanguage() == 'en'
      }
    ],
    actionFields: [],
    actions: [],
    disableToggleAll: true,
    export: {
      pdf: {
        enable: false
      },
      excel: {
        enable: false
      }
    },
    fieldsDef: BankWireType.getModelDef(),
    dataFunction: (filter, sort, pageIndex, pageSize): Observable<Model[]> => {
      var type: string;
      if (this.router.url.includes('CustomerOutgoingBankWireModelCreate') || this.router.url.includes('customerOutgoingBankWireUpdate')) {
        type = 'MT103'
      } else {
        type = 'MT202'
      }
      return this.bankWireTypeService.getTypes(filter, sort, pageIndex, pageSize, type)

    }
  }

  constructor(private bankWireTypeService: BankWireTypeService,
              private languageService: LanguageService, private router: Router) {
    super();
  }

  ngOnInit() {
    if (this.languageService.selectedLanguage == 'ar') {
      this.selectName = 'arName'
    } else {
      this.selectName = 'enName'
    }
    this.selectValue = 'type'
  }
}
