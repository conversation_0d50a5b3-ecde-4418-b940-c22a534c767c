import { NgModule } from '@angular/core';
import { OutgoingBankWireMainComponent } from './outgoing-bank-wire-main/outgoing-bank-wire-main.component';
import { OutgoingBankWireService } from './Services/outgoing-bank-wire.service';
import { CustomerOutgoingBankWireCreateComponent } from './OutgoingBankWireComponents/CustomerOutgoing-bank-wire-create/CustomerOutgoing-bank-wire-create.component';
import { OutgoingBankWireIndexComponent } from './OutgoingBankWireComponents/outgoing-bank-wire-index/outgoing-bank-wire-index.component';
import { OutgoingBankWireModelViewComponent } from './OutgoingBankWireComponents/outgoing-bank-wire-model-view/outgoing-bank-wire-model-view.component';
import { CustomerOutgoingBankWireUpdateComponent } from './OutgoingBankWireComponents/customer-outgoing-bank-wire-update/customer-outgoing-bank-wire-update.component';
import { OutgoingBankWireComponent } from './OutgoingBankWireComponents/outgoing-bank-wire.component';
import { SharedModule } from '../shared/shared.module';
import { OutgoingBankWireRouting } from './OutgoingBankWire.routing';
import { OutgoingBankWireRevisionComponent } from './outgoing-bank-wire-revision/outgoing-bank-wire-revision.component';
import { OutgoingBankWireTypeComponent } from './OutgoingBankWireComponents/outgoing-bank-wire-type/outgoing-bank-wire-type.component';
import { OutgoingBankWireCreateComponent } from './OutgoingBankWireComponents/outgoing-bank-wire-create/outgoing-bank-wire-create.component';
import { OutgoingBankWireUpdateComponent } from './OutgoingBankWireComponents/outgoing-bank-wire-update/outgoing-bank-wire-update.component';
import { ExportToPdfService } from '../services/exportToPdf.service';

@NgModule({
  imports: [SharedModule, OutgoingBankWireRouting],
  exports: [],
  providers: [OutgoingBankWireService, ExportToPdfService],
  declarations: [
    OutgoingBankWireMainComponent,
    CustomerOutgoingBankWireCreateComponent,
    OutgoingBankWireIndexComponent,
    OutgoingBankWireModelViewComponent,
    CustomerOutgoingBankWireUpdateComponent,
    OutgoingBankWireComponent,
    OutgoingBankWireRevisionComponent,
    OutgoingBankWireTypeComponent,
    OutgoingBankWireCreateComponent,
    OutgoingBankWireUpdateComponent,
  ],
})
export class OutgoingBankWireModule {}
