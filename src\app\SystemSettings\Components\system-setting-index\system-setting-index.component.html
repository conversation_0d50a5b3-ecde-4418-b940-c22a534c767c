<div *ngIf="systemSettings | async as configurations">
  <mat-accordion>
    <mat-expansion-panel *ngFor="let group of configurations;">
      <mat-expansion-panel-header>
        <mat-panel-title>
          {{ group.systemSettingCategory | i18n }}
        </mat-panel-title>
      </mat-expansion-panel-header>

      <span class="important-msg" *ngIf="group.systemSettingCategory == 'SessionSetting'">{{ 'ThisNeedRestartServices' | i18n }}</span>
      <table class="table table-striped">
        <thead>
        <tr>
          <th>{{ 'key' | i18n }}</th>
          <th>{{ 'type' | i18n }}</th>
          <th>{{ 'value' | i18n }}</th>
          <th>{{ 'Actions' | i18n }}</th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let config of group.systemSettingDtos;">
          <td>{{ config.key | i18n }}</td>
          <td>{{ config.systemSettingType | i18n }}</td>
          <td>{{ config.value | i18n }}</td>
          <td>
            <button
              class="btn-edit-icon"
              (click)="edit(config)"
              [matTooltip]="'Edit' | i18n"
            >
              <img src="assets/icons/grid/ic_edit.svg"/>
            </button>
          </td>
        </tr>
        </tbody>
      </table>
    </mat-expansion-panel>
  </mat-accordion>
</div>
