import {Injectable} from '@angular/core';
import {<PERSON>ttpClient, HttpHandler, HttpInterceptor, HttpRequest} from '@angular/common/http';
import {Observable} from 'rxjs';
import {AuthenticationService} from "../main/services/authentication.service";
import {LanguageService} from "@ttwr-framework/ngx-main-visuals";
import {ConfigService} from "../services/config.service";

@Injectable()
export class UserDataInterceptor implements HttpInterceptor {

  constructor(public http: HttpClient, private authService: AuthenticationService,
              private languageService: LanguageService, private configService: ConfigService) {
  }

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<any> {
    let url = '';
    url = req.url.startsWith('/api/') ? this.configService.configuration.apiUrl + '/' + req.url.substr(5) : req.url;

    if (this.authService.getAccessToken()) {
      req = req.clone({
        setHeaders: {
          'Authorization': this.authService.getAccessToken(),
          'Accept-Language': this.languageService.selectedLanguage,
        },
        // withCredentials: true,
        url: url
      });
    } else {
      req = req.clone({
        setHeaders: {
          'Accept-Language': this.languageService.selectedLanguage,
        },
        // withCredentials: true,
        url: url
      });
    }

    return next.handle(req);
  }

}
