import {Validators} from '@angular/forms';

import {Component, OnInit} from '@angular/core';
import {Observable, throwError} from 'rxjs';
import {UserService} from '../services/user.service';
import {UserGroupLovGrid} from './auth-item.model';
import {
  AlertService,
  GridDef,
  Model,
  ModelLovGridComponent, TtwrInputCheckboxComponent,
  TtwrInputComponent,
  TtwrInputDateComponent,
  TtwrInputLovComponent,
  Types,
} from '@ttwr-framework/ngx-main-visuals';
import {catchError} from "rxjs/operators";
import { LOVBankBranchesListComponent } from '../../Banks/LOVBankBranchesList/LOVBankBranchesList.component';
import { LOVREBBranchesListComponent } from '../../Banks/LOVBankBranchesList/LOVREBBranchesList.component';

export class User extends Model {
  id!: string;
  FullName!: string;
  username!: string;
  PasswordHash!: string;
  Dob!: string;
  // zone: number;
  // area: number;
  Mobile!: string;
  Phone!: string;
  Email!: string;
  ExpiryDate!: string;
  roles!: string;
  employeeId!: string;
  isInactive!: boolean;
  workingStartHour!: string;
  workingEndHour!: string;
  branchIdentificationCode!: string;
  branchIdentificationName!: string;

  protected static initializeModelDef() {
    return {
      defs: {
        id: {
          placeholder: 'id',
          label: 'id',
          ui: TtwrInputComponent,
          validators: [],
        },
        FullName: {
          dataType: Types.STRING,
          placeholder: 'fullname',
          label: 'fullname',
          isSortable: true,
          ui: TtwrInputComponent,
          validators: [
            {
              name: 'required',
              validator: Validators.required,
              message: 'This field is required',
            },
          ],
        },
        username: {
          dataType: Types.STRING,
          placeholder: 'Username',
          label: 'Username',
          isSortable: true,
          ui: TtwrInputComponent,
          validators: [
            {
              name: 'required',
              validator: Validators.required,
              message: 'This field is required',
            },
            {
              name: 'pattern',
              validator: Validators.pattern('[a-zA-Z0-9\\-._@+\\\\]+'),
              message: 'Invalid username',
            },
          ],
        },
        PasswordHash: {
          dataType: Types.PASSWORD,
          placeholder: 'Password',
          label: 'Password',
          isSortable: true,
          ui: TtwrInputComponent,
          validators: [
            {
              name: 'required',
              validator: Validators.required,
              message: 'This field is required',
            },
            {
              name: 'minlength',
              validator: Validators.minLength(6),
              message: 'Password must be 6 characters'
            }
          ],
        },
        Dob: {
          dataType: Types.DATE,
          placeholder: 'Birth Date',
          label: 'Birth Date',
          ui: TtwrInputDateComponent,
        },
        workingStartHour: {
          dataType: Types.STRING,
          placeholder: 'WorkingStartHour',
          label: 'WorkingStartHour',
          ui: TtwrInputComponent,
        },
        workingEndHour: {
          dataType: Types.STRING,
          placeholder: 'WorkingEndHour',
          label: 'WorkingEndHour',
          ui: TtwrInputComponent,
        },
        Mobile: {
          dataType: Types.STRING,
          placeholder: 'Mobile',
          label: 'Mobile',
          isSortable: true,
          ui: TtwrInputComponent,
          validators: [
            {
              name: 'required',
              validator: Validators.required,
              message: 'This field is required',
            },
          ],
        },
        Phone: {
          dataType: Types.STRING,
          placeholder: 'Phone',
          label: 'Phone',
          isSortable: true,
          ui: TtwrInputComponent,
          validators: [
            {
              name: 'required',
              validator: Validators.required,
              message: 'This field is required',
            },
          ],
        },
        roles: {
          dataType: Types.STRING,
          placeholder: 'roles',
          label: 'roles',
          ui: TtwrInputComponent,
        },
        Email: {
          dataType: Types.STRING,
          placeholder: 'Email',
          label: 'Email',
          isSortable: true,
          ui: TtwrInputComponent,
          validators: [
            {
              name: 'required',
              validator: Validators.required,
              message: 'This field is required',
            },
          ],
        },
        ExpiryDate: {
          dataType: Types.DATE,
          placeholder: 'Expiry date',
          label: 'Expiry date',
          ui: TtwrInputDateComponent,
          validators: [
            {
              name: 'required',
              validator: Validators.required,
              message: 'Expiry date is required',
            },
          ],
        },
        UserGroupS: {
          dataType: Types.STRING,
          placeholder: 'User groups',
          label: 'User groups',
          ui: TtwrInputLovComponent,
          config: {
            popUpHeader: 'Groups',
            gridComponent: UserGroupLovGrid,
            searchValueField: 'UserGroupsNames',
            multiSelect: true,
          },
        },
        UserGroupsNames: {
          dataType: Types.STRING,
          ui: TtwrInputComponent,
        },
        employeeId: {
          dataType: Types.STRING,
          placeholder: 'employeeId',
          label: 'employeeId',
          ui: TtwrInputComponent,
          validators: [
            {
              name: 'required',
              validator: Validators.required,
              message: 'this field is required'
            }
          ]
        },
        isInactive: {
          dataType: Types.NUMBER,
          placeholder: 'Active',
          label: 'Active',
          isSortable: true,
          defaultValue: true,
          ui: TtwrInputCheckboxComponent,
        },
        branchIdentificationCode: {
          ID: 'branchIdentificationCode',
          dataType: Types.STRING,
          label: 'branchIdentificationCode',
          ui: TtwrInputLovComponent,
          config: {
            multiSelect: false,
            gridComponent: LOVREBBranchesListComponent,
            searchValueField: 'payBranchBIC',
          },
        },
        branchIdentificationName: {
          dataType: Types.STRING,
          placeholder: 'branchIdentificationName',
          label: 'branchIdentificationName',
          ui: TtwrInputComponent,
        },
      },
      validators: [],
    };
  }
}

@Component({
  selector: 'app-user-lov-grid',
  template:
    '<ttwr-lov-grid [config]="config" [selectName]="selectName" [selectValue]="selectValue"></ttwr-lov-grid>',
})
export class UserLovGridComponent extends ModelLovGridComponent implements OnInit {
  public config: GridDef = {
    title: 'Users',
    fields: [{key: 'FullName'}, {key: 'Username'}],
    actionFields: [],
    fieldsDef: User.getModelDef(),
    actions: [],
    dataFunction: (filter, sort, pageIndex, pageSize): Observable<Model[]> => {
      return this.userService.index(filter, sort, pageIndex, pageSize).pipe(catchError(err => {
        this.alertService.error(err.message);
        return throwError(err.message);
      }));
    },
  };

  constructor(private userService: UserService,
              private alertService: AlertService) {
    super();
  }

  ngOnInit() {
    this.selectValue = 'Id';
    this.selectName = 'FullName';
  }
}
