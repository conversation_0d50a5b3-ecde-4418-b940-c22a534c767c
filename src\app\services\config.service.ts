import {Injectable} from '@angular/core';

interface Configuration {
  enablePermissions: Boolean;
  apiUrl: string;
  appName: string;
  authenticationClientId: string;
  authenticationUrl: string;
  authorizationUrl: string;
  pingUrl: string;
  MinimumInsAmount: number;
}

@Injectable({
  providedIn: 'root'
})
export class ConfigService {
  public configuration: Configuration = {
    enablePermissions: false,
    apiUrl: "",
    authenticationClientId: "",
    appName: "",
    authenticationUrl: "",
    pingUrl: "",
    authorizationUrl: "",
    MinimumInsAmount: 50000,
  };

  constructor() {
  }
}
