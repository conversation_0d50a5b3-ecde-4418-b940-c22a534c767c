import {Injectable} from '@angular/core';
import {Filter, Model, Sort} from "@ttwr-framework/ngx-main-visuals";
import {Observable} from "rxjs";
import {map} from "rxjs/operators";
import {HttpParams} from "@angular/common/http";
import {SYGSBaseService} from "../../services/SYGSBaseService";

@Injectable()
export class OutgoingTransfersModelService extends SYGSBaseService {
  protected baseName = 'REB';


  viewAsString(id: string): Observable<Model> {
    let params = new HttpParams().set('transferId', id)
    return this.http.get<Model>(`/api/` + this.baseName + `/GetTransferById/`, {
      params: params
    }).pipe(map((obj: any) => {
      if (obj) {
        obj['operations']?.forEach((item: any) => {
          item.resultCode = item.result?.code;
          item.resultMessage = item.result?.message;
          item.resultXapiReferenceNumber = item.result?.xapiReferenceNumber;
          item.resultAuthorizeCode = item.result?.authorizeCode;
        })

        return obj;
      }
    }));
  }


  index(
    filter: Filter[] = [],
    sort: Sort[] = [],
    pageNumber = 0,
    pageSize = 5
  ): Observable<Model[]> {
    const params = this.getGridParams(filter, sort, pageNumber, pageSize);
    return this.http.get<Model[]>(`/api/` + this.baseName + `/GetOutingTransfersReport`, {
      params: params,
    }).pipe(map((res: any) => {
      var newObj: any = {
        Items: res.data.items,
        PageSize: res.data.pageSize,
        TotalCount: res.data.totalCount
      }
      return newObj;
    }));
  }
}
