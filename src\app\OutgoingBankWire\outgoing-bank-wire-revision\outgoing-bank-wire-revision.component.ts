import {Component, Inject, Injectable, OnInit} from '@angular/core';
import {AlertService, FormDef, TtwrInputTextareaComponent, Types} from "@ttwr-framework/ngx-main-visuals";
import {Validators} from "@angular/forms";
import {OutgoingBankWireService} from "../Services/outgoing-bank-wire.service";
import {MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA, MatLegacyDialog as MatDialog} from "@angular/material/legacy-dialog";

@Component({
  selector: 'app-outgoing-bank-wire-revision',
  templateUrl: './outgoing-bank-wire-revision.component.html',
  styleUrls: ['./outgoing-bank-wire-revision.component.sass']
})
export class OutgoingBankWireRevisionComponent implements OnInit {
  public dialogTitle: string = 'RevisionOutgoingBankWire'
  public config: FormDef = {
    fields: [
      {
        key: 'reason'
      }
    ],
    actionFields: [
      {
        label: 'Submit',
        cssClass: 'btn-info',
        delegateFunction: (obj) => {
          this.outgoingBankWireService.revision(this.data.id, obj.reason).subscribe((res: any) => {
            if (res.code == 0) {
              this.alertService.success('successfully send reason')
            } else {
              this.alertService.error(res.message)
            }
          }, error => {
            this.alertService.error(error)
          })
          this.dialog.closeAll()

        }
      }
    ],
    fieldsDef: {
      defs: {
        reason: {
          ID: 'reason',
          dataType: Types.STRING,
          label: 'reason',
          placeholder: 'reason',
          ui: TtwrInputTextareaComponent,
          validators: [
            {
              name: 'required',
              validator: Validators.required,
              message: 'this field is required'
            }
          ]
        }
      }
    }

  }

  constructor(private outgoingBankWireService: OutgoingBankWireService,
              @Inject(MAT_DIALOG_DATA) public data: any,
              private alertService: AlertService,
              private dialog: MatDialog) {
  }

  ngOnInit(): void {
  }

}
