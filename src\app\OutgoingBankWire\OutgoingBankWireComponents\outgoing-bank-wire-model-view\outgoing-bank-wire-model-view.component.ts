import {Component, OnInit} from '@angular/core';
import {OutgoingBankWireModel} from '../../Models/OutgoingBankWire.model';
import {ActivatedRoute, Router} from '@angular/router';
import {OutgoingBankWireService} from '../../Services/outgoing-bank-wire.service';
import {
  AlertService,
  Helper,
  LanguageService,
  MasterDetailDef,
  Model,
  TtwrInputTextareaComponent,
  ViewDef
} from '@ttwr-framework/ngx-main-visuals';
import {MatLegacyDialog as MatDialog} from "@angular/material/legacy-dialog";
import {
  OutgoingBankWireRevisionComponent
} from "../../outgoing-bank-wire-revision/outgoing-bank-wire-revision.component";
import {OutgoingBankWireStatusChangeEventModel} from "../../Models/OutgoingBankWireStatusChangeEvent.model";
import {Observable} from "rxjs";
import {formatDate, formatNumber} from "@angular/common";
import {ExportToPdfService} from "../../../services/exportToPdf.service";
import {map} from "rxjs/operators";


@Component({
  selector: 'app-outgoing-bank-wire-model-view',
  templateUrl: './outgoing-bank-wire-model-view.component.html',
  styleUrls: ['./outgoing-bank-wire-model-view.component.scss']
})
export class OutgoingBankWireModelViewComponent implements OnInit {
  operations: any;
  isCustomerBankWire: boolean = true;

  public customerConfig: MasterDetailDef = {
    masterViewConfig: {
      title: 'OutgoingBankWireModel',
      fields: [
        {
          key: 'dtAccount',
          cssClass: {field: 'col-md-6 col-12', value: 'label-color'},
        },
        {key: 'dtName', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'dtAdd', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'dtMotherName', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'dtNationalNumber', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'dtPhoneNumber', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'rmtInformation', cssClass: {field: 'col-md-6 col-12', value: 'label-color text-preline'}},
        {key: 'id', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'addedAt', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'sygsRef', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}, isHidden: true},
        {key: 'orderType', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'ttcName', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'payBankBICName', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'payBranchBICName', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'benefitBankBICName', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'benefitBranchBICName', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'payPartBICName', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}, isHidden: true},
        {key: 'benefitPartBICName', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}, isHidden: true},
        {key: 'optCode', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}, isHidden: true},
        {key: 'insCurrency', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {
          key: 'insAmount', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}, displayCellValueFunc: (val) => {
            if (val) {
              return formatNumber(val, 'en', '3.0-0');
            } else return '-'
          }
        },
        {key: 'insAmountAsWords', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'exchangeRate', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'dtlChg', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'sendChangeAmount', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'receiveChangeAmount', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'settlementCurrency', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {
          key: 'settlementAmount', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}, displayCellValueFunc: (val) => {
            if (val) {
              return formatNumber(val, 'en', '3.0-0');
            } else return '-'
          }
        },
        {key: 'settlementAmountAsWords', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'priority', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'sendToReceiveInformation', cssClass: {field: 'col-md-6 col-12', value: 'label-color text-preline'}},
        {key: 'ctAccount', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'ctName', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'ctAdd', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'ctPhoneNumber', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'state', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}, isHidden: true},
        {key: 'valueDate', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'sendReference', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'relatedReference', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'checkNumber', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'endrCount', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'endrNames', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {
          key: 'isTransferred', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}, displayCellValueFunc: (val) => {
            if (val) {
              return this.languageService.getLang(val)
            } else {
              return '-'
            }
          }
        },
      ],
      fieldsDef: {
        defs: {
          ...OutgoingBankWireModel.getModelDef().defs,
          sendToReceiveInformation: {
            ID: 'sendToReceiveInformation',
            ui: TtwrInputTextareaComponent,
            label: 'sendToReceiveInformation',
          },
          rmtInformation: {
            ID: 'rmtInformation',
            ui: TtwrInputTextareaComponent,
            label: 'rmtInformation',
          },
        }
      },
      actionFields: [
        {
          label: 'Update',
          cssClass: 'btn-warning',
          visible: (obj: any) => {
            if ((obj.lastState == 'Initial' || obj.lastState == 'NeedCorrect') &&
              (localStorage.getItem('roles')?.includes('OutgoingBankWireManagement') ||
                localStorage.getItem('roles')?.includes('SuperAdmin'))) {
              return true
            } else {
              return false
            }
          },
          delegateFunction: (obj) => {
            console.log(obj)
            if (obj.orderType == 'MT103') {
              this.router.navigate(['customerOutgoingBankWireUpdate/' + obj['id']], {relativeTo: this.activatedRoute.parent})
            } else {
              this.router.navigate(['OutgoingBankWireUpdate/' + obj['id']], {relativeTo: this.activatedRoute.parent})
            }

          }
        },
        // {
        //   label: 'Delete',
        //   cssClass: 'btn-warning',
        //   confirmationRequired: true,
        //   delegateFunction: (obj) => this.outgoingBankWireModelService.delete(this.config.obj['Id']).subscribe(() => {
        //     this.alertService.success('Success Delete');
        //     this.router.navigate(['index/'], {relativeTo: this.activatedRoute.parent});
        //   }),
        // },
        {
          label: 'Send',
          cssClass: 'btn-info',
          confirmationRequired: true,
          visible: (obj: any) => {
            if ((obj.lastState == 'Initial' || obj.lastState == 'NeedCorrect') &&
              (localStorage.getItem('roles')?.includes('OutgoingBankWireAuditing') ||
                localStorage.getItem('roles')?.includes('SuperAdmin'))) {
              return true
            } else {
              return false
            }
          },
          delegateFunction: (obj) => this.outgoingBankWireModelService.audit(this.config.masterViewConfig.obj.id).subscribe((res: any) => {
            if (res.code == 0) {
              this.alertService.success('Success Audit');
              this.config.detailGridsConfig[0].refreshSubject?.next(true)
            } else {
              this.alertService.error(res.message)
            }
          }, error => {
            this.alertService.error(error)
          }),
        },
        {
          label: 'Revision',
          cssClass: 'btn-info',
          visible: (obj: any) => {
            if ((obj.lastState == 'Initial' || obj.lastState == 'NeedCorrect') &&
              (localStorage.getItem('roles')?.includes('OutgoingBankWireAuditing') ||
                localStorage.getItem('roles')?.includes('SuperAdmin'))) {
              return true
            } else {
              return false
            }
          },
          delegateFunction: (obj) => {
            this.dialog.open(OutgoingBankWireRevisionComponent, {
                width: '50%',
                panelClass: 'ttwr-dialog',
                data: {
                  id: obj.id
                }
              }
            ).afterClosed().subscribe(() => {
              this.config.detailGridsConfig[0].refreshSubject?.next(true)
            })
          }
        },
        {
          label: 'Done',
          cssClass: 'btn-danger',
          delegateFunction: (obj) => this.router.navigate(['index/'], {relativeTo: this.activatedRoute.parent})
        },
        {
          label: 'Export to PDF',
          cssClass: 'Done-style',
          delegateFunction: (obj) => {
            var title: string = 'تفاصيل حوالة صادرة';
            obj['operations'] = this.operations.items;
            this.exportToPdfService.exportPdf(obj, title, obj.id, false);
          }
        },
      ]
    },
    detailGridsConfig: [{
      title: 'Events',
      fields: [
        {
          key: 'eventName', displayCellValueFunc: (val) => {
            if (val) {
              return this.languageService.getLang(val)
            } else {
              return '-'
            }
          }
        },
        {
          key: 'executeDate', displayCellValueFunc: (val) => {
            if (val) {
              return formatDate(new Date(val), 'yyyy-MM-dd hh:mm:ss', 'en')
            } else {
              return '-'
            }
          }
        },
        {
          key: 'correctionReason'
        },
        {
          key: 'exception'
        },
        {
          key: 'employeeId', isHidden: true
        },
        {
          key: 'employeeName',
        },
        {
          key: 'xapiReferenceNumber'
        },
        {
          key: 'isSuccess', displayCellValueFunc: (val) => {
            if (val != null) {
              return this.languageService.getLang(val)
            } else {
              return '-'
            }
          }
        },
      ],
      actionFields: [],
      actions: [],
      paginator: {
        initialPageSize: 30
      },
      fieldsDef: OutgoingBankWireStatusChangeEventModel.getModelDef(),
      dataFunction: (filter, sort, pageIndex, pageSize): Observable<Model[]> => {
        return this.outgoingBankWireModelService
          .getOutgoingBankWireStatusChangeEvents(pageIndex, pageSize, this.config.masterViewConfig.obj.id).pipe(map((obj) => {
            this.operations = obj
            return obj;
          }));
      },
      disableToggleAll: true,
      disableFiltrableColumns: true,
      export: {
        pdf: {
          enable: false
        },
        excel: {
          enable: false
        }
      }
    }],
  };

  public config: MasterDetailDef = {
    masterViewConfig: {
      title: 'OutgoingBankWireModel',
      fields: [
        {
          key: 'dtAccount',
          cssClass: {field: 'col-md-6 col-12', value: 'label-color', label: 'PayingBankAccountNumber'},
        },
        {key: 'rmtInformation', cssClass: {field: 'col-md-6 col-12', value: 'label-color text-preline'}, label: 'notes'},
        {key: 'id', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'addedAt', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'orderType', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'ttcName', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'payBankBICName', cssClass: {field: 'col-md-6 col-12', value: 'label-color'},label:'payBankName'},
        {key: 'payBranchBICName', cssClass: {field: 'col-md-6 col-12', value: 'label-color'},label:'payBranchName'},
        {key: 'benefitBankBICName', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}, label: 'BeneficiaryBankName'},
        {key: 'benefitBranchBICName', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'insCurrency', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {
          key: 'insAmount', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}, displayCellValueFunc: (val) => {
            if (val) {
              return formatNumber(val, 'en', '3.0-0');
            } else return '-'
          }
        },
        {key: 'insAmountAsWords', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'exchangeRate', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'dtlChg', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'settlementCurrency', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {
          key: 'settlementAmount', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}, displayCellValueFunc: (val) => {
            if (val) {
              return formatNumber(val, 'en', '3.0-0');
            } else return '-'
          }
        },
        {key: 'settlementAmountAsWords', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'priority', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'sendToReceiveInformation', cssClass: {field: 'col-md-6 col-12', value: 'label-color text-preline'}},
        {key: 'ctAccount', cssClass: {field: 'col-md-6 col-12', value: 'label-color', label: 'BeneficiaryBankAccountNumber'}},
        {key: 'valueDate', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'sendReference', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {key: 'relatedReference', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}},
        {
          key: 'isTransferred', cssClass: {field: 'col-md-6 col-12', value: 'label-color'}, displayCellValueFunc: (val) => {
            if (val) {
              return this.languageService.getLang(val)
            } else {
              return '-'
            }
          }
        },
      ],
      fieldsDef: {
        defs: {
          ...OutgoingBankWireModel.getModelDef().defs,
          sendToReceiveInformation: {
            ID: 'sendToReceiveInformation',
            ui: TtwrInputTextareaComponent,
            label: 'sendToReceiveInformation',
          },
          rmtInformation: {
            ID: 'rmtInformation',
            ui: TtwrInputTextareaComponent,
            label: 'notes',
          },
        }
      },
      actionFields: [
        {
          label: 'Update',
          cssClass: 'btn-warning',
          visible: (obj: any) => {
            if ((obj.lastState == 'Initial' || obj.lastState == 'NeedCorrect') &&
              (localStorage.getItem('roles')?.includes('OutgoingBankWireManagement') ||
                localStorage.getItem('roles')?.includes('SuperAdmin'))) {
              return true
            } else {
              return false
            }
          },
          delegateFunction: (obj) => {
            console.log(obj)
            if (obj.orderType == 'MT103') {
              this.router.navigate(['customerOutgoingBankWireUpdate/' + obj['id']], {relativeTo: this.activatedRoute.parent})
            } else {
              this.router.navigate(['OutgoingBankWireUpdate/' + obj['id']], {relativeTo: this.activatedRoute.parent})
            }

          }
        },
        // {
        //   label: 'Delete',
        //   cssClass: 'btn-warning',
        //   confirmationRequired: true,
        //   delegateFunction: (obj) => this.outgoingBankWireModelService.delete(this.config.obj['Id']).subscribe(() => {
        //     this.alertService.success('Success Delete');
        //     this.router.navigate(['index/'], {relativeTo: this.activatedRoute.parent});
        //   }),
        // },
        {
          label: 'Send',
          cssClass: 'btn-info',
          confirmationRequired: true,
          visible: (obj: any) => {
            if ((obj.lastState == 'Initial' || obj.lastState == 'NeedCorrect') &&
              (localStorage.getItem('roles')?.includes('OutgoingBankWireAuditing') ||
                localStorage.getItem('roles')?.includes('SuperAdmin'))) {
              return true
            } else {
              return false
            }
          },
          delegateFunction: (obj) => this.outgoingBankWireModelService.audit(this.config.masterViewConfig.obj.id).subscribe((res: any) => {
            if (res.code == 0) {
              this.alertService.success('Success Audit');
              this.config.detailGridsConfig[0].refreshSubject?.next(true)
            } else {
              this.alertService.error(res.message)
            }
          }, error => {
            this.alertService.error(error)
          }),
        },
        {
          label: 'Revision',
          cssClass: 'btn-info',
          visible: (obj: any) => {
            if ((obj.lastState == 'Initial' || obj.lastState == 'NeedCorrect') &&
              (localStorage.getItem('roles')?.includes('OutgoingBankWireAuditing') ||
                localStorage.getItem('roles')?.includes('SuperAdmin'))) {
              return true
            } else {
              return false
            }
          },
          delegateFunction: (obj) => {
            this.dialog.open(OutgoingBankWireRevisionComponent, {
                width: '50%',
                panelClass: 'ttwr-dialog',
                data: {
                  id: obj.id
                }
              }
            ).afterClosed().subscribe(() => {
              this.config.detailGridsConfig[0].refreshSubject?.next(true)
            })
          }
        },
        {
          label: 'Done',
          cssClass: 'btn-danger',
          delegateFunction: (obj) => this.router.navigate(['index/'], {relativeTo: this.activatedRoute.parent})
        },
        {
          label: 'Export to PDF',
          cssClass: 'Done-style',
          delegateFunction: (obj) => {
            var title: string = 'تفاصيل حوالة صادرة';
            obj['operations'] = this.operations.items;
            this.exportToPdfService.exportPdf(obj, title, obj.id, false);
          }
        },
      ]
    },
    detailGridsConfig: [{
      title: 'Events',
      fields: [
        {
          key: 'eventName', displayCellValueFunc: (val) => {
            if (val) {
              return this.languageService.getLang(val)
            } else {
              return '-'
            }
          }
        },
        {
          key: 'executeDate', displayCellValueFunc: (val) => {
            if (val) {
              return formatDate(new Date(val), 'yyyy-MM-dd hh:mm:ss', 'en')
            } else {
              return '-'
            }
          }
        },
        {
          key: 'correctionReason'
        },
        {
          key: 'exception'
        },
        {
          key: 'employeeId', isHidden: true
        },
        {
          key: 'employeeName',
        },
        {
          key: 'xapiReferenceNumber'
        },
        {
          key: 'isSuccess', displayCellValueFunc: (val) => {
            if (val != null) {
              return this.languageService.getLang(val)
            } else {
              return '-'
            }
          }
        },
      ],
      actionFields: [],
      actions: [],
      paginator: {
        initialPageSize: 30
      },
      fieldsDef: OutgoingBankWireStatusChangeEventModel.getModelDef(),
      dataFunction: (filter, sort, pageIndex, pageSize): Observable<Model[]> => {
        return this.outgoingBankWireModelService
          .getOutgoingBankWireStatusChangeEvents(pageIndex, pageSize, this.config.masterViewConfig.obj.id).pipe(map((obj) => {
            this.operations = obj
            return obj;
          }));
      },
      disableToggleAll: true,
      disableFiltrableColumns: true,
      export: {
        pdf: {
          enable: false
        },
        excel: {
          enable: false
        }
      }
    }],
  };


  constructor(private route: ActivatedRoute, private router: Router,
              private outgoingBankWireModelService: OutgoingBankWireService,
              private alertService: AlertService,
              private activatedRoute: ActivatedRoute,
              private dialog: MatDialog,
              private exportToPdfService: ExportToPdfService,
              private languageService: LanguageService) {
  }

  ngOnInit() {
    if (this.route.snapshot.data.obj) {
      this.config.masterViewConfig.obj = Helper.deepCopy(this.route.snapshot.data.obj)
      this.customerConfig.masterViewConfig.obj = Helper.deepCopy(this.route.snapshot.data.obj)
      if (this.route.snapshot.data.obj.orderType == "MT202") {
        this.isCustomerBankWire = false
      }
    }
  }

}
