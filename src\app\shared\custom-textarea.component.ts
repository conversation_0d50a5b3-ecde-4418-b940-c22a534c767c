import { Component, OnDestroy } from '@angular/core';
import { TtwrBaseInputComponent } from '@ttwr-framework/ngx-main-visuals';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-custom-textarea',
  template: `
    <div
      *ngIf="field && field.viewOnly !== true"
      [hidden]="field && field.isHidden"
      [ngClass]="(field.cssClass && field.cssClass.field ? field.cssClass.field : '') + ' ' + (field.fieldActions && field.fieldActions.length > 0 ? 'hasFieldActions' : '')"
      class="form-group"


    >
      <label *ngIf="field.sideLabel!==false" [ngClass]="field.cssClass&& field.cssClass.label?field.cssClass.label:''">
        <span [matTooltip]="(field.tooltip?field.tooltip:'') | i18n">{{ (field.label ? field.label : '')| i18n }}</span>
        <span *ngIf="isRequired(field.validators?field.validators:[])" class="mat-form-field-required-marker">*</span>
      </label>
      <mat-form-field
        appearance="outline"
        class="mat-form-field-type-textarea-file"
        [formGroup]="group"
        style="width: 383px !important; min-width: 0;"
      >
        <textarea
          style="
          resize: none;
          font-family: 'Kawkab', monospace, sans-serif;
        "
          [rows]="this.config.rows"
          [maxLength]="this.config.maxLength"
          matInput
          [formControlName]="field.key"
          [required]="!!isRequired(field.validators ?? [])"
          [type]="field.dataType ?? 'text'"
          [placeholder]="(field.placeholder ?? '') | i18n"
        ></textarea>
        <mat-error>
          <ng-container *ngFor="let validator of (field.validators ?? [])">
            <span *ngIf="group.controls[field.key].hasError(validator.name!)">
              {{ validator.message! | i18n }}
            </span>
          </ng-container>
        </mat-error>
      </mat-form-field>
    </div>
  `,
})
export class CustomTextAreaComponent extends TtwrBaseInputComponent implements OnDestroy {
  private subscription?: Subscription;


  ngOnInit() {
    super.ngOnInit();

    if (this.group) {
      const control = this.group.controls[this.field.key];
      const COLS_COUNT = 35

      this.subscription = control?.valueChanges.subscribe((value: string) => {

        // Split the text into lines by existing newlines
        const lines = value.split(/\r?\n/);
        let newLines: string[] = [];

        // Loop through each line of the text
        lines.forEach((line) => {
          const producedLines = [];
          while (line.length > COLS_COUNT) {
            let wrapAt = line.lastIndexOf(' ', COLS_COUNT); // Find the last space within the column limit
            if (wrapAt === -1) wrapAt = COLS_COUNT;         // If no space, force wrap at the column limit

            // Append the wrapped part, including all spaces
            producedLines.push(line.slice(0, wrapAt));

            line = line.slice(wrapAt).replace(/^\s/, ''); // Move to the next part of the line without trimming spaces
          }
          producedLines.push(line);// Add the remaining part of the line
          newLines = [...newLines, ...producedLines];
        });

        control.setValue(newLines.slice(0, 5).join('\n'), {
          emitEvent: false,
        })
      })
    }
  }

  ngOnDestroy() {
    this.subscription?.unsubscribe();
  }
}
