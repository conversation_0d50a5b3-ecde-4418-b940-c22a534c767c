import {Component, Inject, LOCALE_ID} from '@angular/core';
import {IncomingTransfersModel} from '../../Models/IncomingTransfers.model';

import {ActivatedRoute, Router} from '@angular/router';

import {NewIncomingTransfersService} from '../../Services/new-incoming-transfers.service';
import {Observable, throwError} from 'rxjs';
import {AlertService, GridDef, LanguageService, Model} from '@ttwr-framework/ngx-main-visuals';
import {formatNumber} from "@angular/common";
import {catchError} from "rxjs/operators";
import {MatLegacyDialog as MatDialog} from "@angular/material/legacy-dialog";
import {NewIncomingTransfersViewComponent} from "../new-incoming-transfers-view/new-incoming-transfers-view.component";


@Component({
  selector: 'app-new-incoming-transfers-model-index',
  templateUrl: './new-incoming-transfers-index.component.html',
  styleUrls: ['./new-incoming-transfers-index.component.scss']
})
export class NewIncomingTransfersIndexComponent {
  id: any;

  public config: GridDef = {
    title: 'NewIncomingTransfersModel',
    fields: [
      {key: 'sygsRef'},
      {key: 'payBankBIC'},
      {key: 'valueDate'},
      {
        key: 'insAmount', displayCellValueFunc: (val) => {
          if (val) {
            return formatNumber(val, this.locale, '3.0-2');
          } else return '-'
        }
      },
      {
        key: 'settlementAmount', displayCellValueFunc: (val) => {
          if (val) {
            return formatNumber(val, this.locale, '3.0-2');
          } else return '-'
        }
      },
      {key: 'dtAccount'},
      {key: 'dtName'},
      {key: 'ctAccount'},
      {key: 'ctName'},
      {key: 'ctNameInREB'},
    ],
    paginator: {
      initialPageSize: 1,
      pageSizeOptions: [1, 5, 10, 20, 30]
    },
    actionFields: [
      {
        header: 'Actions',
        actions: [
          {
            icon: 'assets/icons/grid/ic_view.svg',
            cssClass: 'btn-view',
            tooltip: 'View',
            delegateFunction: (obj) => {
              this.dialog.open(NewIncomingTransfersViewComponent, {
                width: '80%',
                panelClass: 'ttwr-dialog',
                data: {
                  obj: obj
                }
              }).afterClosed().subscribe(() => {
                this.config.refreshSubject?.next(true)
              })
            }
          }
        ]
      },
    ],
    export: {
      excel: {
        enable: false
      },
      pdf: {
        enable: false
      }
    },
    disableToggleAll: true,
    disableFiltrableColumns: true,

    fieldsDef: IncomingTransfersModel.getModelDef(),
    actions: [],
    dataFunction: (filter, sort, pageIndex, pageSize): Observable<Model[]> => {
      return this.newIncomingTransfersService.index(filter, sort, pageIndex, pageSize).pipe(catchError(err => {
        this.alertService.error(err.error.message)
        return throwError(err.error.message)
      }));
    }
  };

  constructor(private newIncomingTransfersService: NewIncomingTransfersService, private alertService: AlertService,
              private router: Router, private activatedRoute: ActivatedRoute,
              private languageService: LanguageService,
              @Inject(LOCALE_ID) public locale: string,
              private dialog: MatDialog) {
  }

  ngOnInit() {
    // this.id = setInterval(() => {
    //   this.config.refreshSubject?.next(true)
    // }, 10 * 60 * 1000 ); // 10 s
  }

  ngOnDestroy() {
    // if (this.id) {
    //   clearInterval(this.id);
    // }
  }


}
