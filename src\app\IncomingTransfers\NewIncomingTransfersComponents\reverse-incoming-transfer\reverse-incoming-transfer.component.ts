import {Component, Inject, OnInit} from '@angular/core';
import {
  AlertService,
  FormDef,
  LanguageService,
  TtwrInputTextareaComponent,
  Types
} from "@ttwr-framework/ngx-main-visuals";
import {Validators} from "@angular/forms";
import {MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA, MatLegacyDialog as MatDialog} from "@angular/material/legacy-dialog";
import {NewIncomingTransfersService} from "../../Services/new-incoming-transfers.service";

@Component({
  selector: 'app-reverse-incoming-transfer',
  templateUrl: './reverse-incoming-transfer.component.html',
  styleUrls: ['./reverse-incoming-transfer.component.sass']
})
export class ReverseIncomingTransferComponent implements OnInit {
  public dialogTitle: string = 'ReverseIncomingBankWire'

  public configForm: FormDef = {
    title: '',
    fields: [
      {
        key: 'reason', cssClass: {field: 'col-12'}
      }
    ],
    fieldsDef: {
      defs: {
        reason: {
          ID: 'reason',
          dataType: Types.STRING,
          label: 'reason',
          placeholder: 'reason',
          ui: TtwrInputTextareaComponent,
          validators: [
            {
              name: 'maxlength',
              validator: Validators.maxLength(35),
              message: 'Characters must be less than 35'
            },
            {
              name: 'required',
              validator: Validators.required,
              message: 'This field is required'
            }
          ]
        }
      }
    },
    actionFields: [
      {
        label: 'Submit',
        cssClass: 'btn-info',
        delegateFunction: (obj) => this.newIncomingTransfersService.reverse(this.data, obj.reason).subscribe((res: any) => {
          if (res) {
            if (res.code == 0) {
              this.alertService.success(this.languageService.getLang('Transfer reversed successfully'));
            } else {
              this.alertService.error(res.error);
            }
            this.dialog.closeAll()
          }
        }, (err: any) => {
          this.alertService.error(err.error.message)
        }),
      }
    ]
  }

  constructor(private languageService: LanguageService, private alertService: AlertService,
              private dialog: MatDialog, private newIncomingTransfersService: NewIncomingTransfersService,
              @Inject(MAT_DIALOG_DATA) public data: any
  ) {
  }

  ngOnInit(): void {
  }

}
