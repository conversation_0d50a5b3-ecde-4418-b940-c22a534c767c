import {
  Model, TtwrInputCheckboxComponent,
  TtwrInputComponent,
  TtwrInputDateComponent, TtwrInputLovComponent,
  TtwrInputSelectComponent,
  Types
} from "@ttwr-framework/ngx-main-visuals";
import {LOVBankListComponent} from "../../Banks/LOVBankList/LOVBankList.component";
import {LOVREBBranchesListComponent} from "../../Banks/LOVBankBranchesList/LOVREBBranchesList.component";

export class FilterModel extends Model {
  FromDate!: Date;
  ToDate!: Date;
  sygsRef!: string;
  benefitBankBIC!: string;
  transferType!: string;
  isSuccessful!: number;
  lastState!: string;
  addedAt!: Date;
  insAmount!: number;
  dtAccount!: string;
  dtName!: String;
  ctAccount!: string;
  ctName!: string;
  username!: string;
  branchIdentificationCode!: string;
  payBranchBIC!: string;


  protected static initializeModelDef() {
    return {
      defs: {
        FromDate: {
          dataType: Types.DATETIME,
          label: 'From Date',
          ui: TtwrInputDateComponent,
        },
        addedAt: {
          dataType: Types.DATE,
          label: 'addedAt',
          ui: TtwrInputDateComponent,
        },
        insAmount: {
          dataType: Types.NUMBER,
          label: 'insAmount',
          ui: TtwrInputComponent,
        },
        dtAccount: {
          dataType: Types.STRING,
          label: 'dtAccount',
          ui: TtwrInputComponent,
        },
        dtName: {
          dataType: Types.STRING,
          label: 'dtName',
          ui: TtwrInputComponent,
        },
        ctAccount: {
          dataType: Types.STRING,
          label: 'ctAccount',
          ui: TtwrInputComponent,
        },
        ctName: {
          dataType: Types.STRING,
          label: 'ctName',
          ui: TtwrInputComponent,
        },
        ToDate: {
          dataType: Types.DATETIME,
          label: 'To Date',
          ui: TtwrInputDateComponent,
        },
        sygsRef: {
          dataType: Types.STRING,
          label: 'sygsRef',
          ui: TtwrInputComponent,
        },
        benefitBankBIC: {
          dataType: Types.STRING,
          label: 'benefitBankBIC',
          ui: TtwrInputLovComponent,
          config: {
            multiSelect: false,
            gridComponent: LOVBankListComponent,
            width: '70%',
            searchValueField: 'benefitBankBIC',
          },
        },
        payBankBIC: {
          dataType: Types.STRING,
          label: 'payBankBIC',
          ui: TtwrInputLovComponent,
          config: {
            multiSelect: false,
            gridComponent: LOVBankListComponent,
            width: '70%',
            searchValueField: 'payBankBIC',
          },
        },
        lastState: {
          dataType: Types.STRING,
          label: 'lastState',
          ui: TtwrInputSelectComponent,
          options: [
            {
              value: 'Initial', label: 'Initial'
            },
            {
              value: 'Checked', label: 'Checked'
            },
            {
              value: 'NeedCorrect', label: 'NeedCorrect'
            },
            {
              value: 'TransferringToCB', label: 'TransferringToCB'
            },
            {
              value: 'TransferringToREB', label: 'TransferringToREB'
            },
            {
              value: 'ReverseREBTransfer', label: 'ReverseREBTransfer'
            }
          ]
        },
        transferType: {
          dataType: Types.STRING,
          label: 'transfer type',
          ui: TtwrInputSelectComponent,
          options: [{value: 'REBAddTransfer', label: 'REBAddTransfer'},
            {value: 'CBReverseTransfer', label: 'CBReverseTransfer'},
            {value: 'CBArchiveTransfer', label: 'CBArchiveTransfer'},
          ],
          config: {
            isMultiSelect: false,
          },
        },
        isSuccessful: {
          dataType: Types.NUMBER,
          label: 'isSuccessful',
          ui: TtwrInputCheckboxComponent,
          config: {
            labelPosition: "after",
          },
        },
        username: {
          dataType: Types.STRING,
          label: 'Username',
          ui: TtwrInputComponent,
        },
        branchIdentificationCode: {
          dataType: Types.STRING,
          label: 'branchIdentificationCode',
          ui: TtwrInputLovComponent,
          config: {
            multiSelect: false,
            gridComponent: LOVREBBranchesListComponent,
            width: '70%',
            searchValueField: 'branchIdentificationCode',
          },
        },
        payBranchBIC: {
          dataType: Types.STRING,
          label: 'payBranchBIC',
          ui: TtwrInputLovComponent,
          config: {
            multiSelect: false,
            gridComponent: LOVREBBranchesListComponent,
            width: '70%',
            searchValueField: 'payBranchBIC',
          },
        },
      }
    }
  }

}
