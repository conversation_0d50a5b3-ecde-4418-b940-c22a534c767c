import {Injectable} from '@angular/core';
import {Filter, Model, Sort} from "@ttwr-framework/ngx-main-visuals";
import {Observable} from "rxjs";
import {map} from "rxjs/operators";
import {HttpParams} from "@angular/common/http";
import {SYGSBaseService} from "../../services/SYGSBaseService";

@Injectable()
export class IncomingTransfersModelService extends SYGSBaseService {
  isSuccess: boolean = false;
  payBankBIC!: string;
  sygsRef!: string;
  fromDate!: string | null;
  toDate!: string | null;
  transferType!: string;

  protected baseName = 'IncomingBankWire';

  viewAsString(id: string): Observable<Model> {
    let params = new HttpParams().set('transferId', id)
    return this.http.get<Model>(`/api/` + this.baseName + `/GetTransferById/`, {
      params: params
    }).pipe(map((obj: any) => {
      if (obj) {
        obj['operations']?.forEach((item: any) => {
          item.resultCode = item.result?.code;
          item.resultMessage = item.result?.message;
          item.resultXapiReferenceNumber = item.result?.xapiReferenceNumber;
          item.resultAuthorizeCode = item.result?.authorizeCode;
        })

        return obj;
      }
    }));
  }


  index(
    filter: Filter[] = [],
    sort: Sort[] = [],
    pageNumber = 0,
    pageSize = 5
  ): Observable<Model[]> {
    const params = this.getGridParams(filter, sort, pageNumber, pageSize);
    return this.http.get<Model[]>(`/api/` + this.baseName + `/GetIncomingTransfersReport`, {
      params: params,
    }).pipe(map((res: any) => {
      var newObj: any = {
        Items: res.data.items,
        PageSize: res.data.pageSize,
        TotalCount: res.data.totalCount
      }
      return newObj;
    }))
  }

  exportToExcel(filter: Filter[] = []): Observable<any> {
    let params = new HttpParams()
    filter.forEach((x, i) => {
      params = params.set('filters[' + i + '].attribute', x.attribute);
      params = params.set('filters[' + i + '].operation', x.operation);
      params = params.set('filters[' + i + '].value', x.value);
      x.useOrOperator ? params = params.set('filters[' + i + '].useOrOperator', x.useOrOperator) : null;
    })
    return this.http.get(`/api/` + this.baseName + `/ExportIncomingTransfersReportToExcel`, {
      responseType: 'blob',
      params: params
    })
  }
}
