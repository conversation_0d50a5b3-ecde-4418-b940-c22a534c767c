import {Component, Inject, OnInit} from '@angular/core';
import {UserGroup} from '../../models/user-group.model';
import {UserGroupService} from '../../services/user-group.service';
import {MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA, MatLegacyDialogRef as MatDialogRef} from '@angular/material/legacy-dialog';
import {AlertService, FormDef, Helper, LanguageService} from '@ttwr-framework/ngx-main-visuals';

@Component({
  selector: 'app-user-group-update',
  templateUrl: './user-group-update.component.html',
  styleUrls: ['./user-group-update.component.scss'],
})
export class UserGroupUpdateComponent implements OnInit {
  dialogTitle='User Group Update'
  public config: FormDef = {
    titleHidden:true,
    fields: [{key: 'Name'}, {key: 'Code'}],

    fieldsDef: UserGroup.getModelDef(),
    obj: {},
    actionFields: [
      {
        label: 'Submit',
        delegateFunction: (obj) =>
          this.userGroupService.update(this.config.obj?.['Id'], obj).subscribe(() => {
            this.alertService.success('Success Update');
            this.dialogRef.close(true);
          }),
      },
      {
        label: 'Cancel',
        cssClass: 'btn-danger',
        causeValidate: false,
        delegateFunction: () => this.dialogRef.close(),
      },
    ],
  };

  constructor(
    public dialogRef: MatDialogRef<UserGroupUpdateComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private userGroupService: UserGroupService,
    private alertService: AlertService,
  ) {
  }

  ngOnInit() {
    this.config.obj = Helper.deepCopy(this.data['obj']);
  }
}
