import {Component, OnInit} from '@angular/core';
import {MenuItem} from "../shared/models/MenuItem";
import {Breadcrumb, BreadcrumbService} from "angular-crumbs";

@Component({
  selector: 'app-breadcrumb',
  templateUrl: './breadcrumb.component.html',
  styleUrls: ['./breadcrumb.component.scss']
})
export class BreadcrumbComponent implements OnInit {

  breadcrumbs!: MenuItem[];

  constructor(private breadcrumbService: BreadcrumbService) {
  }

  ngOnInit(): void {
    this.breadcrumbService.breadcrumbChanged.subscribe(crumbs => {
      this.breadcrumbs = crumbs.map(c => this.toPrimeNgMenuItem(c));
    });
  }

  private toPrimeNgMenuItem(crumb: Breadcrumb) {
    return <MenuItem>{label: crumb.displayName, url: `#${crumb.url}`}
  }


}
