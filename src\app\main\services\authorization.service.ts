import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';

import { of, throwError, catchError, map } from 'rxjs';
import { ConfigService } from '../../services/config.service';

@Injectable({ providedIn: 'root' })
export class AuthorizationService {
  constructor(private http: HttpClient, private configService: ConfigService) {
  }

  public getAllPermission(): Promise<{ NAME: any } | any> {
    const token = localStorage.getItem('token');
    return this.http
      .post<{ Name: any }[]>(this.configService.configuration.authorizationUrl, {})
      .pipe(
        map((res) => {

          const permissions = res.map((obj) => {
            return obj.Name;
          });
          return res;
        }),
        catchError((error: HttpErrorResponse) => {
          if (error.status !== 401) {
            return throwError(error);
          }
          return of(false);
        })
      )
      .toPromise();
  }
}
